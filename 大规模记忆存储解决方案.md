# 大规模记忆存储解决方案

## 当前问题分析

目前拾光忆栈应用在存储记忆时面临以下问题：

1. **元数据文件过大**：随着记忆数量增加，`metadata.json`文件变得越来越大，导致：
   - 加载和解析时间长
   - 网络传输效率低
   - 内存占用高
   - 可能超出云存储单文件大小限制

2. **性能下降**：大量记忆数据导致应用性能下降，特别是在搜索、过滤和加载记忆时。

3. **同步问题**：大文件同步更容易出现冲突和失败。

## 解决方案：分片存储架构

为了支持10万条以上的记忆，我们需要实现一个分片存储架构，将记忆数据分散到多个文件中，同时保持高效的索引和查询能力。

### 架构目录层级

新的存储架构采用以下目录结构组织数据：

```
users/
  {userId}/
    metadata.json                    # 主元数据文件（精简版）
    chunks/
      chunk_{chunkId}.json           # 记忆块文件，每块最多1000条记忆
    indexes/
      categories/
        category_{categoryId}.json    # 分类索引文件
      tags/
        tag_{safeTagName}.json       # 标签索引文件
      search_index.json              # 全文搜索索引
    memories/
      {year}/
        {month}/
          {memoryId}.json            # 完整记忆内容文件
    assets/
      images/
        {memoryId}/
          {imageId}.{extension}      # 原始图片
        {memoryId}/thumbnails/
          {imageId}_thumb.{extension} # 图片缩略图
      videos/
        {memoryId}/
          {videoId}.{extension}      # 原始视频
        {memoryId}/thumbnails/
          {videoId}_thumb.jpg         # 视频缩略图
```

这种层级结构有以下优势：
1. **清晰的数据组织**：按照功能和类型分离数据
2. **高效的访问路径**：可以直接访问需要的数据，无需加载全部内容
3. **良好的扩展性**：可以轻松添加新的索引类型或数据类型
4. **优化的存储效率**：相关数据集中存储，减少冗余

### 1. 元数据分离与分片

#### 1.1 主元数据文件精简化

将`metadata.json`精简为只包含必要的全局信息和索引：

```json
{
  "user_id": "user123",
  "version": 42,
  "last_sync": "2023-06-15T10:30:00Z",
  "last_modified_by": "device_abc",
  "memory_count": 105432,
  "chunk_count": 106,
  "categories": [...],
  "memory_index": {
    "recent": [...],  // 仅保留最近的100条
    "favorites": [...],  // 仅保留最近的100条
    "by_category": {
      "category1": { "count": 1250, "file": "indexes/categories/category_1.json" },
      "category2": { "count": 3421, "file": "indexes/categories/category_2.json" }
    },
    "by_tag": {
      "tag1": { "count": 523, "file": "indexes/tags/tag_1.json" },
      "tag2": { "count": 128, "file": "indexes/tags/tag_2.json" }
    }
  },
  "memory_chunks": [
    { "id": "chunk_1", "file": "chunks/chunk_1.json", "count": 1000, "date_range": ["2020-01-01", "2020-03-15"] },
    { "id": "chunk_2", "file": "chunks/chunk_2.json", "count": 1000, "date_range": ["2020-03-16", "2020-06-30"] }
  ],
  "devices": {...}
}
```

#### 1.2 记忆数据分块存储

将记忆元数据分散到多个块文件中：

- 每个块文件最多存储1000条记忆元数据
- 按时间顺序或其他策略进行分块
- 块文件路径：`users/{userId}/chunks/chunk_{chunkId}.json`

```json
// chunk_1.json
{
  "chunk_id": "chunk_1",
  "memory_count": 1000,
  "date_range": ["2020-01-01", "2020-03-15"],
  "memories": {
    "memory_id_1": {
      "id": "memory_id_1",
      "title": "记忆标题",
      "content": "记忆内容摘要...",
      "created_at": "2020-01-05T10:30:00Z",
      "category": "category1",
      "tags": ["tag1", "tag2"],
      "has_images": true,
      "has_videos": false,
      "path": "/memories/2020/01/memory_id_1.json"
    },
    // ... 更多记忆
  }
}
```

#### 1.3 分类和标签索引分离

将分类和标签索引分离到单独的文件中：

- 分类索引：`users/{userId}/indexes/categories/category_{categoryId}.json`
- 标签索引：`users/{userId}/indexes/tags/tag_{tagName}.json`

```json
// category_1.json
{
  "category_id": "1",
  "name": "工作",
  "memory_count": 1250,
  "memories": ["memory_id_1", "memory_id_2", ...]
}

// tag_work.json
{
  "tag": "工作",
  "memory_count": 523,
  "memories": ["memory_id_1", "memory_id_5", ...]
}
```

### 2. 懒加载与缓存策略

#### 2.1 按需加载数据

- 只在需要时加载记忆块和索引文件
- 实现分页加载机制，每次只加载有限数量的记忆

#### 2.2 多级缓存系统

增强现有的缓存系统：

- **内存缓存**：缓存最近访问的记忆和索引
- **IndexedDB缓存**：存储更多的记忆数据，替代localStorage（容量更大）
- **缓存过期策略**：LRU（最近最少使用）策略自动清理缓存

#### 2.3 预加载策略

- 预测用户可能需要的数据并提前加载
- 根据用户行为模式优化预加载策略

### 3. 搜索与检索优化

#### 3.1 全文搜索索引

- 实现倒排索引用于全文搜索
- 将搜索索引分片存储
- 支持模糊搜索和关键词搜索

#### 3.2 高级过滤与排序

- 实现高效的内存过滤算法
- 支持多条件组合过滤
- 优化排序性能

### 4. 同步与冲突解决

#### 4.1 增量同步

- 只同步变更的数据块，而不是整个元数据
- 实现基于版本的增量同步机制

#### 4.2 冲突检测与解决

- 实现更精细的冲突检测机制
- 提供自动和手动冲突解决选项

### 5. 数据压缩与优化

- 在传输和存储前压缩数据
- 移除不必要的冗余信息
- 优化JSON结构以减小体积

## 实现路线图

### 第一阶段：元数据分片

1. 修改元数据结构，支持分块存储
2. 实现记忆数据分块机制
3. 更新记忆保存和加载逻辑

### 第二阶段：索引优化

1. 实现分类和标签索引分离
2. 开发全文搜索索引
3. 优化搜索和过滤算法

#### 全文搜索索引实现

全文搜索索引采用倒排索引结构，存储在 `users/{userId}/indexes/search_index.json` 文件中：

```json
{
  "version": 1,
  "last_updated": "2023-10-15T08:30:00Z",
  "keywords": {
    "工作": {
      "count": 523,
      "memories": ["memory_id_1", "memory_id_5", ...]
    },
    "会议": {
      "count": 128,
      "memories": ["memory_id_2", "memory_id_7", ...]
    },
    // 更多关键词...
  },
  "memory_to_keywords": {
    "memory_id_1": ["工作", "会议", "项目"],
    "memory_id_2": ["会议", "客户"],
    // 更多记忆到关键词的映射...
  }
}
```

搜索索引服务（SearchIndexService）提供以下功能：

1. **关键词提取**：使用基于规则的分词算法，从记忆标题、内容、标签和分类中提取关键词
2. **模糊搜索**：支持部分匹配和近似匹配，对于长度超过3个字符的关键词启用模糊匹配
3. **相关性排序**：精确匹配的关键词得分更高（1分），模糊匹配的得分较低（0.5分）
4. **实时索引更新**：在记忆添加、修改或删除时自动更新索引
5. **索引重建**：提供完整的索引重建功能，可以对所有记忆重新建立索引

### 第三阶段：缓存与性能优化

1. 实现IndexedDB缓存系统
2. 开发懒加载和预加载机制
3. 优化数据压缩和传输

### 第四阶段：同步机制升级

1. 实现增量同步
2. 开发冲突检测和解决机制
3. 优化网络传输效率

## 技术选择

1. **存储技术**：
   - 云存储：继续使用现有的OSS/OBS
   - 本地存储：IndexedDB + 内存缓存

2. **数据处理**：
   - 存储结构：分层级的JSON文件结构，按功能和类型分离
   - 索引：自定义倒排索引实现，支持全文搜索
   - 分词：基于规则的分词算法，支持中文和英文，并使用滑动窗口提取中文短语
   - 相关性计算：基于精确匹配和模糊匹配的加权计算

3. **同步机制**：
   - 基于版本的增量同步
   - 冲突解决策略：最新优先 + 手动合并选项

## 性能预期

- 支持10万+记忆条目
- 元数据加载时间：<2秒
- 记忆搜索响应时间：<500ms
- 同步效率提升：>80%

## 结论

通过实施分片存储架构，拾光忆栈将能够高效地管理10万条以上的记忆，同时保持良好的用户体验和性能。该方案不仅解决了当前的存储瓶颈，还为未来的扩展提供了坚实的基础。
