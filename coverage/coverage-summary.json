{"total": {"lines": {"total": 8072, "covered": 769, "skipped": 0, "pct": 9.52}, "statements": {"total": 8242, "covered": 799, "skipped": 0, "pct": 9.69}, "functions": {"total": 1328, "covered": 202, "skipped": 0, "pct": 15.21}, "branches": {"total": 4204, "covered": 328, "skipped": 0, "pct": 7.8}, "branchesTrue": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/add-memory/AddMemory.js": {"lines": {"total": 15, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 15, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/add-memory/index.js": {"lines": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/application/services/IMemoryStorageService.js": {"lines": {"total": 25, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 25, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 25, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/application/services/ISettingsService.js": {"lines": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/application/services/MemoryStorageService.js": {"lines": {"total": 323, "covered": 203, "skipped": 0, "pct": 62.84}, "functions": {"total": 50, "covered": 41, "skipped": 0, "pct": 82}, "statements": {"total": 339, "covered": 217, "skipped": 0, "pct": 64.01}, "branches": {"total": 123, "covered": 55, "skipped": 0, "pct": 44.71}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/application/services/SettingsService.js": {"lines": {"total": 154, "covered": 11, "skipped": 0, "pct": 7.14}, "functions": {"total": 23, "covered": 2, "skipped": 0, "pct": 8.69}, "statements": {"total": 156, "covered": 11, "skipped": 0, "pct": 7.05}, "branches": {"total": 85, "covered": 1, "skipped": 0, "pct": 1.17}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/background/index.js": {"lines": {"total": 81, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 19, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 81, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 93, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/browse-memories/BrowseMemories.js": {"lines": {"total": 515, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 78, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 526, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 368, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/browse-memories/index.js": {"lines": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/AutoSyncManager.js": {"lines": {"total": 51, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 51, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 22, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/CategoryManager.js": {"lines": {"total": 175, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 22, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 176, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 63, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/CloudStorageSettings.js": {"lines": {"total": 86, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 10, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 86, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 44, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/ConfigurationManager.js": {"lines": {"total": 140, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 23, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 140, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 69, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/DataMigrationPage.js": {"lines": {"total": 23, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 23, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/MemoryEditor.js": {"lines": {"total": 172, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 32, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 179, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 95, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/MigrationToolEntry.js": {"lines": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/StorageMigrationPanel.js": {"lines": {"total": 90, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 22, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 93, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 65, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/SyncSettingsPanel.js": {"lines": {"total": 111, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 25, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 117, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 87, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/SyncStatusBar.js": {"lines": {"total": 79, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 14, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 84, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 48, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/storage/AmazonS3Settings.js": {"lines": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/storage/GoogleDriveSettings.js": {"lines": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/storage/HuaweiObsSettings.js": {"lines": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/storage/MinioSettings.js": {"lines": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/storage/TencentCosSettings.js": {"lines": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/components/storage/index.js": {"lines": {"total": 0, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 0, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/content/index.js": {"lines": {"total": 181, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 17, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 182, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 27, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/domain/entities/Memory.js": {"lines": {"total": 57, "covered": 56, "skipped": 0, "pct": 98.24}, "functions": {"total": 8, "covered": 8, "skipped": 0, "pct": 100}, "statements": {"total": 58, "covered": 57, "skipped": 0, "pct": 98.27}, "branches": {"total": 83, "covered": 78, "skipped": 0, "pct": 93.97}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/feedback/Feedback.js": {"lines": {"total": 53, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 13, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 53, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 33, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/feedback/index.js": {"lines": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/adapters/AdapterFactory.js": {"lines": {"total": 57, "covered": 55, "skipped": 0, "pct": 96.49}, "functions": {"total": 20, "covered": 20, "skipped": 0, "pct": 100}, "statements": {"total": 64, "covered": 62, "skipped": 0, "pct": 96.87}, "branches": {"total": 15, "covered": 14, "skipped": 0, "pct": 93.33}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/adapters/ChromeRuntimeAdapter.js": {"lines": {"total": 100, "covered": 74, "skipped": 0, "pct": 74}, "functions": {"total": 39, "covered": 30, "skipped": 0, "pct": 76.92}, "statements": {"total": 100, "covered": 74, "skipped": 0, "pct": 74}, "branches": {"total": 39, "covered": 28, "skipped": 0, "pct": 71.79}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/adapters/ChromeStorageAdapter.js": {"lines": {"total": 77, "covered": 66, "skipped": 0, "pct": 85.71}, "functions": {"total": 27, "covered": 26, "skipped": 0, "pct": 96.29}, "statements": {"total": 78, "covered": 67, "skipped": 0, "pct": 85.89}, "branches": {"total": 35, "covered": 27, "skipped": 0, "pct": 77.14}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/adapters/ChromeTabsAdapter.js": {"lines": {"total": 109, "covered": 3, "skipped": 0, "pct": 2.75}, "functions": {"total": 45, "covered": 2, "skipped": 0, "pct": 4.44}, "statements": {"total": 109, "covered": 3, "skipped": 0, "pct": 2.75}, "branches": {"total": 46, "covered": 4, "skipped": 0, "pct": 8.69}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/adapters/IStorageAdapter.js": {"lines": {"total": 7, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 7, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 7, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/adapters/index.js": {"lines": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/cache/ICacheService.js": {"lines": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/cache/MemoryCacheService.js": {"lines": {"total": 109, "covered": 95, "skipped": 0, "pct": 87.15}, "functions": {"total": 23, "covered": 23, "skipped": 0, "pct": 100}, "statements": {"total": 110, "covered": 96, "skipped": 0, "pct": 87.27}, "branches": {"total": 54, "covered": 44, "skipped": 0, "pct": 81.48}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/di/DIContainer.js": {"lines": {"total": 112, "covered": 21, "skipped": 0, "pct": 18.75}, "functions": {"total": 18, "covered": 5, "skipped": 0, "pct": 27.77}, "statements": {"total": 112, "covered": 21, "skipped": 0, "pct": 18.75}, "branches": {"total": 73, "covered": 8, "skipped": 0, "pct": 10.95}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/di/ServiceFactory.js": {"lines": {"total": 120, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 43, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 121, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 34, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/di/ServiceRegistry.js": {"lines": {"total": 123, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 60, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 127, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 20, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/di/ServiceTypes.js": {"lines": {"total": 11, "covered": 3, "skipped": 0, "pct": 27.27}, "functions": {"total": 6, "covered": 1, "skipped": 0, "pct": 16.66}, "statements": {"total": 12, "covered": 3, "skipped": 0, "pct": 25}, "branches": {"total": 10, "covered": 2, "skipped": 0, "pct": 20}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/locks/DistributedLockService.js": {"lines": {"total": 138, "covered": 112, "skipped": 0, "pct": 81.15}, "functions": {"total": 23, "covered": 22, "skipped": 0, "pct": 95.65}, "statements": {"total": 141, "covered": 115, "skipped": 0, "pct": 81.56}, "branches": {"total": 54, "covered": 44, "skipped": 0, "pct": 81.48}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/locks/ILockService.js": {"lines": {"total": 14, "covered": 4, "skipped": 0, "pct": 28.57}, "functions": {"total": 11, "covered": 1, "skipped": 0, "pct": 9.09}, "statements": {"total": 14, "covered": 4, "skipped": 0, "pct": 28.57}, "branches": {"total": 2, "covered": 1, "skipped": 0, "pct": 50}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/repositories/ChromeStorageRepository.js": {"lines": {"total": 80, "covered": 66, "skipped": 0, "pct": 82.5}, "functions": {"total": 21, "covered": 21, "skipped": 0, "pct": 100}, "statements": {"total": 83, "covered": 69, "skipped": 0, "pct": 83.13}, "branches": {"total": 27, "covered": 22, "skipped": 0, "pct": 81.48}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/infrastructure/repositories/IStorageRepository.js": {"lines": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/main/Main.js": {"lines": {"total": 51, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 51, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 25, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/main/index.js": {"lines": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/migration/MigrationPage.js": {"lines": {"total": 126, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 20, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 126, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 68, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/migration/RebuildSearchIndexPage.js": {"lines": {"total": 42, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 42, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/migration/index.js": {"lines": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/migration/rebuild-search-index.js": {"lines": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/options/Options.js": {"lines": {"total": 254, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 40, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 255, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 60, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/options/index.js": {"lines": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/popup/Popup.js": {"lines": {"total": 25, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 10, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 25, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/popup/index.js": {"lines": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/CloudStorageService.js": {"lines": {"total": 96, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 96, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 54, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/ConflictResolutionService.js": {"lines": {"total": 74, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 13, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 77, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 89, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/EncryptionService.js": {"lines": {"total": 27, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 27, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/IncrementalSyncService.js": {"lines": {"total": 226, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 20, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 228, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 160, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/IndexedDBCacheService.js": {"lines": {"total": 235, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 43, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 237, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 57, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/LocalCacheService.js": {"lines": {"total": 81, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 85, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 22, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/OssStorageService.js": {"lines": {"total": 1367, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 94, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 1395, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 795, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/SearchIndexService.js": {"lines": {"total": 154, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 19, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 160, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 70, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/StorageMigrationService.js": {"lines": {"total": 318, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 17, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 326, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 185, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/StorageService.js": {"lines": {"total": 378, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 59, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 396, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 215, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/SyncOptimizationService.js": {"lines": {"total": 114, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 121, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 50, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/index.js": {"lines": {"total": 0, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 0, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/storage/HuaweiObsProvider.js": {"lines": {"total": 154, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 30, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 156, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 84, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/storage/IStorageProvider.js": {"lines": {"total": 11, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 11, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 11, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/storage/MinioProvider.js": {"lines": {"total": 199, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 17, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 205, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 131, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/storage/StorageFactory.js": {"lines": {"total": 14, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 14, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/storage/StorageManager.js": {"lines": {"total": 30, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 30, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 21, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/storage/StorageTypes.js": {"lines": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/services/storage/index.js": {"lines": {"total": 0, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 0, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/settings/CacheSettingsPage.js": {"lines": {"total": 81, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 17, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 84, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 46, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/settings/cache-settings.js": {"lines": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/settings/sync-settings.js": {"lines": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/utils/AwsSignatureV4.js": {"lines": {"total": 108, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 22, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 109, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 20, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/utils/ConfigEncryption.js": {"lines": {"total": 79, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 82, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 50, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/utils/ConfigurationExportImport.js": {"lines": {"total": 44, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 44, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 46, "covered": 0, "skipped": 0, "pct": 0}}, "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/src/utils/MigrationTool.js": {"lines": {"total": 271, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 20, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 273, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 169, "covered": 0, "skipped": 0, "pct": 0}}}