
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for infrastructure/di</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> infrastructure/di</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.45% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>24/372</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.29% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>10/137</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">4.72% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>6/127</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.55% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>24/366</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="DIContainer.js"><a href="DIContainer.js.html">DIContainer.js</a></td>
	<td data-value="18.75" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 18%"></div><div class="cover-empty" style="width: 82%"></div></div>
	</td>
	<td data-value="18.75" class="pct low">18.75%</td>
	<td data-value="112" class="abs low">21/112</td>
	<td data-value="10.95" class="pct low">10.95%</td>
	<td data-value="73" class="abs low">8/73</td>
	<td data-value="27.77" class="pct low">27.77%</td>
	<td data-value="18" class="abs low">5/18</td>
	<td data-value="18.75" class="pct low">18.75%</td>
	<td data-value="112" class="abs low">21/112</td>
	</tr>

<tr>
	<td class="file low" data-value="ServiceFactory.js"><a href="ServiceFactory.js.html">ServiceFactory.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="121" class="abs low">0/121</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="34" class="abs low">0/34</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="43" class="abs low">0/43</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="120" class="abs low">0/120</td>
	</tr>

<tr>
	<td class="file low" data-value="ServiceRegistry.js"><a href="ServiceRegistry.js.html">ServiceRegistry.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="127" class="abs low">0/127</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="20" class="abs low">0/20</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="60" class="abs low">0/60</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="123" class="abs low">0/123</td>
	</tr>

<tr>
	<td class="file low" data-value="ServiceTypes.js"><a href="ServiceTypes.js.html">ServiceTypes.js</a></td>
	<td data-value="25" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 25%"></div><div class="cover-empty" style="width: 75%"></div></div>
	</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="12" class="abs low">3/12</td>
	<td data-value="20" class="pct low">20%</td>
	<td data-value="10" class="abs low">2/10</td>
	<td data-value="16.66" class="pct low">16.66%</td>
	<td data-value="6" class="abs low">1/6</td>
	<td data-value="27.27" class="pct low">27.27%</td>
	<td data-value="11" class="abs low">3/11</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-17T11:12:37.318Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    