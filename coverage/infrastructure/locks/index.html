
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for infrastructure/locks</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> infrastructure/locks</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">76.77% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>119/155</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">80.35% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>45/56</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">67.64% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>23/34</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">76.31% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>116/152</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="DistributedLockService.js"><a href="DistributedLockService.js.html">DistributedLockService.js</a></td>
	<td data-value="81.56" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 81%"></div><div class="cover-empty" style="width: 19%"></div></div>
	</td>
	<td data-value="81.56" class="pct high">81.56%</td>
	<td data-value="141" class="abs high">115/141</td>
	<td data-value="81.48" class="pct high">81.48%</td>
	<td data-value="54" class="abs high">44/54</td>
	<td data-value="95.65" class="pct high">95.65%</td>
	<td data-value="23" class="abs high">22/23</td>
	<td data-value="81.15" class="pct high">81.15%</td>
	<td data-value="138" class="abs high">112/138</td>
	</tr>

<tr>
	<td class="file low" data-value="ILockService.js"><a href="ILockService.js.html">ILockService.js</a></td>
	<td data-value="28.57" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 28%"></div><div class="cover-empty" style="width: 72%"></div></div>
	</td>
	<td data-value="28.57" class="pct low">28.57%</td>
	<td data-value="14" class="abs low">4/14</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="9.09" class="pct low">9.09%</td>
	<td data-value="11" class="abs low">1/11</td>
	<td data-value="28.57" class="pct low">28.57%</td>
	<td data-value="14" class="abs low">4/14</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-17T11:12:37.318Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    