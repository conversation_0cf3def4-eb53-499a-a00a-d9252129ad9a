
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for application/services</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> application/services</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">42.85% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>228/532</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">25.92% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>56/216</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">39.09% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>43/110</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">41.63% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>214/514</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="IMemoryStorageService.js"><a href="IMemoryStorageService.js.html">IMemoryStorageService.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="25" class="abs low">0/25</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="25" class="abs low">0/25</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="25" class="abs low">0/25</td>
	</tr>

<tr>
	<td class="file low" data-value="ISettingsService.js"><a href="ISettingsService.js.html">ISettingsService.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	</tr>

<tr>
	<td class="file medium" data-value="MemoryStorageService.js"><a href="MemoryStorageService.js.html">MemoryStorageService.js</a></td>
	<td data-value="64.01" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 64%"></div><div class="cover-empty" style="width: 36%"></div></div>
	</td>
	<td data-value="64.01" class="pct medium">64.01%</td>
	<td data-value="339" class="abs medium">217/339</td>
	<td data-value="44.71" class="pct low">44.71%</td>
	<td data-value="123" class="abs low">55/123</td>
	<td data-value="82" class="pct high">82%</td>
	<td data-value="50" class="abs high">41/50</td>
	<td data-value="62.84" class="pct medium">62.84%</td>
	<td data-value="323" class="abs medium">203/323</td>
	</tr>

<tr>
	<td class="file low" data-value="SettingsService.js"><a href="SettingsService.js.html">SettingsService.js</a></td>
	<td data-value="7.05" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7.05" class="pct low">7.05%</td>
	<td data-value="156" class="abs low">11/156</td>
	<td data-value="1.17" class="pct low">1.17%</td>
	<td data-value="85" class="abs low">1/85</td>
	<td data-value="8.69" class="pct low">8.69%</td>
	<td data-value="23" class="abs low">2/23</td>
	<td data-value="7.14" class="pct low">7.14%</td>
	<td data-value="154" class="abs low">11/154</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-17T11:12:37.318Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    