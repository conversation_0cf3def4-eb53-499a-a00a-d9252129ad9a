
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for infrastructure/adapters</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> infrastructure/adapters</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">56.9% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>206/362</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">54.07% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>73/135</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">54.92% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>78/142</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">55.93% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>198/354</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="AdapterFactory.js"><a href="AdapterFactory.js.html">AdapterFactory.js</a></td>
	<td data-value="96.87" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 96%"></div><div class="cover-empty" style="width: 4%"></div></div>
	</td>
	<td data-value="96.87" class="pct high">96.87%</td>
	<td data-value="64" class="abs high">62/64</td>
	<td data-value="93.33" class="pct high">93.33%</td>
	<td data-value="15" class="abs high">14/15</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="20" class="abs high">20/20</td>
	<td data-value="96.49" class="pct high">96.49%</td>
	<td data-value="57" class="abs high">55/57</td>
	</tr>

<tr>
	<td class="file medium" data-value="ChromeRuntimeAdapter.js"><a href="ChromeRuntimeAdapter.js.html">ChromeRuntimeAdapter.js</a></td>
	<td data-value="74" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 74%"></div><div class="cover-empty" style="width: 26%"></div></div>
	</td>
	<td data-value="74" class="pct medium">74%</td>
	<td data-value="100" class="abs medium">74/100</td>
	<td data-value="71.79" class="pct medium">71.79%</td>
	<td data-value="39" class="abs medium">28/39</td>
	<td data-value="76.92" class="pct medium">76.92%</td>
	<td data-value="39" class="abs medium">30/39</td>
	<td data-value="74" class="pct medium">74%</td>
	<td data-value="100" class="abs medium">74/100</td>
	</tr>

<tr>
	<td class="file high" data-value="ChromeStorageAdapter.js"><a href="ChromeStorageAdapter.js.html">ChromeStorageAdapter.js</a></td>
	<td data-value="85.89" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 85%"></div><div class="cover-empty" style="width: 15%"></div></div>
	</td>
	<td data-value="85.89" class="pct high">85.89%</td>
	<td data-value="78" class="abs high">67/78</td>
	<td data-value="77.14" class="pct medium">77.14%</td>
	<td data-value="35" class="abs medium">27/35</td>
	<td data-value="96.29" class="pct high">96.29%</td>
	<td data-value="27" class="abs high">26/27</td>
	<td data-value="85.71" class="pct high">85.71%</td>
	<td data-value="77" class="abs high">66/77</td>
	</tr>

<tr>
	<td class="file low" data-value="ChromeTabsAdapter.js"><a href="ChromeTabsAdapter.js.html">ChromeTabsAdapter.js</a></td>
	<td data-value="2.75" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 2%"></div><div class="cover-empty" style="width: 98%"></div></div>
	</td>
	<td data-value="2.75" class="pct low">2.75%</td>
	<td data-value="109" class="abs low">3/109</td>
	<td data-value="8.69" class="pct low">8.69%</td>
	<td data-value="46" class="abs low">4/46</td>
	<td data-value="4.44" class="pct low">4.44%</td>
	<td data-value="45" class="abs low">2/45</td>
	<td data-value="2.75" class="pct low">2.75%</td>
	<td data-value="109" class="abs low">3/109</td>
	</tr>

<tr>
	<td class="file low" data-value="IStorageAdapter.js"><a href="IStorageAdapter.js.html">IStorageAdapter.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	</tr>

<tr>
	<td class="file low" data-value="index.js"><a href="index.js.html">index.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-17T11:12:37.179Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    