# 拾光忆栈 Chrome 扩展

一个使用 React 和 Ant Design 构建的用于保存和管理记忆的 Chrome 扩展。

## 功能

- 从任何网页保存文本片段
- 组织和搜索您的记忆
- 可自定义设置
- 支持暗色模式
- 多设备同步
- 多语言支持

## 开发

### 前提条件

- Node.js 和 npm

### 设置

1. 克隆仓库
2. 安装依赖项：
   ```
   npm install
   ```
3. 启动开发服务器：
   ```
   npm start
   ```
4. 在 Chrome 中加载扩展：
   - 打开 Chrome 并访问 `chrome://extensions`
   - 启用“开发者模式”
   - 点击“加载已解压的扩展程序”并选择 `dist` 文件夹

### 生产构建

```
npm run build
```

## 许可证

MIT
