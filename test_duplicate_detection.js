/**
 * 测试重复文件检测功能
 */

// 模拟测试重复文件检测
async function testDuplicateDetection() {
  console.log('开始测试重复文件检测功能...');
  
  try {
    // 导入必要的模块
    const storageMigrationService = require('./src/services/StorageMigrationService').default;
    
    // 测试_normalizeETag方法
    const testETag1 = '"abc123def456"';
    const testETag2 = 'abc123def456';
    const testETag3 = '"ABC123DEF456"';
    
    console.log('测试ETag标准化:');
    console.log(`原始: ${testETag1} -> 标准化: ${storageMigrationService._normalizeETag(testETag1)}`);
    console.log(`原始: ${testETag2} -> 标准化: ${storageMigrationService._normalizeETag(testETag2)}`);
    console.log(`原始: ${testETag3} -> 标准化: ${storageMigrationService._normalizeETag(testETag3)}`);
    
    // 验证相同的ETag应该产生相同的标准化结果
    const normalized1 = storageMigrationService._normalizeETag(testETag1);
    const normalized2 = storageMigrationService._normalizeETag(testETag2);
    const normalized3 = storageMigrationService._normalizeETag(testETag3);
    
    if (normalized1 === normalized2 && normalized2 === normalized3) {
      console.log('✅ ETag标准化测试通过');
    } else {
      console.log('❌ ETag标准化测试失败');
    }
    
    console.log('重复文件检测功能测试完成');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testDuplicateDetection();
}

module.exports = { testDuplicateDetection };
