const path = require('path');
const CopyPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');

module.exports = {
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename]
    }
  },
  entry: {
    popup: './src/popup/index.js',
    options: './src/options/index.js',
    background: './src/background/index.js',
    content: './src/content/index.js',
    'add-memory': './src/add-memory/index.js',
    'browse-memories': './src/browse-memories/index.js',
    'migration': './src/migration/index.js',
    'rebuild-search-index': './src/migration/rebuild-search-index.js',
    'cache-settings': './src/settings/cache-settings.js',
    'sync-settings': './src/settings/sync-settings.js',
    'main': './src/main/index.js',
    'feedback': './src/feedback/index.js',
  },
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].js',
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react'],
            cacheDirectory: true
          },
        },
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
      },
    ],
  },
  optimization: {
    splitChunks: {
      chunks: 'all',
      maxInitialRequests: Infinity,
      minSize: 0,
      cacheGroups: {
        vendors: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          priority: -10
        },
        antd: {
          test: /[\\/]node_modules[\\/]antd[\\/]/,
          name: 'antd',
          priority: 20
        },
        react: {
          test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
          name: 'react',
          priority: 30
        },
        aws: {
          test: /[\\/]node_modules[\\/]@aws-sdk[\\/]/,
          name: 'aws-sdk',
          priority: 20
        }
      }
    }
  },
  plugins: [
    new CopyPlugin({
      patterns: [
        { from: 'public' },
        { from: 'manifest.json' },
      ],
    }),
    new HtmlWebpackPlugin({
      template: './src/popup/popup.html',
      filename: 'popup.html',
      chunks: ['popup'],
    }),
    new HtmlWebpackPlugin({
      template: './src/options/options.html',
      filename: 'options.html',
      chunks: ['options'],
    }),
    new HtmlWebpackPlugin({
      template: './src/add-memory/add-memory.html',
      filename: 'add-memory.html',
      chunks: ['add-memory'],
    }),
    new HtmlWebpackPlugin({
      template: './src/browse-memories/browse-memories.html',
      filename: 'browse-memories.html',
      chunks: ['browse-memories'],
    }),
    new HtmlWebpackPlugin({
      template: './src/migration/migration.html',
      filename: 'migration.html',
      chunks: ['migration'],
    }),
    new HtmlWebpackPlugin({
      template: './src/migration/rebuild-search-index.html',
      filename: 'rebuild-search-index.html',
      chunks: ['rebuild-search-index'],
    }),
    new HtmlWebpackPlugin({
      template: './src/settings/cache-settings.html',
      filename: 'cache-settings.html',
      chunks: ['cache-settings'],
    }),
    new HtmlWebpackPlugin({
      template: './src/settings/sync-settings.html',
      filename: 'sync-settings.html',
      chunks: ['sync-settings'],
    }),
    new HtmlWebpackPlugin({
      template: './src/main/main.html',
      filename: 'main.html',
      chunks: ['main'],
    }),
    new HtmlWebpackPlugin({
      template: './src/feedback/feedback.html',
      filename: 'feedback.html',
      chunks: ['feedback'],
    }),
  ],
};
