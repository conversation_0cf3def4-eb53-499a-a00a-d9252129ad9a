body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
}

/* Global styles to match settings pages */
.ant-typography {
  font-size: 14px;
}

.ant-form-item-label > label {
  font-size: 14px;
}

.ant-input, .ant-select, .ant-btn {
  font-size: 14px;
}

/* Import Ant Design styles */
@import 'antd/dist/reset.css';

.feedback-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  font-size: 14px;
}

.feedback-form {
  margin-top: 24px;
}

/* Increase font size for form labels */
.feedback-form .ant-form-item-label > label {
  font-size: 14px;
  font-weight: 500;
}

/* Increase font size for form inputs */
.feedback-form .ant-input,
.feedback-form .ant-input-affix-wrapper,
.feedback-form .ant-select-selector,
.feedback-form .ant-btn {
  font-size: 14px;
}

/* Increase font size for TextArea */
.feedback-form .ant-input-textarea textarea {
  font-size: 14px;
}

.feedback-success {
  text-align: center;
  padding: 40px 0;
}

.feedback-success-icon {
  font-size: 72px;
  color: #52c41a;
}

.feedback-type-tag {
  margin-right: 8px;
  cursor: pointer;
  font-size: 14px;
  padding: 6px 12px;
}

.feedback-type-tag.selected {
  background-color: #1890ff;
  color: white;
}

.feedback-submit-btn {
  margin-top: 16px;
}

.feedback-info {
  margin-top: 24px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.feedback-info h3 {
  margin-top: 0;
}

.feedback-info ul {
  padding-left: 20px;
}

.feedback-info li {
  margin-bottom: 8px;
}

.feedback-screenshot-uploader {
  margin-top: 16px;
}

.feedback-screenshot-tip {
  color: #888;
  font-size: 12px;
  margin-top: 8px;
}

.feedback-contact {
  margin-top: 16px;
}

.feedback-contact-optional {
  color: #888;
  font-size: 13px;
  margin-left: 8px;
}

.feedback-browser-info {
  margin-top: 16px;
  font-size: 13px;
  color: #888;
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
}

.feedback-browser-info-item {
  margin-bottom: 6px;
}

.feedback-divider {
  margin: 24px 0;
}

.feedback-alternative {
  margin-top: 24px;
  text-align: center;
}

.feedback-alternative .ant-typography {
  font-size: 14px;
  margin-bottom: 16px;
}

.feedback-alternative .ant-btn {
  font-size: 14px;
  height: auto;
  padding: 6px 15px;
}

.feedback-github-link {
  display: inline-block;
  margin-top: 8px;
}
