import React, { useState, useEffect } from 'react';
import { Button, Card, Progress, Alert, List, Typography, Spin, Modal, message, Divider, Empty, Space } from 'antd';
import { CloudUploadOutlined, HistoryOutlined, CheckCircleOutlined, ExclamationCircleOutlined, SearchOutlined, ToolOutlined, DatabaseOutlined } from '@ant-design/icons';
import migrationTool from '../utils/MigrationTool';
import ossStorageService from '../services/OssStorageService';

const { Title, Text, Paragraph } = Typography;

/**
 * 迁移页面组件
 * 用于将旧的存储结构迁移到新的分块存储结构
 */
const MigrationPage = () => {
  const [needsMigration, setNeedsMigration] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  const [isMigrating, setIsMigrating] = useState(false);
  const [migrationResult, setMigrationResult] = useState(null);
  const [backups, setBackups] = useState([]);
  const [isLoadingBackups, setIsLoadingBackups] = useState(false);
  const [memoryCount, setMemoryCount] = useState(0);
  const [isRecoveringMetadata, setIsRecoveringMetadata] = useState(false);
  const [recoveryResult, setRecoveryResult] = useState(null);
  const [isExploringOss, setIsExploringOss] = useState(false);
  const [exploreResult, setExploreResult] = useState(null);

  // 检查是否需要迁移
  useEffect(() => {
    const checkMigration = async () => {
      setIsChecking(true);
      try {
        // 检查是否需要迁移
        const needsMigration = await migrationTool.checkNeedsMigration();
        setNeedsMigration(needsMigration);

        // 获取记忆数量
        const metadata = await ossStorageService.getUserMetadata();
        setMemoryCount(metadata.memory_count || 0);

        // 加载备份列表
        loadBackups();
      } catch (error) {
        console.error('检查迁移状态失败:', error);
        message.error('检查迁移状态失败: ' + error.message);
      } finally {
        setIsChecking(false);
      }
    };

    checkMigration();
  }, []);

  // 加载备份列表
  const loadBackups = async () => {
    setIsLoadingBackups(true);
    try {
      const backupList = await migrationTool.getBackups();
      setBackups(backupList);
    } catch (error) {
      console.error('加载备份列表失败:', error);
      message.error('加载备份列表失败: ' + error.message);
    } finally {
      setIsLoadingBackups(false);
    }
  };

  // 执行迁移
  const handleMigrate = async () => {
    Modal.confirm({
      title: '确认迁移',
      icon: <ExclamationCircleOutlined />,
      content: '迁移过程将会将您的记忆数据转换为新的分块存储结构，以支持大规模记忆存储。迁移过程中会自动备份您的数据。确定要继续吗？',
      onOk: async () => {
        setIsMigrating(true);
        setMigrationResult(null);

        try {
          const result = await migrationTool.migrate();
          setMigrationResult(result);

          if (result.success) {
            message.success('数据迁移成功');
            setNeedsMigration(false);
            loadBackups();
          } else {
            message.error('数据迁移失败: ' + result.message);
          }
        } catch (error) {
          console.error('数据迁移失败:', error);
          setMigrationResult({
            success: false,
            message: `数据迁移失败: ${error.message}`,
            error
          });
          message.error('数据迁移失败: ' + error.message);
        } finally {
          setIsMigrating(false);
        }
      }
    });
  };

  // 从备份恢复
  const handleRestore = (backupKey) => {
    Modal.confirm({
      title: '确认恢复',
      icon: <ExclamationCircleOutlined />,
      content: '从备份恢复将会覆盖当前的数据。确定要继续吗？',
      onOk: async () => {
        try {
          const result = await migrationTool.restoreFromBackup(backupKey);

          if (result.success) {
            message.success('数据恢复成功');
            // 重新检查迁移状态
            const needsMigration = await migrationTool.checkNeedsMigration();
            setNeedsMigration(needsMigration);
          } else {
            message.error('数据恢复失败: ' + result.message);
          }
        } catch (error) {
          console.error('数据恢复失败:', error);
          message.error('数据恢复失败: ' + error.message);
        }
      }
    });
  };

  // 删除备份
  const handleDeleteBackup = (backupKey) => {
    Modal.confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '删除备份后将无法恢复。确定要继续吗？',
      onOk: async () => {
        try {
          const result = await migrationTool.deleteBackup(backupKey);

          if (result.success) {
            message.success('备份删除成功');
            loadBackups();
          } else {
            message.error('备份删除失败: ' + result.message);
          }
        } catch (error) {
          console.error('备份删除失败:', error);
          message.error('备份删除失败: ' + error.message);
        }
      }
    });
  };

  // 探索 OSS 目录结构
  const handleExploreOss = async () => {
    setIsExploringOss(true);
    setExploreResult(null);

    try {
      console.log('开始探索 OSS 目录结构...');

      // 输出 OssStorageService 的状态
      console.log('OssStorageService 状态:', {
        isInitialized: ossStorageService.isInitialized,
        userId: ossStorageService.userId,
        bucketName: ossStorageService.bucketName,
        baseUrl: ossStorageService.baseUrl,
        providerName: ossStorageService.providerName
      });

      // 如果未初始化，先初始化
      if (!ossStorageService.isInitialized) {
        console.log('初始化 OssStorageService...');
        await ossStorageService.initialize();
      }

      const userId = ossStorageService.userId;
      if (!userId) {
        throw new Error('无法获取用户ID，请先登录或初始化存储服务');
      }

      // 探索用户目录
      const userDirResult = await migrationTool.exploreOssStructure(`users/${userId}/`, 2);

      // 探索根目录
      const rootDirResult = await migrationTool.exploreOssStructure('', 1);

      const result = {
        userDir: userDirResult,
        rootDir: rootDirResult
      };

      setExploreResult(result);
      message.success('探索 OSS 目录结构成功');
    } catch (error) {
      console.error('探索 OSS 目录结构失败:', error);
      setExploreResult({
        success: false,
        message: `探索失败: ${error.message}`,
        error
      });
      message.error('探索 OSS 目录结构失败: ' + error.message);
    } finally {
      setIsExploringOss(false);
    }
  };

  // 从 OSS 恢复 metadata.json
  const handleRecoverMetadata = () => {
    Modal.confirm({
      title: '确认恢复元数据',
      icon: <ExclamationCircleOutlined />,
      content: '此操作将从 OSS 中的记忆文件重建 metadata.json 文件。如果当前已有元数据，将会备份并替换它。确定要继续吗？',
      onOk: async () => {
        setIsRecoveringMetadata(true);
        setRecoveryResult(null);

        try {
          console.log('开始从 OSS 恢复元数据...');

          // 输出 OssStorageService 的状态
          console.log('OssStorageService 状态:', {
            isInitialized: ossStorageService.isInitialized,
            userId: ossStorageService.userId,
            bucketName: ossStorageService.bucketName,
            baseUrl: ossStorageService.baseUrl,
            providerName: ossStorageService.providerName
          });

          // 如果未初始化，先初始化
          if (!ossStorageService.isInitialized) {
            console.log('初始化 OssStorageService...');
            await ossStorageService.initialize();
          }

          const result = await migrationTool.recoverMetadataFromOSS();
          console.log('恢复结果:', result);
          setRecoveryResult(result);

          if (result.success) {
            message.success('元数据恢复成功');
            // 重新检查迁移状态
            const needsMigration = await migrationTool.checkNeedsMigration();
            setNeedsMigration(needsMigration);

            // 获取记忆数量
            const metadata = await ossStorageService.getUserMetadata();
            setMemoryCount(metadata.memory_count || 0);
          } else {
            message.error('元数据恢复失败: ' + result.message);
          }
        } catch (error) {
          console.error('元数据恢复失败:', error);
          setRecoveryResult({
            success: false,
            message: `元数据恢复失败: ${error.message}`,
            error
          });
          message.error('元数据恢复失败: ' + error.message);
        } finally {
          setIsRecoveringMetadata(false);
        }
      }
    });
  };

  if (isChecking) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: '20px' }}>正在检查迁移状态...</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px' }}>
      <Title level={2}>记忆数据迁移</Title>

      <Card style={{ marginBottom: '20px' }}>
        <Title level={4}>存储结构升级</Title>
        <Paragraph>
          为了支持大规模记忆存储（10万条以上），我们需要将您的记忆数据迁移到新的分块存储结构。
          迁移过程会自动备份您的数据，确保数据安全。
        </Paragraph>

        {needsMigration ? (
          <>
            <Alert
              message="需要迁移"
              description={`检测到您有 ${memoryCount} 条记忆数据需要迁移到新的存储结构。`}
              type="warning"
              showIcon
              style={{ marginBottom: '20px' }}
            />

            <Button
              type="primary"
              icon={<CloudUploadOutlined />}
              size="large"
              onClick={handleMigrate}
              loading={isMigrating}
              disabled={isMigrating}
            >
              {isMigrating ? '正在迁移...' : '开始迁移'}
            </Button>

            {isMigrating && (
              <div style={{ marginTop: '20px' }}>
                <Progress percent={50} status="active" />
                <p>正在迁移数据，请勿关闭页面...</p>
              </div>
            )}

            {migrationResult && (
              <Alert
                message={migrationResult.success ? '迁移成功' : '迁移失败'}
                description={migrationResult.message}
                type={migrationResult.success ? 'success' : 'error'}
                showIcon
                style={{ marginTop: '20px' }}
              />
            )}
          </>
        ) : (
          <Alert
            message="无需迁移"
            description="您的记忆数据已经使用最新的存储结构，无需迁移。"
            type="success"
            showIcon
          />
        )}
      </Card>

      <Card style={{ marginBottom: '20px' }}>
        <Title level={4}>元数据恢复工具</Title>
        <Paragraph>
          如果您的 metadata.json 文件损坏或丢失，导致记忆列表无法加载，可以使用此工具从 OSS 中的记忆文件重建元数据。
        </Paragraph>

        <Space>
          <Button
            type="primary"
            icon={<DatabaseOutlined />}
            onClick={handleRecoverMetadata}
            loading={isRecoveringMetadata}
            disabled={isRecoveringMetadata || isExploringOss}
          >
            {isRecoveringMetadata ? '正在恢复元数据...' : '从 OSS 恢复元数据'}
          </Button>

          <Button
            icon={<SearchOutlined />}
            onClick={handleExploreOss}
            loading={isExploringOss}
            disabled={isExploringOss || isRecoveringMetadata}
          >
            {isExploringOss ? '正在探索 OSS 目录...' : '探索 OSS 目录结构'}
          </Button>
        </Space>

        {isRecoveringMetadata && (
          <div style={{ marginTop: '20px' }}>
            <Progress percent={50} status="active" strokeColor={{ from: '#108ee9', to: '#87d068' }} />
            <p>正在从 OSS 恢复元数据，请勿关闭页面...</p>
            <p style={{ fontSize: '12px', color: '#888' }}>该过程可能需要几分钟，取决于记忆数量和网络状况</p>
          </div>
        )}

        {isExploringOss && (
          <div style={{ marginTop: '20px' }}>
            <Progress percent={50} status="active" strokeColor={{ from: '#108ee9', to: '#87d068' }} />
            <p>正在探索 OSS 目录结构，请稍候...</p>
          </div>
        )}

        {recoveryResult && (
          <div style={{ marginTop: '20px' }}>
            <Alert
              message={recoveryResult.success ? '恢复成功' : '恢复失败'}
              description={
                <div>
                  <p>{recoveryResult.message}</p>
                  {recoveryResult.errorCount > 0 && recoveryResult.errorDetails && recoveryResult.errorDetails.length > 0 && (
                    <div style={{ marginTop: '10px' }}>
                      <p>错误详情（最多显示20条）：</p>
                      <ul style={{ maxHeight: '200px', overflowY: 'auto', backgroundColor: '#f5f5f5', padding: '10px' }}>
                        {recoveryResult.errorDetails.map((error, index) => (
                          <li key={index} style={{ marginBottom: '5px', color: '#ff4d4f' }}>{error}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              }
              type={recoveryResult.success ? 'success' : 'error'}
              showIcon
            />
          </div>
        )}

        {exploreResult && (
          <div style={{ marginTop: '20px' }}>
            <Alert
              message="OSS 目录结构探索结果"
              description={
                <div>
                  <h4>用户目录:</h4>
                  <p>前缀: {exploreResult.userDir?.prefix}</p>
                  <p>文件总数: {exploreResult.userDir?.totalFiles}</p>
                  <p>JSON 文件数: {exploreResult.userDir?.jsonFiles}</p>
                  {exploreResult.userDir?.files && exploreResult.userDir.files.length > 0 && (
                    <div>
                      <p>文件示例:</p>
                      <ul style={{ maxHeight: '200px', overflowY: 'auto', backgroundColor: '#f5f5f5', padding: '10px' }}>
                        {exploreResult.userDir.files.map((file, index) => (
                          <li key={index} style={{ marginBottom: '5px' }}>{file}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <h4 style={{ marginTop: '20px' }}>根目录:</h4>
                  <p>前缀: {exploreResult.rootDir?.prefix || '空'}</p>
                  <p>文件总数: {exploreResult.rootDir?.totalFiles}</p>
                  <p>JSON 文件数: {exploreResult.rootDir?.jsonFiles}</p>
                  {exploreResult.rootDir?.files && exploreResult.rootDir.files.length > 0 && (
                    <div>
                      <p>文件示例:</p>
                      <ul style={{ maxHeight: '200px', overflowY: 'auto', backgroundColor: '#f5f5f5', padding: '10px' }}>
                        {exploreResult.rootDir.files.map((file, index) => (
                          <li key={index} style={{ marginBottom: '5px' }}>{file}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              }
              type="info"
              showIcon
            />
          </div>
        )}
      </Card>

      <Card style={{ marginBottom: '20px' }}>
        <Title level={4}>搜索索引重建</Title>
        <Paragraph>
          重建搜索索引可以提高搜索性能和准确性，特别是在添加了大量新记忆或迁移数据后。
        </Paragraph>

        <Button
          type="primary"
          icon={<SearchOutlined />}
          onClick={() => window.location.href = 'rebuild-search-index.html'}
        >
          重建搜索索引
        </Button>
      </Card>

      <Card title="备份管理" extra={<Button type="link" onClick={loadBackups} loading={isLoadingBackups}>刷新</Button>}>
        {isLoadingBackups ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin />
            <p>加载备份列表...</p>
          </div>
        ) : backups.length > 0 ? (
          <List
            itemLayout="horizontal"
            dataSource={backups}
            renderItem={backup => (
              <List.Item
                actions={[
                  <Button type="link" onClick={() => handleRestore(backup.key)}>恢复</Button>,
                  <Button type="link" danger onClick={() => handleDeleteBackup(backup.key)}>删除</Button>
                ]}
              >
                <List.Item.Meta
                  avatar={<HistoryOutlined style={{ fontSize: '24px' }} />}
                  title={`备份 ${new Date(backup.timestamp).toLocaleString()}`}
                  description={`包含 ${backup.memoryCount} 条记忆`}
                />
              </List.Item>
            )}
          />
        ) : (
          <Empty description="暂无备份" />
        )}
      </Card>
    </div>
  );
};

export default MigrationPage;
