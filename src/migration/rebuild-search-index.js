import React from 'react';
import { createRoot } from 'react-dom/client';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import RebuildSearchIndexPage from './RebuildSearchIndexPage';
import 'antd/dist/reset.css';

// 初始化存储服务
import { storageService } from '../services';
storageService.initialize();

// 渲染应用
const container = document.getElementById('root');
const root = createRoot(container);
root.render(
  <ConfigProvider locale={zhCN}>
    <RebuildSearchIndexPage />
  </ConfigProvider>
);
