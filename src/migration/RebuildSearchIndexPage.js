import React, { useState } from 'react';
import { Layout, Typography, Button, Card, Alert, Progress, Space, Divider } from 'antd';
import { ArrowLeftOutlined, DatabaseOutlined } from '@ant-design/icons';
import { ossStorageService, searchIndexService } from '../services';

const { Header, Content } = Layout;
const { Title, Paragraph } = Typography;

const RebuildSearchIndexPage = () => {
  const [isRebuilding, setIsRebuilding] = useState(false);
  const [progress, setProgress] = useState(0);
  const [result, setResult] = useState(null);
  const [memoryCount, setMemoryCount] = useState(0);

  // 返回上一页
  const goBack = () => {
    window.history.back();
  };

  // 检查是否需要重建索引
  const checkIndexStatus = async () => {
    try {
      // 初始化OSS存储服务
      if (!ossStorageService.isInitialized) {
        await ossStorageService.initialize();
      }

      // 获取元数据
      const metadata = await ossStorageService.getUserMetadata();
      setMemoryCount(metadata.memory_count || 0);
    } catch (error) {
      console.error('检查索引状态失败:', error);
    }
  };

  // 组件挂载时检查索引状态
  React.useEffect(() => {
    checkIndexStatus();
  }, []);

  // 重建搜索索引
  const handleRebuildIndex = async () => {
    try {
      setIsRebuilding(true);
      setProgress(0);
      setResult(null);

      // 初始化OSS存储服务
      if (!ossStorageService.isInitialized) {
        await ossStorageService.initialize();
      }

      // 获取元数据
      const metadata = await ossStorageService.getUserMetadata();
      const totalMemories = metadata.memory_count || 0;
      
      // 设置进度监听器
      const originalIndexMemory = searchIndexService.indexMemory;
      let processedCount = 0;
      
      // 替换indexMemory方法，添加进度更新
      searchIndexService.indexMemory = async (memory) => {
        await originalIndexMemory.call(searchIndexService, memory);
        processedCount++;
        const newProgress = Math.floor((processedCount / totalMemories) * 100);
        setProgress(newProgress);
      };

      // 重建索引
      await searchIndexService.rebuildIndex();

      // 恢复原始方法
      searchIndexService.indexMemory = originalIndexMemory;

      // 设置结果
      setResult({
        success: true,
        message: `搜索索引重建成功，共处理 ${processedCount} 条记忆。`
      });
    } catch (error) {
      console.error('重建搜索索引失败:', error);
      setResult({
        success: false,
        message: `重建搜索索引失败: ${error.message}`
      });
    } finally {
      setIsRebuilding(false);
      setProgress(100);
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 24px', display: 'flex', alignItems: 'center' }}>
        <Button
          type="text"
          icon={<ArrowLeftOutlined />}
          onClick={goBack}
          style={{ marginRight: '16px' }}
        />
        <Title level={3} style={{ margin: '0' }}>重建搜索索引</Title>
      </Header>
      
      <Content style={{ padding: '24px' }}>
        <Card style={{ marginBottom: '20px' }}>
          <Title level={4}>搜索索引重建</Title>
          <Paragraph>
            重建搜索索引可以提高搜索性能和准确性，特别是在添加了大量新记忆或修改了现有记忆后。
            重建过程会扫描所有记忆并创建优化的搜索索引，以支持全文搜索功能。
          </Paragraph>
          
          <Alert
            message="索引状态"
            description={`当前系统中有 ${memoryCount} 条记忆需要建立索引。`}
            type="info"
            showIcon
            style={{ marginBottom: '20px' }}
          />
          
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button
              type="primary"
              icon={<DatabaseOutlined />}
              size="large"
              onClick={handleRebuildIndex}
              loading={isRebuilding}
              disabled={isRebuilding}
            >
              {isRebuilding ? '正在重建索引...' : '重建搜索索引'}
            </Button>
            
            {isRebuilding && (
              <div style={{ marginTop: '20px' }}>
                <Progress percent={progress} status="active" />
                <p>正在重建搜索索引，请勿关闭页面...</p>
              </div>
            )}
            
            {result && (
              <Alert
                message={result.success ? '重建成功' : '重建失败'}
                description={result.message}
                type={result.success ? 'success' : 'error'}
                showIcon
                style={{ marginTop: '20px' }}
              />
            )}
          </Space>
        </Card>
        
        <Divider />
        
        <Card>
          <Title level={4}>关于搜索索引</Title>
          <Paragraph>
            搜索索引是一种数据结构，用于加速全文搜索操作。它将记忆中的关键词映射到记忆ID，
            使系统能够快速找到包含特定关键词的记忆，而无需扫描所有记忆内容。
          </Paragraph>
          
          <Paragraph>
            <strong>重建索引的情况：</strong>
            <ul>
              <li>添加了大量新记忆</li>
              <li>修改了大量现有记忆</li>
              <li>搜索结果不准确或不完整</li>
              <li>系统升级后</li>
            </ul>
          </Paragraph>
          
          <Paragraph>
            重建索引可能需要一些时间，具体取决于记忆的数量和大小。在重建过程中，
            请保持页面打开，直到过程完成。
          </Paragraph>
        </Card>
      </Content>
    </Layout>
  );
};

export default RebuildSearchIndexPage;
