// Content script for EverSnip extension

// 创建快速保存对话框
function createQuickSaveDialog(selectedText) {
  // 获取默认分类和分类列表
  chrome.storage.local.get(['settings'], (result) => {
    const settings = result.settings || {};
    const categories = settings.categories || [];
    const defaultCategory = settings.defaultCategory || '';

    // 创建对话框
    const dialog = document.createElement('div');
    dialog.style.position = 'fixed';
    dialog.style.top = '50%';
    dialog.style.left = '50%';
    dialog.style.transform = 'translate(-50%, -50%)';
    dialog.style.backgroundColor = 'white';
    dialog.style.padding = '20px';
    dialog.style.borderRadius = '8px';
    dialog.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    dialog.style.zIndex = '10000';
    dialog.style.width = '400px';
    dialog.style.maxWidth = '90vw';
    dialog.style.maxHeight = '80vh';
    dialog.style.overflow = 'auto';
    dialog.style.fontFamily = 'Arial, sans-serif';

    // 添加标题
    const title = document.createElement('h3');
    title.textContent = '快速保存记忆';
    title.style.margin = '0 0 16px 0';
    title.style.color = '#333';
    dialog.appendChild(title);

    // 添加内容预览
    const preview = document.createElement('div');
    preview.style.backgroundColor = '#f5f5f5';
    preview.style.padding = '12px';
    preview.style.borderRadius = '4px';
    preview.style.marginBottom = '16px';
    preview.style.maxHeight = '100px';
    preview.style.overflow = 'auto';
    preview.style.whiteSpace = 'pre-wrap';
    preview.style.wordBreak = 'break-word';
    preview.style.fontSize = '14px';
    preview.textContent = selectedText;
    dialog.appendChild(preview);

    // 添加标题输入框
    const titleLabel = document.createElement('div');
    titleLabel.textContent = '标题';
    titleLabel.style.marginBottom = '4px';
    titleLabel.style.fontWeight = 'bold';
    dialog.appendChild(titleLabel);

    const titleInput = document.createElement('input');
    titleInput.type = 'text';
    titleInput.placeholder = '输入记忆标题';
    titleInput.style.width = '100%';
    titleInput.style.padding = '8px';
    titleInput.style.boxSizing = 'border-box';
    titleInput.style.marginBottom = '16px';
    titleInput.style.borderRadius = '4px';
    titleInput.style.border = '1px solid #d9d9d9';
    dialog.appendChild(titleInput);

    // 添加分类选择
    const categoryLabel = document.createElement('div');
    categoryLabel.textContent = '分类';
    categoryLabel.style.marginBottom = '4px';
    categoryLabel.style.fontWeight = 'bold';
    dialog.appendChild(categoryLabel);

    const categorySelect = document.createElement('select');
    categorySelect.style.width = '100%';
    categorySelect.style.padding = '8px';
    categorySelect.style.boxSizing = 'border-box';
    categorySelect.style.marginBottom = '16px';
    categorySelect.style.borderRadius = '4px';
    categorySelect.style.border = '1px solid #d9d9d9';

    // 添加分类选项
    categories.forEach(category => {
      const option = document.createElement('option');
      option.value = category.id;
      option.textContent = category.name;
      option.selected = category.id === defaultCategory;
      categorySelect.appendChild(option);
    });

    dialog.appendChild(categorySelect);

    // 添加按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.style.display = 'flex';
    buttonContainer.style.justifyContent = 'flex-end';
    buttonContainer.style.gap = '8px';

    // 添加取消按钮
    const cancelButton = document.createElement('button');
    cancelButton.textContent = '取消';
    cancelButton.style.padding = '8px 16px';
    cancelButton.style.border = '1px solid #d9d9d9';
    cancelButton.style.borderRadius = '4px';
    cancelButton.style.backgroundColor = 'white';
    cancelButton.style.cursor = 'pointer';
    cancelButton.onclick = () => {
      document.body.removeChild(dialog);
      document.body.removeChild(overlay);
    };
    buttonContainer.appendChild(cancelButton);

    // 添加保存按钮
    const saveButton = document.createElement('button');
    saveButton.textContent = '保存';
    saveButton.style.padding = '8px 16px';
    saveButton.style.border = 'none';
    saveButton.style.borderRadius = '4px';
    saveButton.style.backgroundColor = '#1890ff';
    saveButton.style.color = 'white';
    saveButton.style.cursor = 'pointer';
    saveButton.onclick = () => {
      const title = titleInput.value.trim() || '无标题记忆';
      const categoryId = categorySelect.value;

      // 创建新记忆
      const newMemory = {
        id: Date.now().toString(),
        title: title,
        content: selectedText,
        category: categoryId,
        createdAt: new Date().toISOString(),
        url: window.location.href,
        tags: [],
      };

      // 从存储中获取现有记忆
      chrome.storage.local.get(['memories'], (result) => {
        const memories = result.memories || [];
        const updatedMemories = [...memories, newMemory];

        // 保存更新后的记忆列表
        chrome.storage.local.set({ memories: updatedMemories }, () => {
          // 关闭对话框
          document.body.removeChild(dialog);
          document.body.removeChild(overlay);

          // 显示成功通知并提供查看选项
          showNotificationWithAction('记忆已保存！', '查看记忆', () => {
            // 打开浏览记忆页面
            chrome.runtime.sendMessage({ type: 'OPEN_BROWSE_MEMORIES' });
          });
        });
      });
    };
    buttonContainer.appendChild(saveButton);

    dialog.appendChild(buttonContainer);

    // 创建遮罩层
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    overlay.style.zIndex = '9999';

    // 添加到页面
    document.body.appendChild(overlay);
    document.body.appendChild(dialog);

    // 聚焦标题输入框
    titleInput.focus();
  });
}

// 显示通知
function showNotification(message) {
  const notification = document.createElement('div');
  notification.textContent = message;
  notification.style.position = 'fixed';
  notification.style.bottom = '20px';
  notification.style.right = '20px';
  notification.style.padding = '10px 20px';
  notification.style.backgroundColor = '#1890ff';
  notification.style.color = 'white';
  notification.style.borderRadius = '4px';
  notification.style.zIndex = '9999';
  notification.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';

  document.body.appendChild(notification);

  // 3秒后移除通知
  setTimeout(() => {
    notification.remove();
  }, 3000);
}

// 显示带操作按钮的通知
function showNotificationWithAction(message, actionText, actionCallback) {
  const notification = document.createElement('div');
  notification.style.position = 'fixed';
  notification.style.bottom = '20px';
  notification.style.right = '20px';
  notification.style.backgroundColor = '#1890ff';
  notification.style.color = 'white';
  notification.style.padding = '10px 20px';
  notification.style.borderRadius = '4px';
  notification.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
  notification.style.zIndex = '9999';
  notification.style.display = 'flex';
  notification.style.alignItems = 'center';
  notification.style.justifyContent = 'space-between';
  notification.style.minWidth = '200px';

  // 消息文本
  const messageSpan = document.createElement('span');
  messageSpan.textContent = message;
  notification.appendChild(messageSpan);

  // 操作按钮
  const actionButton = document.createElement('button');
  actionButton.textContent = actionText;
  actionButton.style.marginLeft = '10px';
  actionButton.style.backgroundColor = 'white';
  actionButton.style.color = '#1890ff';
  actionButton.style.border = 'none';
  actionButton.style.borderRadius = '4px';
  actionButton.style.padding = '4px 8px';
  actionButton.style.cursor = 'pointer';
  actionButton.onclick = () => {
    if (actionCallback) actionCallback();
    document.body.removeChild(notification);
  };
  notification.appendChild(actionButton);

  document.body.appendChild(notification);

  // 5秒后自动关闭
  setTimeout(() => {
    if (document.body.contains(notification)) {
      document.body.removeChild(notification);
    }
  }, 5000);
}

// 监听键盘快捷键
document.addEventListener('keydown', (event) => {
  // Ctrl+Shift+M 快速保存选中的文本
  if (event.ctrlKey && event.shiftKey && event.key === 'M') {
    const selectedText = window.getSelection().toString().trim();

    if (selectedText) {
      createQuickSaveDialog(selectedText);
    }
  }
});

// 添加右键菜单
document.addEventListener('contextmenu', function(event) {
  const selectedText = window.getSelection().toString().trim();
  if (selectedText) {
    // 将选中的文本保存到存储中，以便右键菜单项可以访问
    chrome.storage.local.set({ selectedText: selectedText });
  }
});

// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'saveSelectedText') {
    chrome.storage.local.get(['selectedText'], (result) => {
      if (result.selectedText) {
        createQuickSaveDialog(result.selectedText);
      }
    });
  }
  return true;
});
