import React from 'react';
import { Layout, Typography, Button, Tooltip } from 'antd';
import { ArrowLeftOutlined, ReadOutlined } from '@ant-design/icons';
import MemoryEditor from '../components/MemoryEditor';

const { Header, Content, Footer } = Layout;
const { Title } = Typography;

const AddMemory = () => {
  const goBack = () => {
    // 如果在iframe中，则发送消息到父窗口
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'navigate',
        data: { page: 'browse-memories' }
      }, '*');
    } else {
      window.close();
    }
  };

  const handleSave = (memoryId) => {
    // 保存成功后发送消息到父窗口
    setTimeout(() => {
      // 如果在iframe中，则发送消息到父窗口
      if (window.parent !== window) {
        window.parent.postMessage({
          type: 'memory_added',
          data: { memoryId }
        }, '*');
      } else {
        // 如果不在iframe中，则直接跳转
        window.location.href = 'browse-memories.html';
      }
    }, 1000); // 等待一秒以显示成功消息
  };

  const openDocumentation = () => {
    window.open('https://eversnip.github.io/EverSnip/', '_blank');
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 24px', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={goBack}
            style={{ marginRight: '16px' }}
          />
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <img src="../icons/icon48.png" alt="Logo" style={{ width: 24, height: 24, marginRight: 8 }} />
            <Title level={3} style={{ margin: '0' }}>添加记忆</Title>
          </div>
        </div>
        <Tooltip title="查看使用文档">
          <Button
            type="text"
            icon={<ReadOutlined />}
            onClick={openDocumentation}
          />
        </Tooltip>
      </Header>
      <Content style={{ padding: '24px' }}>
        <MemoryEditor onSave={handleSave} />
      </Content>
      <Footer style={{ textAlign: 'center' }}>
        拾光忆栈 ©{new Date().getFullYear()}
      </Footer>
    </Layout>
  );
};

export default AddMemory;
