/**
 * 设置服务接口
 * 定义了应用设置管理的统一接口
 */
export class ISettingsService {
  /**
   * 获取设置值
   * @param {string} key - 设置键
   * @param {any} defaultValue - 默认值
   * @returns {Promise<any>} 设置值
   */
  async get(key, defaultValue = null) {
    throw new Error('Method get() must be implemented by subclass');
  }

  /**
   * 设置值
   * @param {string} key - 设置键
   * @param {any} value - 设置值
   * @returns {Promise<void>}
   */
  async set(key, value) {
    throw new Error('Method set() must be implemented by subclass');
  }

  /**
   * 删除设置
   * @param {string} key - 设置键
   * @returns {Promise<boolean>} 是否删除成功
   */
  async remove(key) {
    throw new Error('Method remove() must be implemented by subclass');
  }

  /**
   * 获取所有设置
   * @returns {Promise<Object>} 所有设置
   */
  async getAll() {
    throw new Error('Method getAll() must be implemented by subclass');
  }

  /**
   * 批量设置
   * @param {Object} settings - 设置对象
   * @returns {Promise<void>}
   */
  async setMultiple(settings) {
    throw new Error('Method setMultiple() must be implemented by subclass');
  }

  /**
   * 重置设置为默认值
   * @param {string[]} keys - 要重置的键，不传则重置所有
   * @returns {Promise<void>}
   */
  async reset(keys) {
    throw new Error('Method reset() must be implemented by subclass');
  }

  /**
   * 检查设置是否存在
   * @param {string} key - 设置键
   * @returns {Promise<boolean>} 是否存在
   */
  async has(key) {
    throw new Error('Method has() must be implemented by subclass');
  }

  /**
   * 监听设置变化
   * @param {Function} callback - 变化回调函数
   * @returns {Function} 取消监听的函数
   */
  onChanged(callback) {
    throw new Error('Method onChanged() must be implemented by subclass');
  }

  /**
   * 获取设置模式
   * @returns {Promise<Object>} 设置模式定义
   */
  async getSchema() {
    throw new Error('Method getSchema() must be implemented by subclass');
  }

  /**
   * 验证设置值
   * @param {string} key - 设置键
   * @param {any} value - 设置值
   * @returns {Promise<boolean>} 是否有效
   */
  async validate(key, value) {
    throw new Error('Method validate() must be implemented by subclass');
  }

  /**
   * 导出设置
   * @returns {Promise<Object>} 设置数据
   */
  async export() {
    throw new Error('Method export() must be implemented by subclass');
  }

  /**
   * 导入设置
   * @param {Object} settings - 设置数据
   * @param {boolean} merge - 是否合并现有设置
   * @returns {Promise<void>}
   */
  async import(settings, merge = true) {
    throw new Error('Method import() must be implemented by subclass');
  }
}
