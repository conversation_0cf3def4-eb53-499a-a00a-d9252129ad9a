import { ISettingsService } from './ISettingsService.js';

/**
 * 设置服务实现
 * 基于存储仓储的应用设置管理
 */
export class SettingsService extends ISettingsService {
  /**
   * 构造函数
   * @param {IStorageRepository} storageRepository - 存储仓储
   * @param {ICacheService} cacheService - 缓存服务
   * @param {ILockService} lockService - 锁服务
   * @param {string} deviceId - 设备ID
   * @param {Object} defaultSettings - 默认设置
   */
  constructor(storageRepository, cacheService, lockService, deviceId, defaultSettings = {}) {
    super();
    this.storage = storageRepository;
    this.cache = cacheService;
    this.lockService = lockService;
    this.deviceId = deviceId;
    this.defaultSettings = this._getDefaultSettings();
    this.namespace = 'settings';
    this.cacheNamespace = 'settings';
    this.cacheTtl = 5 * 60 * 1000; // 5分钟缓存
    this.changeListeners = new Set();
    
    // 设置模式定义
    this.schema = {
      theme: { type: 'string', enum: ['light', 'dark', 'auto'], default: 'auto' },
      language: { type: 'string', enum: ['zh-CN', 'en-US'], default: 'zh-CN' },
      autoSave: { type: 'boolean', default: true },
      saveInterval: { type: 'number', min: 1000, max: 60000, default: 5000 },
      maxMemories: { type: 'number', min: 100, max: 10000, default: 1000 },
      enableNotifications: { type: 'boolean', default: true },
      enableSync: { type: 'boolean', default: false },
      debugMode: { type: 'boolean', default: false }
    };

    // 监听存储变化
    this._setupStorageListener();
  }

  /**
   * 创建设置键
   * @param {string} key - 原始键
   * @returns {string} 完整的设置键
   */
  _createKey(key) {
    return this.storage.createNamespacedKey ? 
      this.storage.createNamespacedKey(this.namespace, key) : 
      `${this.namespace}:${key}`;
  }

  /**
   * 创建缓存键
   * @param {string} key - 原始键
   * @returns {string} 缓存键
   */
  _createCacheKey(key) {
    return `${this.cacheNamespace}:${key}`;
  }

  /**
   * 设置存储变化监听器
   */
  _setupStorageListener() {
    if (this.storage.onChanged) {
      this.storage.onChanged((changes, areaName) => {
        for (const [key, change] of Object.entries(changes)) {
          if (key.startsWith(`${this.namespace}:`)) {
            const settingKey = key.replace(`${this.namespace}:`, '');
            
            // 清除缓存
            this.cache.delete(this._createCacheKey(settingKey), this.cacheNamespace);
            
            // 通知监听器
            this._notifyListeners(settingKey, change.newValue, change.oldValue);
          }
        }
      });
    }
  }

  /**
   * 通知变化监听器
   * @param {string} key - 设置键
   * @param {any} newValue - 新值
   * @param {any} oldValue - 旧值
   */
  _notifyListeners(key, newValue, oldValue) {
    for (const listener of this.changeListeners) {
      try {
        listener(key, newValue, oldValue);
      } catch (error) {
        console.error('Settings change listener error:', error);
      }
    }
  }

  /**
   * 获取设置值
   * @param {string} key - 设置键
   * @param {any} defaultValue - 默认值
   * @returns {Promise<any>} 设置值
   */
  async get(key, defaultValue = null) {
    try {
      // 先从缓存获取
      const cacheKey = this._createCacheKey(key);
      let value = await this.cache.get(cacheKey, this.cacheNamespace);
      
      if (value !== null) {
        return value;
      }

      // 从存储获取
      const storageKey = this._createKey(key);
      const result = await this.storage.get(storageKey);
      value = result[storageKey];

      // 如果没有值，使用默认值
      if (value === undefined || value === null) {
        value = defaultValue !== null ? defaultValue : 
                this.defaultSettings[key] !== undefined ? this.defaultSettings[key] :
                this.schema[key]?.default !== undefined ? this.schema[key].default :
                null;
      }

      // 缓存结果
      if (value !== null) {
        await this.cache.set(cacheKey, value, { 
          ttl: this.cacheTtl, 
          namespace: this.cacheNamespace 
        });
      }

      return value;
    } catch (error) {
      console.error(`Failed to get setting '${key}':`, error);
      return defaultValue;
    }
  }

  /**
   * 设置值
   * @param {string} key - 设置键
   * @param {any} value - 设置值
   * @returns {Promise<void>}
   */
  async set(key, value) {
    // 验证值
    const isValid = await this.validate(key, value);
    if (!isValid) {
      throw new Error(`Invalid value for setting '${key}': ${value}`);
    }

    try {
      const storageKey = this._createKey(key);
      const oldValue = await this.get(key);

      // 保存到存储
      await this.storage.set({ [storageKey]: value });

      // 更新缓存
      const cacheKey = this._createCacheKey(key);
      await this.cache.set(cacheKey, value, { 
        ttl: this.cacheTtl, 
        namespace: this.cacheNamespace 
      });

      // 通知监听器
      this._notifyListeners(key, value, oldValue);
    } catch (error) {
      throw new Error(`Failed to set setting '${key}': ${error.message}`);
    }
  }

  /**
   * 删除设置
   * @param {string} key - 设置键
   * @returns {Promise<boolean>} 是否删除成功
   */
  async remove(key) {
    try {
      const storageKey = this._createKey(key);
      const oldValue = await this.get(key);

      // 从存储删除
      await this.storage.remove(storageKey);

      // 从缓存删除
      const cacheKey = this._createCacheKey(key);
      await this.cache.delete(cacheKey, this.cacheNamespace);

      // 通知监听器
      this._notifyListeners(key, null, oldValue);

      return true;
    } catch (error) {
      console.error(`Failed to remove setting '${key}':`, error);
      return false;
    }
  }

  /**
   * 获取所有设置
   * @returns {Promise<Object>} 所有设置
   */
  async getAll() {
    try {
      const result = await this.storage.getByNamespace ? 
        await this.storage.getByNamespace(this.namespace) :
        await this._getAllByPrefix();

      // 合并默认设置
      const allSettings = { ...this.defaultSettings };
      
      // 添加模式默认值
      for (const [key, schema] of Object.entries(this.schema)) {
        if (schema.default !== undefined && allSettings[key] === undefined) {
          allSettings[key] = schema.default;
        }
      }

      // 覆盖存储的设置
      Object.assign(allSettings, result);

      return allSettings;
    } catch (error) {
      console.error('Failed to get all settings:', error);
      return { ...this.defaultSettings };
    }
  }

  /**
   * 通过前缀获取所有设置（兼容方法）
   * @returns {Promise<Object>} 设置对象
   */
  async _getAllByPrefix() {
    const allKeys = await this.storage.keys();
    const settingKeys = allKeys.filter(key => key.startsWith(`${this.namespace}:`));
    
    if (settingKeys.length === 0) {
      return {};
    }

    const result = await this.storage.get(settingKeys);
    const settings = {};

    for (const [key, value] of Object.entries(result)) {
      const settingKey = key.replace(`${this.namespace}:`, '');
      settings[settingKey] = value;
    }

    return settings;
  }

  /**
   * 批量设置
   * @param {Object} settings - 设置对象
   * @returns {Promise<void>}
   */
  async setMultiple(settings) {
    const validatedSettings = {};
    const storageData = {};

    // 验证所有设置
    for (const [key, value] of Object.entries(settings)) {
      const isValid = await this.validate(key, value);
      if (!isValid) {
        throw new Error(`Invalid value for setting '${key}': ${value}`);
      }
      validatedSettings[key] = value;
      storageData[this._createKey(key)] = value;
    }

    try {
      // 保存到存储
      await this.storage.set(storageData);

      // 更新缓存
      for (const [key, value] of Object.entries(validatedSettings)) {
        const cacheKey = this._createCacheKey(key);
        await this.cache.set(cacheKey, value, { 
          ttl: this.cacheTtl, 
          namespace: this.cacheNamespace 
        });
      }

      // 通知监听器
      for (const [key, value] of Object.entries(validatedSettings)) {
        const oldValue = await this.get(key);
        this._notifyListeners(key, value, oldValue);
      }
    } catch (error) {
      throw new Error(`Failed to set multiple settings: ${error.message}`);
    }
  }

  /**
   * 重置设置为默认值
   * @param {string[]} keys - 要重置的键，不传则重置所有
   * @returns {Promise<void>}
   */
  async reset(keys) {
    try {
      if (keys && Array.isArray(keys)) {
        // 重置指定键
        const resetData = {};
        for (const key of keys) {
          const defaultValue = this.defaultSettings[key] !== undefined ? 
            this.defaultSettings[key] : 
            this.schema[key]?.default;
          
          if (defaultValue !== undefined) {
            resetData[key] = defaultValue;
          }
        }
        await this.setMultiple(resetData);
      } else {
        // 重置所有设置
        await this.storage.removeByNamespace ? 
          await this.storage.removeByNamespace(this.namespace) :
          await this._removeAllByPrefix();
        
        // 清除缓存
        await this.cache.clear(this.cacheNamespace);
      }
    } catch (error) {
      throw new Error(`Failed to reset settings: ${error.message}`);
    }
  }

  /**
   * 通过前缀删除所有设置（兼容方法）
   * @returns {Promise<void>}
   */
  async _removeAllByPrefix() {
    const allKeys = await this.storage.keys();
    const settingKeys = allKeys.filter(key => key.startsWith(`${this.namespace}:`));
    
    if (settingKeys.length > 0) {
      await this.storage.remove(settingKeys);
    }
  }

  /**
   * 检查设置是否存在
   * @param {string} key - 设置键
   * @returns {Promise<boolean>} 是否存在
   */
  async has(key) {
    try {
      const storageKey = this._createKey(key);
      return await this.storage.has(storageKey);
    } catch (error) {
      console.error(`Failed to check setting existence '${key}':`, error);
      return false;
    }
  }

  /**
   * 监听设置变化
   * @param {Function} callback - 变化回调函数
   * @returns {Function} 取消监听的函数
   */
  onChanged(callback) {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    this.changeListeners.add(callback);

    return () => {
      this.changeListeners.delete(callback);
    };
  }

  /**
   * 获取设置模式
   * @returns {Promise<Object>} 设置模式定义
   */
  async getSchema() {
    return { ...this.schema };
  }

  /**
   * 验证设置值
   * @param {string} key - 设置键
   * @param {any} value - 设置值
   * @returns {Promise<boolean>} 是否有效
   */
  async validate(key, value) {
    const schema = this.schema[key];
    if (!schema) {
      return true; // 未定义模式的键允许任何值
    }

    try {
      // 类型检查
      if (schema.type && typeof value !== schema.type) {
        return false;
      }

      // 枚举检查
      if (schema.enum && !schema.enum.includes(value)) {
        return false;
      }

      // 数值范围检查
      if (schema.type === 'number') {
        if (schema.min !== undefined && value < schema.min) {
          return false;
        }
        if (schema.max !== undefined && value > schema.max) {
          return false;
        }
      }

      // 字符串长度检查
      if (schema.type === 'string') {
        if (schema.minLength !== undefined && value.length < schema.minLength) {
          return false;
        }
        if (schema.maxLength !== undefined && value.length > schema.maxLength) {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error(`Validation error for setting '${key}':`, error);
      return false;
    }
  }

  /**
   * 导出设置
   * @returns {Promise<Object>} 设置数据
   */
  async export() {
    try {
      const allSettings = await this.getAll();
      return {
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        settings: allSettings
      };
    } catch (error) {
      throw new Error(`Failed to export settings: ${error.message}`);
    }
  }

  /**
   * 导入设置
   * @param {Object} data - 设置数据
   * @param {boolean} merge - 是否合并现有设置
   * @returns {Promise<void>}
   */
  async import(data, merge = true) {
    if (!data || !data.settings) {
      throw new Error('Invalid settings data format');
    }

    try {
      if (!merge) {
        await this.reset();
      }

      await this.setMultiple(data.settings);
    } catch (error) {
      throw new Error(`Failed to import settings: ${error.message}`);
    }
  }

  /**
   * 获取默认设置
   * @returns {Object} 默认设置对象
   */
  getDefaultSettings() {
    return this._getDefaultSettings();
  }

  /**
   * 内部获取默认设置方法
   * @returns {Object} 默认设置对象
   * @private
   */
  _getDefaultSettings() {
    return {
      categories: [
        { id: 'work', name: '工作', color: '#3498db', icon: 'WorkIcon' },
        { id: 'study', name: '学习', color: '#2ecc71', icon: 'StudyIcon' },
        { id: 'life', name: '生活', color: '#e74c3c', icon: 'LifeIcon' },
        { id: 'other', name: '其他', color: '#95a5a6', icon: 'OtherIcon' }
      ],
      defaultCategory: 'other',
      darkMode: false,
      fontSize: 14,
      autoSave: true,
      saveInterval: 5000,
      enableNotifications: true,
      enableSync: false,
      maxMemories: 1000,
      language: 'zh-CN',
      theme: 'auto'
    };
  }

  /**
   * 获取设置
   * @returns {Promise<Object>} 设置对象
   */
  async getSettings() {
    return this._getSettingsInternal();
  }

  /**
   * 内部获取设置方法（不使用锁）
   * @returns {Promise<Object>} 设置对象
   * @private
   */
  async _getSettingsInternal() {
    // 检查存储是否可用
    if (!await this.storage.isAvailable()) {
      throw new Error('存储服务不可用');
    }

    // 先尝试从缓存获取
    const cachedSettings = await this.cache.get('settings');
    if (cachedSettings) {
      return cachedSettings;
    }

    // 从存储获取
    const result = await this.storage.get('settings');
    let settings = result.settings;

    if (!settings) {
      // 如果没有设置，返回默认设置
      settings = this.getDefaultSettings();
    } else {
      // 合并默认设置，确保所有必需字段都存在
      settings = { ...this.getDefaultSettings(), ...settings };
    }

    // 缓存设置
    await this.cache.set('settings', settings, { ttl: this.cacheTtl });

    return settings;
  }

  /**
   * 保存设置
   * @param {Object} settings - 设置对象
   * @returns {Promise<void>}
   */
  async saveSettings(settings) {
    // 检查锁服务是否可用
    if (!await this.lockService.isAvailable()) {
      throw new Error('锁服务不可用');
    }

    const lockKey = 'settings';
    const token = await this.lockService.acquire(lockKey);

    try {
      // 获取当前设置（使用内部方法避免死锁）
      const currentSettings = await this._getSettingsInternal();

      // 合并设置
      const updatedSettings = {
        ...currentSettings,
        ...settings,
        dataVersion: (currentSettings.dataVersion || 0) + 1,
        lastModified: new Date().toISOString(),
        lastModifiedBy: this.deviceId
      };

      // 保存到存储
      await this.storage.set({ settings: updatedSettings });

      // 更新缓存
      await this.cache.set('settings', updatedSettings, { ttl: this.cacheTtl });

      // 通知监听器
      this._notifyListeners('settings', updatedSettings, currentSettings);
    } finally {
      await this.lockService.release(lockKey, token);
    }
  }

  /**
   * 获取特定设置项
   * @param {string} key - 设置键
   * @returns {Promise<any>} 设置值
   */
  async getSetting(key) {
    const settings = await this.getSettings();
    return settings[key];
  }

  /**
   * 更新特定设置项
   * @param {string} key - 设置键
   * @param {any} value - 设置值
   * @returns {Promise<void>}
   */
  async updateSetting(key, value) {
    const lockKey = 'settings';
    const token = await this.lockService.acquire(lockKey);

    try {
      const currentSettings = await this._getSettingsInternal();
      const updatedSettings = {
        ...currentSettings,
        [key]: value,
        dataVersion: (currentSettings.dataVersion || 0) + 1,
        lastModified: new Date().toISOString(),
        lastModifiedBy: this.deviceId
      };

      await this.storage.set({ settings: updatedSettings });
      await this.cache.set('settings', updatedSettings, { ttl: this.cacheTtl });

      this._notifyListeners(key, value, currentSettings[key]);
    } finally {
      await this.lockService.release(lockKey, token);
    }
  }

  /**
   * 添加分类
   * @param {Object} category - 分类对象
   * @returns {Promise<void>}
   */
  async addCategory(category) {
    // 验证分类数据
    this._validateCategory(category);

    const settings = await this.getSettings();
    const categories = [...settings.categories];

    // 检查ID是否已存在
    if (categories.find(cat => cat.id === category.id)) {
      throw new Error(`分类ID ${category.id} 已存在`);
    }

    categories.push(category);
    await this.saveSettings({ categories });
  }

  /**
   * 更新分类
   * @param {string} id - 分类ID
   * @param {Object} updates - 更新数据
   * @returns {Promise<void>}
   */
  async updateCategory(id, updates) {
    // 验证更新数据
    if (updates.color) {
      this._validateColor(updates.color);
    }

    const settings = await this.getSettings();
    const categories = [...settings.categories];
    const categoryIndex = categories.findIndex(cat => cat.id === id);

    if (categoryIndex === -1) {
      throw new Error(`找不到ID为 ${id} 的分类`);
    }

    categories[categoryIndex] = { ...categories[categoryIndex], ...updates };
    await this.saveSettings({ categories });
  }

  /**
   * 删除分类
   * @param {string} id - 分类ID
   * @returns {Promise<void>}
   */
  async deleteCategory(id) {
    const settings = await this.getSettings();
    const categories = settings.categories.filter(cat => cat.id !== id);

    if (categories.length === settings.categories.length) {
      throw new Error(`找不到ID为 ${id} 的分类`);
    }

    await this.saveSettings({ categories });
  }

  /**
   * 获取所有分类
   * @returns {Promise<Array>} 分类数组
   */
  async getCategories() {
    const settings = await this.getSettings();
    return settings.categories || [];
  }

  /**
   * 根据ID获取分类
   * @param {string} id - 分类ID
   * @returns {Promise<Object|null>} 分类对象
   */
  async getCategory(id) {
    const categories = await this.getCategories();
    return categories.find(cat => cat.id === id) || null;
  }

  /**
   * 验证分类数据
   * @param {Object} category - 分类对象
   * @private
   */
  _validateCategory(category) {
    if (!category.id) {
      throw new Error('分类ID不能为空');
    }
    if (!category.name) {
      throw new Error('分类名称不能为空');
    }
    if (category.color) {
      this._validateColor(category.color);
    }
  }

  /**
   * 验证颜色格式
   * @param {string} color - 颜色值
   * @private
   */
  _validateColor(color) {
    const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    if (!colorRegex.test(color)) {
      throw new Error('颜色格式无效');
    }
  }

  /**
   * 导出设置
   * @returns {Promise<Object>} 导出的设置数据
   */
  async exportSettings() {
    const settings = await this.getSettings();
    return {
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      exportedBy: this.deviceId,
      settings
    };
  }

  /**
   * 导入设置
   * @param {Object} data - 导入的设置数据
   * @returns {Promise<void>}
   */
  async importSettings(data) {
    if (!data || !data.settings) {
      throw new Error('无效的导入数据格式');
    }

    await this.saveSettings(data.settings);
  }

  /**
   * 检查服务是否可用
   * @returns {Promise<boolean>} 是否可用
   */
  async isAvailable() {
    try {
      return await this.storage.isAvailable() &&
             await this.cache.isAvailable() &&
             await this.lockService.isAvailable();
    } catch (error) {
      return false;
    }
  }

  /**
   * 通知监听器
   * @param {string} key - 变化的键
   * @param {any} newValue - 新值
   * @param {any} oldValue - 旧值
   * @private
   */
  _notifyListeners(key, newValue, oldValue) {
    const event = {
      key,
      newValue,
      oldValue,
      timestamp: new Date().toISOString()
    };

    this.changeListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('设置变化监听器执行失败:', error);
      }
    });
  }

  /**
   * 获取服务类型
   * @returns {string} 服务类型
   */
  getType() {
    return 'SettingsService';
  }
}
