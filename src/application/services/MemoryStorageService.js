/**
 * 记忆存储服务实现
 * 提供记忆数据管理的具体实现
 */

import { IMemoryStorageService } from './IMemoryStorageService.js';
import { Memory } from '../../domain/entities/Memory.js';

/**
 * 记忆存储服务实现类
 * 使用依赖注入模式，集成缓存、锁机制和存储仓储
 */
export class MemoryStorageService extends IMemoryStorageService {
  /**
   * 构造函数
   * @param {IStorageRepository} storageRepository - 存储仓储
   * @param {ICacheService} cacheService - 缓存服务
   * @param {ILockService} lockService - 锁服务
   * @param {string} deviceId - 设备ID
   */
  constructor(storageRepository, cacheService, lockService, deviceId) {
    super();
    this.storageRepository = storageRepository;
    this.cacheService = cacheService;
    this.lockService = lockService;
    this.deviceId = deviceId;
    
    // 缓存配置
    this.cacheConfig = {
      memoriesKey: 'memories',
      memoryPrefix: 'memory:',
      ttl: 30 * 60 * 1000, // 30分钟
      namespace: 'memory_storage'
    };

    // 锁配置
    this.lockConfig = {
      timeout: 10000, // 10秒
      ttl: 30000, // 30秒
      retryInterval: 100 // 100毫秒
    };

    // 事件监听器
    this.changeListeners = new Set();
  }

  /**
   * 获取所有记忆
   * @param {boolean} [includeDeleted=false] - 是否包含已删除的记忆
   * @returns {Promise<Memory[]>} 记忆数组
   */
  async getMemories(includeDeleted = false) {
    try {
      // 尝试从缓存获取
      const cacheKey = `${this.cacheConfig.memoriesKey}:${includeDeleted ? 'all' : 'active'}`;
      let memories = await this.cacheService.get(cacheKey);

      if (!memories) {
        // 从存储获取
        const result = await this.storageRepository.get(['memories']);
        memories = result.memories || [];

        // 缓存结果
        await this.cacheService.set(cacheKey, memories, {
          ttl: this.cacheConfig.ttl,
          namespace: this.cacheConfig.namespace
        });
      }

      // 过滤已删除的记忆
      if (!includeDeleted) {
        memories = memories.filter(memory => !memory.isDeleted);
      }

      return memories;
    } catch (error) {
      console.error('获取记忆失败:', error);
      throw new Error(`获取记忆失败: ${error.message}`);
    }
  }

  /**
   * 根据ID获取单个记忆
   * @param {string} id - 记忆ID
   * @returns {Promise<Memory|null>} 记忆对象，不存在时返回null
   */
  async getMemory(id) {
    try {
      // 尝试从缓存获取
      const cacheKey = `${this.cacheConfig.memoryPrefix}${id}`;
      let memory = await this.cacheService.get(cacheKey);

      if (!memory) {
        // 从存储获取所有记忆并查找
        const memories = await this.getMemories(true); // 包含已删除的记忆
        memory = memories.find(m => m.id === id) || null;

        // 缓存结果
        if (memory) {
          await this.cacheService.set(cacheKey, memory, {
            ttl: this.cacheConfig.ttl,
            namespace: this.cacheConfig.namespace
          });
        }
      }

      return memory;
    } catch (error) {
      console.error('获取记忆失败:', error);
      throw new Error(`获取记忆失败: ${error.message}`);
    }
  }

  /**
   * 批量获取记忆
   * @param {string[]} ids - 记忆ID数组
   * @returns {Promise<Memory[]>} 记忆数组
   */
  async getMemoriesBatch(ids) {
    try {
      const memories = [];
      const missingIds = [];

      // 尝试从缓存获取
      for (const id of ids) {
        const cacheKey = `${this.cacheConfig.memoryPrefix}${id}`;
        const memory = await this.cacheService.get(cacheKey);
        if (memory) {
          memories.push(memory);
        } else {
          missingIds.push(id);
        }
      }

      // 从存储获取缺失的记忆
      if (missingIds.length > 0) {
        const allMemories = await this.getMemories(true);
        for (const id of missingIds) {
          const memory = allMemories.find(m => m.id === id);
          if (memory) {
            memories.push(memory);
            // 缓存记忆
            const cacheKey = `${this.cacheConfig.memoryPrefix}${id}`;
            await this.cacheService.set(cacheKey, memory, {
              ttl: this.cacheConfig.ttl,
              namespace: this.cacheConfig.namespace
            });
          }
        }
      }

      return memories;
    } catch (error) {
      console.error('批量获取记忆失败:', error);
      throw new Error(`批量获取记忆失败: ${error.message}`);
    }
  }

  /**
   * 添加新记忆
   * @param {CreateMemoryRequest} memoryData - 记忆数据
   * @returns {Promise<Memory>} 创建的记忆对象
   */
  async addMemory(memoryData) {
    // 验证数据
    const validation = Memory.validate(memoryData);
    if (!validation.isValid) {
      throw new Error(`记忆数据验证失败: ${validation.errors.join(', ')}`);
    }

    const lockKey = 'memories';
    const token = await this.lockService.acquire(lockKey, this.lockConfig);

    try {
      // 获取当前记忆列表
      const memories = await this.getMemories(true);

      // 创建新记忆
      const newMemory = Memory.create(memoryData, this.deviceId);

      // 添加到列表
      const updatedMemories = [...memories, newMemory];

      // 保存到存储
      await this.storageRepository.set({ memories: updatedMemories });

      // 更新缓存
      await this._updateMemoryCache(newMemory);
      await this._invalidateMemoriesCache();

      // 触发变化事件
      this._notifyChange('add', newMemory);

      console.log(`记忆添加成功: ${newMemory.id}`);
      return newMemory;
    } finally {
      await this.lockService.release(lockKey, token);
    }
  }

  /**
   * 更新记忆
   * @param {string} id - 记忆ID
   * @param {UpdateMemoryRequest} updates - 更新数据
   * @returns {Promise<Memory>} 更新后的记忆对象
   */
  async updateMemory(id, updates) {
    // 先获取原始记忆以进行完整验证
    const originalMemory = await this.getMemory(id);
    if (!originalMemory) {
      throw new Error(`找不到ID为 ${id} 的记忆`);
    }

    // 合并更新数据进行验证
    const mergedData = { ...originalMemory, ...updates };
    const validation = Memory.validate(mergedData);
    if (!validation.isValid) {
      throw new Error(`更新数据验证失败: ${validation.errors.join(', ')}`);
    }

    const lockKey = 'memories';
    const token = await this.lockService.acquire(lockKey, this.lockConfig);

    try {
      // 获取当前记忆列表
      const memories = await this.getMemories(true);

      // 查找要更新的记忆
      const memoryIndex = memories.findIndex(m => m.id === id);
      if (memoryIndex === -1) {
        throw new Error(`找不到ID为 ${id} 的记忆`);
      }

      if (originalMemory.isDeleted) {
        throw new Error('不能更新已删除的记忆');
      }

      // 更新记忆
      const updatedMemory = Memory.update(originalMemory, updates, this.deviceId);
      memories[memoryIndex] = updatedMemory;

      // 保存到存储
      await this.storageRepository.set({ memories });

      // 更新缓存
      await this._updateMemoryCache(updatedMemory);
      await this._invalidateMemoriesCache();

      // 触发变化事件
      this._notifyChange('update', updatedMemory, originalMemory);

      console.log(`记忆更新成功: ${id}`);
      return updatedMemory;
    } finally {
      await this.lockService.release(lockKey, token);
    }
  }

  /**
   * 删除记忆（软删除）
   * @param {string} id - 记忆ID
   * @returns {Promise<void>}
   */
  async deleteMemory(id) {
    const lockKey = 'memories';
    const token = await this.lockService.acquire(lockKey, this.lockConfig);

    try {
      // 获取当前记忆列表
      const memories = await this.getMemories(true);

      // 查找要删除的记忆
      const memoryIndex = memories.findIndex(m => m.id === id);
      if (memoryIndex === -1) {
        console.warn(`要删除的记忆 ${id} 不存在`);
        return;
      }

      const originalMemory = memories[memoryIndex];
      if (originalMemory.isDeleted) {
        console.warn(`记忆 ${id} 已经被删除`);
        return;
      }

      // 软删除记忆
      const deletedMemory = Memory.softDelete(originalMemory, this.deviceId);
      memories[memoryIndex] = deletedMemory;

      // 保存到存储
      await this.storageRepository.set({ memories });

      // 更新缓存
      await this._updateMemoryCache(deletedMemory);
      await this._invalidateMemoriesCache();

      // 触发变化事件
      this._notifyChange('delete', deletedMemory, originalMemory);

      console.log(`记忆删除成功: ${id}`);
    } finally {
      await this.lockService.release(lockKey, token);
    }
  }

  /**
   * 搜索记忆
   * @param {MemorySearchQuery} query - 搜索条件
   * @returns {Promise<Memory[]>} 匹配的记忆数组
   */
  async searchMemories(query) {
    try {
      // 获取所有记忆
      const memories = await this.getMemories(false);

      // 过滤匹配的记忆
      let results = memories.filter(memory => Memory.matchesQuery(memory, query));

      // 排序
      if (query.sortBy || query.sortOrder) {
        results = Memory.sort(results, query.sortBy, query.sortOrder);
      }

      // 分页
      if (query.offset || query.limit) {
        const offset = query.offset || 0;
        const limit = query.limit || results.length;
        results = results.slice(offset, offset + limit);
      }

      return results;
    } catch (error) {
      console.error('搜索记忆失败:', error);
      throw new Error(`搜索记忆失败: ${error.message}`);
    }
  }

  /**
   * 更新记忆缓存
   * @param {Memory} memory - 记忆对象
   * @private
   */
  async _updateMemoryCache(memory) {
    const cacheKey = `${this.cacheConfig.memoryPrefix}${memory.id}`;
    await this.cacheService.set(cacheKey, memory, {
      ttl: this.cacheConfig.ttl,
      namespace: this.cacheConfig.namespace
    });
  }

  /**
   * 清除记忆列表缓存
   * @private
   */
  async _invalidateMemoriesCache() {
    await this.cacheService.delete(`${this.cacheConfig.memoriesKey}:all`);
    await this.cacheService.delete(`${this.cacheConfig.memoriesKey}:active`);
  }

  /**
   * 通知变化事件
   * @param {string} type - 变化类型
   * @param {Memory} memory - 记忆对象
   * @param {Memory} [originalMemory] - 原始记忆对象
   * @private
   */
  _notifyChange(type, memory, originalMemory) {
    const event = { type, memory, originalMemory, timestamp: new Date().toISOString() };
    this.changeListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('记忆变化监听器执行失败:', error);
      }
    });
  }

  /**
   * 监听记忆变化
   * @param {Function} callback - 变化回调函数
   * @returns {Function} 取消监听的函数
   */
  onMemoryChanged(callback) {
    this.changeListeners.add(callback);
    return () => this.changeListeners.delete(callback);
  }

  /**
   * 检查服务是否可用
   * @returns {Promise<boolean>} 是否可用
   */
  async isAvailable() {
    try {
      const storageAvailable = await this.storageRepository.isAvailable();
      const cacheAvailable = await this.cacheService.isAvailable();
      const lockAvailable = await this.lockService.isAvailable();
      return storageAvailable && cacheAvailable && lockAvailable;
    } catch (error) {
      console.error('检查服务可用性失败:', error);
      return false;
    }
  }

  /**
   * 永久删除记忆
   * @param {string} id - 记忆ID
   * @returns {Promise<void>}
   */
  async permanentDeleteMemory(id) {
    const lockKey = 'memories';
    const token = await this.lockService.acquire(lockKey, this.lockConfig);

    try {
      // 获取当前记忆列表
      const memories = await this.getMemories(true);

      // 查找要删除的记忆
      const memoryIndex = memories.findIndex(m => m.id === id);
      if (memoryIndex === -1) {
        console.warn(`要永久删除的记忆 ${id} 不存在`);
        return;
      }

      const originalMemory = memories[memoryIndex];

      // 从列表中移除
      memories.splice(memoryIndex, 1);

      // 保存到存储
      await this.storageRepository.set({ memories });

      // 清除缓存
      await this.cacheService.delete(`${this.cacheConfig.memoryPrefix}${id}`);
      await this._invalidateMemoriesCache();

      // 触发变化事件
      this._notifyChange('permanentDelete', originalMemory);

      console.log(`记忆永久删除成功: ${id}`);
    } finally {
      await this.lockService.release(lockKey, token);
    }
  }

  /**
   * 恢复已删除的记忆
   * @param {string} id - 记忆ID
   * @returns {Promise<Memory>} 恢复的记忆对象
   */
  async restoreMemory(id) {
    const lockKey = 'memories';
    const token = await this.lockService.acquire(lockKey, this.lockConfig);

    try {
      // 获取当前记忆列表
      const memories = await this.getMemories(true);

      // 查找要恢复的记忆
      const memoryIndex = memories.findIndex(m => m.id === id);
      if (memoryIndex === -1) {
        throw new Error(`找不到ID为 ${id} 的记忆`);
      }

      const originalMemory = memories[memoryIndex];
      if (!originalMemory.isDeleted) {
        throw new Error('记忆未被删除，无需恢复');
      }

      // 恢复记忆
      const restoredMemory = {
        ...originalMemory,
        isDeleted: false,
        deletedAt: undefined,
        deletedBy: undefined,
        version: (originalMemory.version || 0) + 1,
        lastModified: new Date().toISOString(),
        lastModifiedBy: this.deviceId
      };

      memories[memoryIndex] = restoredMemory;

      // 保存到存储
      await this.storageRepository.set({ memories });

      // 更新缓存
      await this._updateMemoryCache(restoredMemory);
      await this._invalidateMemoriesCache();

      // 触发变化事件
      this._notifyChange('restore', restoredMemory, originalMemory);

      console.log(`记忆恢复成功: ${id}`);
      return restoredMemory;
    } finally {
      await this.lockService.release(lockKey, token);
    }
  }

  /**
   * 根据分类获取记忆
   * @param {string} categoryId - 分类ID
   * @returns {Promise<Memory[]>} 该分类下的记忆数组
   */
  async getMemoriesByCategory(categoryId) {
    const memories = await this.getMemories(false);
    return memories.filter(memory => memory.category === categoryId);
  }

  /**
   * 根据标签获取记忆
   * @param {string[]} tags - 标签数组
   * @returns {Promise<Memory[]>} 包含指定标签的记忆数组
   */
  async getMemoriesByTags(tags) {
    const memories = await this.getMemories(false);
    return memories.filter(memory =>
      tags.some(tag => memory.tags.includes(tag))
    );
  }

  /**
   * 批量保存记忆
   * @param {Memory[]} memories - 记忆数组
   * @returns {Promise<BatchOperationResult>} 批量操作结果
   */
  async saveMemories(memories) {
    const lockKey = 'memories';
    const token = await this.lockService.acquire(lockKey, this.lockConfig);

    try {
      const result = {
        total: memories.length,
        success: 0,
        failed: 0,
        errors: []
      };

      // 验证所有记忆
      for (let i = 0; i < memories.length; i++) {
        const memory = memories[i];
        const validation = Memory.validate(memory);
        if (!validation.isValid) {
          result.failed++;
          result.errors.push({
            id: memory.id || `index_${i}`,
            error: `验证失败: ${validation.errors.join(', ')}`
          });
        }
      }

      // 如果有验证失败的记忆，返回结果
      if (result.failed > 0) {
        return result;
      }

      // 保存到存储
      await this.storageRepository.set({ memories });

      // 更新缓存
      for (const memory of memories) {
        await this._updateMemoryCache(memory);
      }
      await this._invalidateMemoriesCache();

      result.success = memories.length;

      // 触发变化事件
      this._notifyChange('batchSave', memories);

      console.log(`批量保存记忆成功: ${result.success}/${result.total}`);
      return result;
    } finally {
      await this.lockService.release(lockKey, token);
    }
  }

  /**
   * 批量删除记忆
   * @param {string[]} ids - 记忆ID数组
   * @returns {Promise<BatchOperationResult>} 批量操作结果
   */
  async deleteMemories(ids) {
    const lockKey = 'memories';
    const token = await this.lockService.acquire(lockKey, this.lockConfig);

    try {
      const result = {
        total: ids.length,
        success: 0,
        failed: 0,
        errors: []
      };

      // 获取当前记忆列表
      const memories = await this.getMemories(true);

      for (const id of ids) {
        try {
          const memoryIndex = memories.findIndex(m => m.id === id);
          if (memoryIndex === -1) {
            result.failed++;
            result.errors.push({ id, error: '记忆不存在' });
            continue;
          }

          const originalMemory = memories[memoryIndex];
          if (originalMemory.isDeleted) {
            result.failed++;
            result.errors.push({ id, error: '记忆已被删除' });
            continue;
          }

          // 软删除记忆
          const deletedMemory = Memory.softDelete(originalMemory, this.deviceId);
          memories[memoryIndex] = deletedMemory;
          result.success++;
        } catch (error) {
          result.failed++;
          result.errors.push({ id, error: error.message });
        }
      }

      // 保存到存储
      await this.storageRepository.set({ memories });

      // 清除缓存
      await this._invalidateMemoriesCache();

      // 触发变化事件
      this._notifyChange('batchDelete', ids);

      console.log(`批量删除记忆完成: ${result.success}/${result.total}`);
      return result;
    } finally {
      await this.lockService.release(lockKey, token);
    }
  }

  /**
   * 获取记忆版本
   * @param {string} id - 记忆ID
   * @returns {Promise<number>} 记忆版本号
   */
  async getMemoryVersion(id) {
    const memory = await this.getMemory(id);
    return memory ? memory.version || 1 : 0;
  }

  /**
   * 获取记忆版本历史
   * @param {string} id - 记忆ID
   * @returns {Promise<MemoryVersion[]>} 版本历史数组
   */
  async getMemoryHistory(id) {
    // 注意：这是一个简化实现，实际项目中可能需要单独的版本历史存储
    const memory = await this.getMemory(id);
    if (!memory) {
      return [];
    }

    return [{
      version: memory.version || 1,
      modifiedAt: memory.lastModified,
      modifiedBy: memory.lastModifiedBy,
      changes: { current: true }
    }];
  }

  /**
   * 获取记忆统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getMemoryStats() {
    try {
      const allMemories = await this.getMemories(true);
      const activeMemories = allMemories.filter(m => !m.isDeleted);
      const deletedMemories = allMemories.filter(m => m.isDeleted);

      // 按分类统计
      const categoryStats = {};
      activeMemories.forEach(memory => {
        const category = memory.category || 'uncategorized';
        categoryStats[category] = (categoryStats[category] || 0) + 1;
      });

      // 按标签统计
      const tagStats = {};
      activeMemories.forEach(memory => {
        memory.tags.forEach(tag => {
          tagStats[tag] = (tagStats[tag] || 0) + 1;
        });
      });

      return {
        total: allMemories.length,
        active: activeMemories.length,
        deleted: deletedMemories.length,
        categories: categoryStats,
        tags: tagStats,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('获取记忆统计失败:', error);
      throw new Error(`获取记忆统计失败: ${error.message}`);
    }
  }

  /**
   * 清空所有记忆
   * @param {boolean} [createBackup=true] - 是否创建备份
   * @returns {Promise<Object>} 备份数据（如果创建备份）
   */
  async clearMemories(createBackup = true) {
    const lockKey = 'memories';
    const token = await this.lockService.acquire(lockKey, this.lockConfig);

    try {
      let backup = null;

      if (createBackup) {
        // 创建备份
        const memories = await this.getMemories(true);
        backup = {
          memories,
          timestamp: new Date().toISOString(),
          deviceId: this.deviceId
        };
      }

      // 清空记忆
      await this.storageRepository.set({ memories: [] });

      // 清除缓存
      await this.cacheService.clear();

      // 触发变化事件
      this._notifyChange('clear', null);

      console.log('记忆清空成功');
      return backup;
    } finally {
      await this.lockService.release(lockKey, token);
    }
  }

  /**
   * 检查记忆是否存在
   * @param {string} id - 记忆ID
   * @returns {Promise<boolean>} 是否存在
   */
  async hasMemory(id) {
    const memory = await this.getMemory(id);
    return memory !== null && !memory.isDeleted;
  }

  /**
   * 获取记忆数量
   * @param {boolean} [includeDeleted=false] - 是否包含已删除的记忆
   * @returns {Promise<number>} 记忆数量
   */
  async getMemoryCount(includeDeleted = false) {
    const memories = await this.getMemories(includeDeleted);
    return memories.length;
  }

  /**
   * 导出记忆数据
   * @param {Object} options - 导出选项
   * @param {string[]} [options.ids] - 要导出的记忆ID数组，不指定则导出全部
   * @param {boolean} [options.includeDeleted=false] - 是否包含已删除的记忆
   * @param {string} [options.format='json'] - 导出格式
   * @returns {Promise<Object>} 导出的数据
   */
  async exportMemories(options = {}) {
    try {
      let memories;

      if (options.ids && options.ids.length > 0) {
        // 导出指定的记忆
        memories = await this.getMemoriesBatch(options.ids);
      } else {
        // 导出所有记忆
        memories = await this.getMemories(options.includeDeleted || false);
      }

      const exportData = {
        memories,
        metadata: {
          exportDate: new Date().toISOString(),
          exportedBy: this.deviceId,
          version: '1.0.0',
          format: options.format || 'json',
          total: memories.length
        }
      };

      console.log(`记忆导出成功: ${memories.length} 条记忆`);
      return exportData;
    } catch (error) {
      console.error('导出记忆失败:', error);
      throw new Error(`导出记忆失败: ${error.message}`);
    }
  }

  /**
   * 导入记忆数据
   * @param {Object} data - 要导入的数据
   * @param {Object} options - 导入选项
   * @param {boolean} [options.merge=true] - 是否与现有数据合并
   * @param {boolean} [options.overwrite=false] - 是否覆盖同ID的记忆
   * @returns {Promise<BatchOperationResult>} 导入结果
   */
  async importMemories(data, options = {}) {
    if (!data.memories || !Array.isArray(data.memories)) {
      throw new Error('无效的导入数据格式');
    }

    const lockKey = 'memories';
    const token = await this.lockService.acquire(lockKey, this.lockConfig);

    try {
      const result = {
        total: data.memories.length,
        success: 0,
        failed: 0,
        errors: []
      };

      const currentMemories = await this.getMemories(true);
      const memoryMap = new Map(currentMemories.map(m => [m.id, m]));

      const memoriesToSave = [];

      for (const importMemory of data.memories) {
        try {
          // 验证记忆数据
          const validation = Memory.validate(importMemory);
          if (!validation.isValid) {
            result.failed++;
            result.errors.push({
              id: importMemory.id || 'unknown',
              error: `验证失败: ${validation.errors.join(', ')}`
            });
            continue;
          }

          const existingMemory = memoryMap.get(importMemory.id);

          if (existingMemory) {
            if (options.overwrite) {
              // 覆盖现有记忆
              const updatedMemory = {
                ...importMemory,
                version: (existingMemory.version || 0) + 1,
                lastModified: new Date().toISOString(),
                lastModifiedBy: this.deviceId
              };
              memoriesToSave.push(updatedMemory);
              memoryMap.set(importMemory.id, updatedMemory);
              result.success++;
            } else if (options.merge) {
              // 跳过已存在的记忆
              result.failed++;
              result.errors.push({
                id: importMemory.id,
                error: '记忆已存在，跳过导入'
              });
            }
          } else {
            // 新记忆
            const newMemory = {
              ...importMemory,
              version: importMemory.version || 1,
              lastModified: new Date().toISOString(),
              lastModifiedBy: this.deviceId
            };
            memoriesToSave.push(newMemory);
            memoryMap.set(importMemory.id, newMemory);
            result.success++;
          }
        } catch (error) {
          result.failed++;
          result.errors.push({
            id: importMemory.id || 'unknown',
            error: error.message
          });
        }
      }

      // 保存更新后的记忆列表
      const finalMemories = Array.from(memoryMap.values());
      await this.storageRepository.set({ memories: finalMemories });

      // 清除缓存
      await this._invalidateMemoriesCache();

      // 触发变化事件
      this._notifyChange('import', memoriesToSave);

      console.log(`记忆导入完成: ${result.success}/${result.total}`);
      return result;
    } finally {
      await this.lockService.release(lockKey, token);
    }
  }

  /**
   * 同步记忆数据
   * @param {Object} options - 同步选项
   * @returns {Promise<Object>} 同步结果
   */
  async syncMemories(options = {}) {
    // 这是一个简化的同步实现
    // 实际项目中可能需要与云存储服务集成
    try {
      const memories = await this.getMemories(true);

      const syncResult = {
        timestamp: new Date().toISOString(),
        deviceId: this.deviceId,
        totalMemories: memories.length,
        lastSyncVersion: Math.max(...memories.map(m => m.version || 1), 0),
        status: 'success'
      };

      console.log('记忆同步完成:', syncResult);
      return syncResult;
    } catch (error) {
      console.error('记忆同步失败:', error);
      throw new Error(`记忆同步失败: ${error.message}`);
    }
  }

  /**
   * 获取服务类型
   * @returns {string} 服务类型
   */
  getType() {
    return 'MemoryStorageService';
  }
}

export default MemoryStorageService;
