/**
 * 记忆存储服务接口
 * 定义了记忆数据管理的统一接口
 */

/**
 * 记忆存储服务接口
 * 提供记忆的CRUD操作、搜索、批量处理等功能
 */
export class IMemoryStorageService {
  /**
   * 获取所有记忆
   * @param {boolean} [includeDeleted=false] - 是否包含已删除的记忆
   * @returns {Promise<Memory[]>} 记忆数组
   */
  async getMemories(includeDeleted = false) {
    throw new Error('Method getMemories() must be implemented by subclass');
  }

  /**
   * 根据ID获取单个记忆
   * @param {string} id - 记忆ID
   * @returns {Promise<Memory|null>} 记忆对象，不存在时返回null
   */
  async getMemory(id) {
    throw new Error('Method getMemory() must be implemented by subclass');
  }

  /**
   * 批量获取记忆
   * @param {string[]} ids - 记忆ID数组
   * @returns {Promise<Memory[]>} 记忆数组
   */
  async getMemoriesBatch(ids) {
    throw new Error('Method getMemoriesBatch() must be implemented by subclass');
  }

  /**
   * 添加新记忆
   * @param {CreateMemoryRequest} memoryData - 记忆数据
   * @returns {Promise<Memory>} 创建的记忆对象
   */
  async addMemory(memoryData) {
    throw new Error('Method addMemory() must be implemented by subclass');
  }

  /**
   * 更新记忆
   * @param {string} id - 记忆ID
   * @param {UpdateMemoryRequest} updates - 更新数据
   * @returns {Promise<Memory>} 更新后的记忆对象
   */
  async updateMemory(id, updates) {
    throw new Error('Method updateMemory() must be implemented by subclass');
  }

  /**
   * 删除记忆（软删除）
   * @param {string} id - 记忆ID
   * @returns {Promise<void>}
   */
  async deleteMemory(id) {
    throw new Error('Method deleteMemory() must be implemented by subclass');
  }

  /**
   * 永久删除记忆
   * @param {string} id - 记忆ID
   * @returns {Promise<void>}
   */
  async permanentDeleteMemory(id) {
    throw new Error('Method permanentDeleteMemory() must be implemented by subclass');
  }

  /**
   * 恢复已删除的记忆
   * @param {string} id - 记忆ID
   * @returns {Promise<Memory>} 恢复的记忆对象
   */
  async restoreMemory(id) {
    throw new Error('Method restoreMemory() must be implemented by subclass');
  }

  /**
   * 搜索记忆
   * @param {MemorySearchQuery} query - 搜索条件
   * @returns {Promise<Memory[]>} 匹配的记忆数组
   */
  async searchMemories(query) {
    throw new Error('Method searchMemories() must be implemented by subclass');
  }

  /**
   * 根据分类获取记忆
   * @param {string} categoryId - 分类ID
   * @returns {Promise<Memory[]>} 该分类下的记忆数组
   */
  async getMemoriesByCategory(categoryId) {
    throw new Error('Method getMemoriesByCategory() must be implemented by subclass');
  }

  /**
   * 根据标签获取记忆
   * @param {string[]} tags - 标签数组
   * @returns {Promise<Memory[]>} 包含指定标签的记忆数组
   */
  async getMemoriesByTags(tags) {
    throw new Error('Method getMemoriesByTags() must be implemented by subclass');
  }

  /**
   * 批量保存记忆
   * @param {Memory[]} memories - 记忆数组
   * @returns {Promise<BatchOperationResult>} 批量操作结果
   */
  async saveMemories(memories) {
    throw new Error('Method saveMemories() must be implemented by subclass');
  }

  /**
   * 批量删除记忆
   * @param {string[]} ids - 记忆ID数组
   * @returns {Promise<BatchOperationResult>} 批量操作结果
   */
  async deleteMemories(ids) {
    throw new Error('Method deleteMemories() must be implemented by subclass');
  }

  /**
   * 获取记忆版本
   * @param {string} id - 记忆ID
   * @returns {Promise<number>} 记忆版本号
   */
  async getMemoryVersion(id) {
    throw new Error('Method getMemoryVersion() must be implemented by subclass');
  }

  /**
   * 获取记忆版本历史
   * @param {string} id - 记忆ID
   * @returns {Promise<MemoryVersion[]>} 版本历史数组
   */
  async getMemoryHistory(id) {
    throw new Error('Method getMemoryHistory() must be implemented by subclass');
  }

  /**
   * 获取记忆统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getMemoryStats() {
    throw new Error('Method getMemoryStats() must be implemented by subclass');
  }

  /**
   * 清空所有记忆
   * @param {boolean} [createBackup=true] - 是否创建备份
   * @returns {Promise<Object>} 备份数据（如果创建备份）
   */
  async clearMemories(createBackup = true) {
    throw new Error('Method clearMemories() must be implemented by subclass');
  }

  /**
   * 导出记忆数据
   * @param {Object} options - 导出选项
   * @param {string[]} [options.ids] - 要导出的记忆ID数组，不指定则导出全部
   * @param {boolean} [options.includeDeleted=false] - 是否包含已删除的记忆
   * @param {string} [options.format='json'] - 导出格式
   * @returns {Promise<Object>} 导出的数据
   */
  async exportMemories(options = {}) {
    throw new Error('Method exportMemories() must be implemented by subclass');
  }

  /**
   * 导入记忆数据
   * @param {Object} data - 要导入的数据
   * @param {Object} options - 导入选项
   * @param {boolean} [options.merge=true] - 是否与现有数据合并
   * @param {boolean} [options.overwrite=false] - 是否覆盖同ID的记忆
   * @returns {Promise<BatchOperationResult>} 导入结果
   */
  async importMemories(data, options = {}) {
    throw new Error('Method importMemories() must be implemented by subclass');
  }

  /**
   * 检查记忆是否存在
   * @param {string} id - 记忆ID
   * @returns {Promise<boolean>} 是否存在
   */
  async hasMemory(id) {
    throw new Error('Method hasMemory() must be implemented by subclass');
  }

  /**
   * 获取记忆数量
   * @param {boolean} [includeDeleted=false] - 是否包含已删除的记忆
   * @returns {Promise<number>} 记忆数量
   */
  async getMemoryCount(includeDeleted = false) {
    throw new Error('Method getMemoryCount() must be implemented by subclass');
  }

  /**
   * 同步记忆数据
   * @param {Object} options - 同步选项
   * @returns {Promise<Object>} 同步结果
   */
  async syncMemories(options = {}) {
    throw new Error('Method syncMemories() must be implemented by subclass');
  }

  /**
   * 监听记忆变化
   * @param {Function} callback - 变化回调函数
   * @returns {Function} 取消监听的函数
   */
  onMemoryChanged(callback) {
    throw new Error('Method onMemoryChanged() must be implemented by subclass');
  }

  /**
   * 检查服务是否可用
   * @returns {Promise<boolean>} 是否可用
   */
  async isAvailable() {
    throw new Error('Method isAvailable() must be implemented by subclass');
  }

  /**
   * 获取服务类型
   * @returns {string} 服务类型
   */
  getType() {
    throw new Error('Method getType() must be implemented by subclass');
  }
}

export default IMemoryStorageService;
