/**
 * SettingsService 单元测试
 */

import { SettingsService } from '../SettingsService.js';

// Mock依赖
class MockStorageRepository {
  constructor() {
    this.data = {};
    this.available = true;
  }

  async get(keys) {
    if (typeof keys === 'string') {
      return { [keys]: this.data[keys] };
    }
    const result = {};
    keys.forEach(key => {
      if (this.data[key]) result[key] = this.data[key];
    });
    return result;
  }

  async set(items) {
    Object.assign(this.data, items);
  }

  async remove(keys) {
    if (typeof keys === 'string') {
      delete this.data[keys];
    } else {
      keys.forEach(key => delete this.data[key]);
    }
  }

  async clear() {
    this.data = {};
  }

  async isAvailable() {
    return this.available;
  }

  setAvailable(available) {
    this.available = available;
  }

  getData() {
    return { ...this.data };
  }
}

class MockCacheService {
  constructor() {
    this.cache = new Map();
    this.available = true;
  }

  async get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    // 检查过期
    if (item.expiresAt && Date.now() > item.expiresAt) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }

  async set(key, value, options = {}) {
    const item = { value };
    if (options.ttl) {
      item.expiresAt = Date.now() + options.ttl;
    }
    this.cache.set(key, item);
  }

  async delete(key) {
    return this.cache.delete(key);
  }

  async clear() {
    this.cache.clear();
  }

  async isAvailable() {
    return this.available;
  }

  setAvailable(available) {
    this.available = available;
  }
}

class MockLockService {
  constructor() {
    this.locks = new Map();
    this.available = true;
  }

  async acquire(key, options = {}) {
    if (this.locks.has(key)) {
      throw new Error(`Lock already acquired for key: ${key}`);
    }
    
    const token = `token_${Date.now()}_${Math.random()}`;
    this.locks.set(key, { token, acquiredAt: Date.now() });
    return token;
  }

  async release(key, token) {
    const lock = this.locks.get(key);
    if (!lock || lock.token !== token) {
      return false;
    }
    
    this.locks.delete(key);
    return true;
  }

  async isLocked(key) {
    return this.locks.has(key);
  }

  async isAvailable() {
    return this.available;
  }

  setAvailable(available) {
    this.available = available;
  }

  clearLocks() {
    this.locks.clear();
  }
}

describe('SettingsService', () => {
  let settingsService;
  let mockStorage;
  let mockCache;
  let mockLock;
  const deviceId = 'test-device-123';

  beforeEach(() => {
    mockStorage = new MockStorageRepository();
    mockCache = new MockCacheService();
    mockLock = new MockLockService();
    settingsService = new SettingsService(mockStorage, mockCache, mockLock, deviceId);
  });

  afterEach(() => {
    mockLock.clearLocks();
  });

  describe('初始化和默认设置', () => {
    test('应该有默认设置', () => {
      const defaultSettings = settingsService.getDefaultSettings();
      
      expect(defaultSettings).toBeDefined();
      expect(defaultSettings.categories).toBeDefined();
      expect(Array.isArray(defaultSettings.categories)).toBe(true);
      expect(defaultSettings.categories.length).toBeGreaterThan(0);
      expect(defaultSettings.defaultCategory).toBeDefined();
    });

    test('应该能够获取设置（首次使用返回默认设置）', async () => {
      const settings = await settingsService.getSettings();
      
      expect(settings).toBeDefined();
      expect(settings.categories).toBeDefined();
      expect(settings.defaultCategory).toBeDefined();
    });
  });

  describe('设置的CRUD操作', () => {
    test('应该能够保存设置', async () => {
      const testSettings = {
        categories: [
          { id: '1', name: '测试分类', color: '#ff0000', icon: 'TestIcon' }
        ],
        defaultCategory: '1',
        darkMode: true
      };

      await settingsService.saveSettings(testSettings);
      
      const savedSettings = await settingsService.getSettings();
      expect(savedSettings.categories).toEqual(testSettings.categories);
      expect(savedSettings.defaultCategory).toBe(testSettings.defaultCategory);
      expect(savedSettings.darkMode).toBe(testSettings.darkMode);
    });

    test('保存设置时应该更新版本信息', async () => {
      const testSettings = { darkMode: true };
      
      await settingsService.saveSettings(testSettings);
      
      const savedSettings = await settingsService.getSettings();
      expect(savedSettings.dataVersion).toBeDefined();
      expect(savedSettings.lastModified).toBeDefined();
      expect(savedSettings.lastModifiedBy).toBe(deviceId);
    });

    test('应该能够获取特定设置项', async () => {
      const testSettings = { darkMode: true, fontSize: 16 };
      await settingsService.saveSettings(testSettings);
      
      const darkMode = await settingsService.getSetting('darkMode');
      const fontSize = await settingsService.getSetting('fontSize');
      
      expect(darkMode).toBe(true);
      expect(fontSize).toBe(16);
    });

    test('应该能够更新特定设置项', async () => {
      // 先保存初始设置
      await settingsService.saveSettings({ darkMode: false });
      
      // 更新特定设置项
      await settingsService.updateSetting('darkMode', true);
      
      const updatedSettings = await settingsService.getSettings();
      expect(updatedSettings.darkMode).toBe(true);
    });

    test('获取不存在的设置项应该返回undefined', async () => {
      const nonExistentSetting = await settingsService.getSetting('nonExistent');
      expect(nonExistentSetting).toBeUndefined();
    });
  });

  describe('缓存机制', () => {
    test('应该使用缓存提高性能', async () => {
      const testSettings = { darkMode: true };
      await settingsService.saveSettings(testSettings);
      
      // 第一次获取（从存储）
      const settings1 = await settingsService.getSettings();
      
      // 清空存储但保留缓存
      mockStorage.data = {};
      
      // 第二次获取（从缓存）
      const settings2 = await settingsService.getSettings();
      
      expect(settings2.darkMode).toBe(true);
    });

    test('保存设置时应该更新缓存', async () => {
      const testSettings = { darkMode: true };
      await settingsService.saveSettings(testSettings);
      
      // 验证缓存中有数据
      const cachedSettings = await mockCache.get('settings');
      expect(cachedSettings).toBeDefined();
      expect(cachedSettings.darkMode).toBe(true);
    });
  });

  describe('锁机制', () => {
    test('保存设置时应该使用锁', async () => {
      const testSettings = { darkMode: true };
      
      // 模拟并发保存
      const promise1 = settingsService.saveSettings(testSettings);
      const promise2 = settingsService.saveSettings({ darkMode: false });
      
      // 第一个应该成功，第二个应该失败（锁冲突）
      await expect(promise1).resolves.toBeUndefined();
      await expect(promise2).rejects.toThrow();
    });

    test('更新设置时应该使用锁', async () => {
      await settingsService.saveSettings({ darkMode: false });
      
      // 模拟并发更新
      const promise1 = settingsService.updateSetting('darkMode', true);
      const promise2 = settingsService.updateSetting('fontSize', 16);
      
      // 第一个应该成功，第二个应该失败（锁冲突）
      await expect(promise1).resolves.toBeUndefined();
      await expect(promise2).rejects.toThrow();
    });
  });

  describe('分类管理', () => {
    test('应该能够添加分类', async () => {
      const newCategory = {
        id: 'new-cat',
        name: '新分类',
        color: '#00ff00',
        icon: 'NewIcon'
      };

      await settingsService.addCategory(newCategory);
      
      const settings = await settingsService.getSettings();
      const addedCategory = settings.categories.find(cat => cat.id === 'new-cat');
      
      expect(addedCategory).toBeDefined();
      expect(addedCategory.name).toBe('新分类');
    });

    test('应该能够更新分类', async () => {
      // 先添加一个分类
      const category = {
        id: 'test-cat',
        name: '测试分类',
        color: '#ff0000',
        icon: 'TestIcon'
      };
      await settingsService.addCategory(category);
      
      // 更新分类
      const updates = { name: '更新后的分类', color: '#0000ff' };
      await settingsService.updateCategory('test-cat', updates);
      
      const settings = await settingsService.getSettings();
      const updatedCategory = settings.categories.find(cat => cat.id === 'test-cat');
      
      expect(updatedCategory.name).toBe('更新后的分类');
      expect(updatedCategory.color).toBe('#0000ff');
      expect(updatedCategory.icon).toBe('TestIcon'); // 未更新的字段应保持不变
    });

    test('应该能够删除分类', async () => {
      // 先添加一个分类
      const category = {
        id: 'test-cat',
        name: '测试分类',
        color: '#ff0000',
        icon: 'TestIcon'
      };
      await settingsService.addCategory(category);
      
      // 删除分类
      await settingsService.deleteCategory('test-cat');
      
      const settings = await settingsService.getSettings();
      const deletedCategory = settings.categories.find(cat => cat.id === 'test-cat');
      
      expect(deletedCategory).toBeUndefined();
    });

    test('删除不存在的分类应该抛出错误', async () => {
      await expect(
        settingsService.deleteCategory('non-existent')
      ).rejects.toThrow('找不到ID为 non-existent 的分类');
    });

    test('应该能够获取所有分类', async () => {
      const categories = await settingsService.getCategories();
      
      expect(Array.isArray(categories)).toBe(true);
      expect(categories.length).toBeGreaterThan(0);
    });

    test('应该能够根据ID获取分类', async () => {
      const categories = await settingsService.getCategories();
      const firstCategory = categories[0];
      
      const category = await settingsService.getCategory(firstCategory.id);
      
      expect(category).toBeDefined();
      expect(category.id).toBe(firstCategory.id);
    });

    test('获取不存在的分类应该返回null', async () => {
      const category = await settingsService.getCategory('non-existent');
      expect(category).toBeNull();
    });
  });

  describe('数据验证', () => {
    test('添加分类时应该验证数据', async () => {
      // 缺少必需字段
      await expect(
        settingsService.addCategory({ name: '测试' })
      ).rejects.toThrow('分类ID不能为空');

      // 无效的颜色格式
      await expect(
        settingsService.addCategory({
          id: 'test',
          name: '测试',
          color: 'invalid-color',
          icon: 'TestIcon'
        })
      ).rejects.toThrow('颜色格式无效');
    });

    test('更新分类时应该验证数据', async () => {
      // 先添加一个分类
      await settingsService.addCategory({
        id: 'test',
        name: '测试',
        color: '#ff0000',
        icon: 'TestIcon'
      });

      // 无效的更新数据
      await expect(
        settingsService.updateCategory('test', { color: 'invalid' })
      ).rejects.toThrow('颜色格式无效');
    });
  });

  describe('错误处理', () => {
    test('存储不可用时应该抛出错误', async () => {
      mockStorage.setAvailable(false);
      
      await expect(
        settingsService.getSettings()
      ).rejects.toThrow();
    });

    test('缓存不可用时应该仍能正常工作', async () => {
      mockCache.setAvailable(false);
      
      const testSettings = { darkMode: true };
      await settingsService.saveSettings(testSettings);
      
      const settings = await settingsService.getSettings();
      expect(settings.darkMode).toBe(true);
    });

    test('锁服务不可用时应该抛出错误', async () => {
      mockLock.setAvailable(false);
      
      await expect(
        settingsService.saveSettings({ darkMode: true })
      ).rejects.toThrow();
    });
  });

  describe('服务状态', () => {
    test('应该能够检查服务是否可用', async () => {
      expect(await settingsService.isAvailable()).toBe(true);
      
      mockStorage.setAvailable(false);
      expect(await settingsService.isAvailable()).toBe(false);
    });

    test('应该返回正确的服务类型', () => {
      expect(settingsService.getType()).toBe('SettingsService');
    });
  });
});
