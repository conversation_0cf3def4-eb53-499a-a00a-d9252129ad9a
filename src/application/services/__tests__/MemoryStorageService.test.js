/**
 * MemoryStorageService 单元测试
 */

import { MemoryStorageService } from '../MemoryStorageService.js';
import { Memory } from '../../../domain/entities/Memory.js';

// Mock依赖（复用之前的Mock类）
class MockStorageRepository {
  constructor() {
    this.data = {};
    this.available = true;
  }

  async get(keys) {
    if (typeof keys === 'string') {
      return { [keys]: this.data[keys] };
    }
    const result = {};
    keys.forEach(key => {
      if (this.data[key]) result[key] = this.data[key];
    });
    return result;
  }

  async set(items) {
    Object.assign(this.data, items);
  }

  async remove(keys) {
    if (typeof keys === 'string') {
      delete this.data[keys];
    } else {
      keys.forEach(key => delete this.data[key]);
    }
  }

  async clear() {
    this.data = {};
  }

  async isAvailable() {
    return this.available;
  }

  setAvailable(available) {
    this.available = available;
  }

  getData() {
    return { ...this.data };
  }
}

class MockCacheService {
  constructor() {
    this.cache = new Map();
    this.available = true;
  }

  async get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (item.expiresAt && Date.now() > item.expiresAt) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }

  async set(key, value, options = {}) {
    const item = { value };
    if (options.ttl) {
      item.expiresAt = Date.now() + options.ttl;
    }
    this.cache.set(key, item);
  }

  async delete(key) {
    return this.cache.delete(key);
  }

  async clear() {
    this.cache.clear();
  }

  async isAvailable() {
    return this.available;
  }

  setAvailable(available) {
    this.available = available;
  }
}

class MockLockService {
  constructor() {
    this.locks = new Map();
    this.available = true;
  }

  async acquire(key, options = {}) {
    if (this.locks.has(key)) {
      throw new Error(`Lock already acquired for key: ${key}`);
    }
    
    const token = `token_${Date.now()}_${Math.random()}`;
    this.locks.set(key, { token, acquiredAt: Date.now() });
    return token;
  }

  async release(key, token) {
    const lock = this.locks.get(key);
    if (!lock || lock.token !== token) {
      return false;
    }
    
    this.locks.delete(key);
    return true;
  }

  async isLocked(key) {
    return this.locks.has(key);
  }

  async isAvailable() {
    return this.available;
  }

  setAvailable(available) {
    this.available = available;
  }

  clearLocks() {
    this.locks.clear();
  }
}

describe('MemoryStorageService', () => {
  let memoryService;
  let mockStorage;
  let mockCache;
  let mockLock;
  const deviceId = 'test-device-123';

  beforeEach(() => {
    mockStorage = new MockStorageRepository();
    mockCache = new MockCacheService();
    mockLock = new MockLockService();
    memoryService = new MemoryStorageService(mockStorage, mockCache, mockLock, deviceId);
  });

  afterEach(() => {
    mockLock.clearLocks();
  });

  describe('基本CRUD操作', () => {
    test('应该能够添加记忆', async () => {
      const memoryData = {
        title: '测试记忆',
        content: '这是一个测试记忆的内容',
        category: 'test-category',
        tags: ['测试', '单元测试']
      };

      const addedMemory = await memoryService.addMemory(memoryData);

      expect(addedMemory).toBeDefined();
      expect(addedMemory.id).toBeDefined();
      expect(addedMemory.title).toBe(memoryData.title);
      expect(addedMemory.content).toBe(memoryData.content);
      expect(addedMemory.category).toBe(memoryData.category);
      expect(addedMemory.tags).toEqual(memoryData.tags);
      expect(addedMemory.version).toBe(1);
      expect(addedMemory.lastModifiedBy).toBe(deviceId);
    });

    test('应该能够获取所有记忆', async () => {
      // 添加几个记忆
      await memoryService.addMemory({ title: '记忆1', content: '内容1' });
      await memoryService.addMemory({ title: '记忆2', content: '内容2' });

      const memories = await memoryService.getMemories();

      expect(memories).toBeDefined();
      expect(Array.isArray(memories)).toBe(true);
      expect(memories.length).toBe(2);
    });

    test('应该能够根据ID获取记忆', async () => {
      const addedMemory = await memoryService.addMemory({
        title: '测试记忆',
        content: '测试内容'
      });

      const retrievedMemory = await memoryService.getMemory(addedMemory.id);

      expect(retrievedMemory).toBeDefined();
      expect(retrievedMemory.id).toBe(addedMemory.id);
      expect(retrievedMemory.title).toBe(addedMemory.title);
    });

    test('应该能够更新记忆', async () => {
      const addedMemory = await memoryService.addMemory({
        title: '原始标题',
        content: '原始内容'
      });

      const updates = {
        title: '更新后的标题',
        content: '更新后的内容'
      };

      const updatedMemory = await memoryService.updateMemory(addedMemory.id, updates);

      expect(updatedMemory.title).toBe(updates.title);
      expect(updatedMemory.content).toBe(updates.content);
      expect(updatedMemory.version).toBe(2);
      expect(updatedMemory.lastModifiedBy).toBe(deviceId);
    });

    test('应该能够删除记忆（软删除）', async () => {
      const addedMemory = await memoryService.addMemory({
        title: '要删除的记忆',
        content: '内容'
      });

      await memoryService.deleteMemory(addedMemory.id);

      // 获取所有记忆（不包含已删除）
      const activeMemories = await memoryService.getMemories(false);
      expect(activeMemories.find(m => m.id === addedMemory.id)).toBeUndefined();

      // 获取所有记忆（包含已删除）
      const allMemories = await memoryService.getMemories(true);
      const deletedMemory = allMemories.find(m => m.id === addedMemory.id);
      expect(deletedMemory).toBeDefined();
      expect(deletedMemory.isDeleted).toBe(true);
    });

    test('应该能够永久删除记忆', async () => {
      const addedMemory = await memoryService.addMemory({
        title: '要永久删除的记忆',
        content: '内容'
      });

      await memoryService.permanentDeleteMemory(addedMemory.id);

      // 即使包含已删除的记忆也找不到
      const allMemories = await memoryService.getMemories(true);
      expect(allMemories.find(m => m.id === addedMemory.id)).toBeUndefined();
    });

    test('应该能够恢复已删除的记忆', async () => {
      const addedMemory = await memoryService.addMemory({
        title: '要恢复的记忆',
        content: '内容'
      });

      // 先删除
      await memoryService.deleteMemory(addedMemory.id);

      // 再恢复
      const restoredMemory = await memoryService.restoreMemory(addedMemory.id);

      expect(restoredMemory.isDeleted).toBe(false);
      expect(restoredMemory.version).toBe(3); // 创建(1) -> 删除(2) -> 恢复(3)

      // 应该在活跃记忆列表中
      const activeMemories = await memoryService.getMemories(false);
      expect(activeMemories.find(m => m.id === addedMemory.id)).toBeDefined();
    });
  });

  describe('批量操作', () => {
    test('应该能够批量获取记忆', async () => {
      const memory1 = await memoryService.addMemory({ title: '记忆1', content: '内容1' });
      const memory2 = await memoryService.addMemory({ title: '记忆2', content: '内容2' });

      const memories = await memoryService.getMemoriesBatch([memory1.id, memory2.id]);

      expect(memories.length).toBe(2);
      expect(memories.map(m => m.id)).toContain(memory1.id);
      expect(memories.map(m => m.id)).toContain(memory2.id);
    });

    test('应该能够批量保存记忆', async () => {
      const memories = [
        Memory.create({ title: '记忆1', content: '内容1' }, deviceId),
        Memory.create({ title: '记忆2', content: '内容2' }, deviceId)
      ];

      const result = await memoryService.saveMemories(memories);

      expect(result.total).toBe(2);
      expect(result.success).toBe(2);
      expect(result.failed).toBe(0);

      const savedMemories = await memoryService.getMemories();
      expect(savedMemories.length).toBe(2);
    });

    test('应该能够批量删除记忆', async () => {
      const memory1 = await memoryService.addMemory({ title: '记忆1', content: '内容1' });
      const memory2 = await memoryService.addMemory({ title: '记忆2', content: '内容2' });

      const result = await memoryService.deleteMemories([memory1.id, memory2.id]);

      expect(result.total).toBe(2);
      expect(result.success).toBe(2);
      expect(result.failed).toBe(0);

      const activeMemories = await memoryService.getMemories(false);
      expect(activeMemories.length).toBe(0);
    });
  });

  describe('搜索功能', () => {
    beforeEach(async () => {
      // 准备测试数据
      await memoryService.addMemory({
        title: 'JavaScript学习笔记',
        content: '学习JavaScript的基础知识',
        category: 'study',
        tags: ['JavaScript', '编程', '学习']
      });

      await memoryService.addMemory({
        title: 'React项目经验',
        content: '使用React开发项目的经验总结',
        category: 'work',
        tags: ['React', '前端', '项目']
      });

      await memoryService.addMemory({
        title: '生活感悟',
        content: '关于生活的一些思考和感悟',
        category: 'life',
        tags: ['生活', '思考']
      });
    });

    test('应该能够按关键词搜索', async () => {
      const results = await memoryService.searchMemories({
        keyword: 'JavaScript'
      });

      expect(results.length).toBe(1);
      expect(results[0].title).toContain('JavaScript');
    });

    test('应该能够按分类搜索', async () => {
      const results = await memoryService.searchMemories({
        category: 'study'
      });

      expect(results.length).toBe(1);
      expect(results[0].category).toBe('study');
    });

    test('应该能够按标签搜索', async () => {
      const results = await memoryService.searchMemories({
        tags: ['编程']
      });

      expect(results.length).toBe(1);
      expect(results[0].tags).toContain('编程');
    });

    test('应该能够组合搜索条件', async () => {
      const results = await memoryService.searchMemories({
        keyword: 'React',
        category: 'work'
      });

      expect(results.length).toBe(1);
      expect(results[0].title).toContain('React');
      expect(results[0].category).toBe('work');
    });

    test('应该能够分页搜索', async () => {
      const results = await memoryService.searchMemories({
        limit: 2,
        offset: 0
      });

      expect(results.length).toBe(2);
    });
  });

  describe('分类和标签查询', () => {
    beforeEach(async () => {
      await memoryService.addMemory({
        title: '工作记录1',
        content: '内容1',
        category: 'work',
        tags: ['项目', '开发']
      });

      await memoryService.addMemory({
        title: '工作记录2',
        content: '内容2',
        category: 'work',
        tags: ['会议', '讨论']
      });

      await memoryService.addMemory({
        title: '学习笔记',
        content: '内容3',
        category: 'study',
        tags: ['学习', '笔记']
      });
    });

    test('应该能够按分类获取记忆', async () => {
      const workMemories = await memoryService.getMemoriesByCategory('work');
      expect(workMemories.length).toBe(2);
      workMemories.forEach(memory => {
        expect(memory.category).toBe('work');
      });
    });

    test('应该能够按标签获取记忆', async () => {
      const projectMemories = await memoryService.getMemoriesByTags(['项目']);
      expect(projectMemories.length).toBe(1);
      expect(projectMemories[0].tags).toContain('项目');
    });
  });

  describe('缓存机制', () => {
    test('应该使用缓存提高性能', async () => {
      const memory = await memoryService.addMemory({
        title: '缓存测试',
        content: '测试缓存机制'
      });

      // 第一次获取（从存储）
      const memory1 = await memoryService.getMemory(memory.id);

      // 清空存储但保留缓存
      mockStorage.data = {};

      // 第二次获取（从缓存）
      const memory2 = await memoryService.getMemory(memory.id);

      expect(memory2).toBeDefined();
      expect(memory2.id).toBe(memory.id);
    });
  });

  describe('锁机制', () => {
    test('添加记忆时应该使用锁', async () => {
      const memoryData = { title: '测试', content: '内容' };

      // 模拟并发添加
      const promise1 = memoryService.addMemory(memoryData);
      const promise2 = memoryService.addMemory(memoryData);

      // 第一个应该成功，第二个应该失败（锁冲突）
      await expect(promise1).resolves.toBeDefined();
      await expect(promise2).rejects.toThrow();
    });
  });

  describe('数据验证', () => {
    test('添加记忆时应该验证数据', async () => {
      // 缺少标题
      await expect(
        memoryService.addMemory({ content: '内容' })
      ).rejects.toThrow('标题不能为空');

      // 缺少内容
      await expect(
        memoryService.addMemory({ title: '标题' })
      ).rejects.toThrow('内容不能为空');
    });

    test('更新记忆时应该验证数据', async () => {
      const memory = await memoryService.addMemory({
        title: '测试',
        content: '内容'
      });

      // 无效的更新数据
      await expect(
        memoryService.updateMemory(memory.id, { title: '' })
      ).rejects.toThrow('标题不能为空');
    });
  });

  describe('统计功能', () => {
    beforeEach(async () => {
      await memoryService.addMemory({
        title: '记忆1',
        content: '内容1',
        category: 'work',
        tags: ['项目']
      });

      await memoryService.addMemory({
        title: '记忆2',
        content: '内容2',
        category: 'study',
        tags: ['学习']
      });

      // 添加一个已删除的记忆
      const memory3 = await memoryService.addMemory({
        title: '记忆3',
        content: '内容3'
      });
      await memoryService.deleteMemory(memory3.id);
    });

    test('应该能够获取记忆统计信息', async () => {
      const stats = await memoryService.getMemoryStats();

      expect(stats.total).toBe(3);
      expect(stats.active).toBe(2);
      expect(stats.deleted).toBe(1);
      expect(stats.categories.work).toBe(1);
      expect(stats.categories.study).toBe(1);
      expect(stats.tags['项目']).toBe(1);
      expect(stats.tags['学习']).toBe(1);
    });

    test('应该能够获取记忆数量', async () => {
      const activeCount = await memoryService.getMemoryCount(false);
      const totalCount = await memoryService.getMemoryCount(true);

      expect(activeCount).toBe(2);
      expect(totalCount).toBe(3);
    });

    test('应该能够检查记忆是否存在', async () => {
      const memory = await memoryService.addMemory({
        title: '存在测试',
        content: '内容'
      });

      expect(await memoryService.hasMemory(memory.id)).toBe(true);
      expect(await memoryService.hasMemory('non-existent')).toBe(false);

      // 删除后应该返回false
      await memoryService.deleteMemory(memory.id);
      expect(await memoryService.hasMemory(memory.id)).toBe(false);
    });
  });

  describe('事件监听', () => {
    test('应该能够监听记忆变化', async () => {
      const events = [];
      const unsubscribe = memoryService.onMemoryChanged((event) => {
        events.push(event);
      });

      // 添加记忆
      const memory = await memoryService.addMemory({
        title: '事件测试',
        content: '内容'
      });

      // 更新记忆
      await memoryService.updateMemory(memory.id, { title: '更新后的标题' });

      // 删除记忆
      await memoryService.deleteMemory(memory.id);

      expect(events.length).toBe(3);
      expect(events[0].type).toBe('add');
      expect(events[1].type).toBe('update');
      expect(events[2].type).toBe('delete');

      unsubscribe();
    });
  });

  describe('服务状态', () => {
    test('应该能够检查服务是否可用', async () => {
      expect(await memoryService.isAvailable()).toBe(true);

      mockStorage.setAvailable(false);
      expect(await memoryService.isAvailable()).toBe(false);
    });

    test('应该返回正确的服务类型', () => {
      expect(memoryService.getType()).toBe('MemoryStorageService');
    });
  });
});
