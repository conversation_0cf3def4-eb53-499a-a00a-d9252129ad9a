import React, { useState, useEffect } from 'react';
import { Layout, Typography, Menu, Card, Result, theme, Form, Select, Button, Divider, Space, Input, message, Switch, Radio, Slider, Upload, Modal, Popconfirm, Alert, Empty } from 'antd';
import {
  CloudOutlined,
  LockOutlined,
  SaveOutlined,
  CheckCircleOutlined,
  GlobalOutlined,
  DatabaseOutlined,
  BgColorsOutlined,
  ImportOutlined,
  ExportOutlined,
  DeleteOutlined,
  UploadOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  TagsOutlined,
  PlusOutlined,
  EditOutlined,
  BulbOutlined,
  HeartOutlined,
  StarOutlined,
  BookOutlined,
  FlagOutlined,
  TagOutlined,
  TrophyOutlined,
  SmileOutlined,
  HomeOutlined,
  ShopOutlined,
  UserOutlined,
  SyncOutlined,
  ArrowLeftOutlined,
  CloudSyncOutlined
} from '@ant-design/icons';
import { storageService, cloudStorageService, EncryptionService, ossStorageService, incrementalSyncService } from '../services';
import SyncSettingsPanel from '../components/SyncSettingsPanel';
import CategorySettings from "../components/CategoryManager";
import CloudStorageSettings from '../components/CloudStorageSettings';
import DataMigrationPage from '../components/DataMigrationPage';
import ConfigurationManager from '../components/ConfigurationManager';

const { Header, Content, Footer, Sider } = Layout;
const { Title, Paragraph } = Typography;
const { useToken } = theme;

// 定义菜单项
const menuItems = [
  {
    key: 'user',
    icon: <UserOutlined />,
    label: '用户信息',
  },
  {
    key: 'cloud',
    icon: <CloudOutlined />,
    label: '云存储设置',
  },
  {
    key: 'sync',
    icon: <SyncOutlined />,
    label: '同步设置',
  },
  /* 暂时不启用
  {
    key: 'security',
    icon: <LockOutlined />,
    label: '加密设置',
  },
   */
  {
    key: 'categories',
    icon: <TagsOutlined />,
    label: '分类设置',
  },
  /* 暂时不启用
  {
    key: 'appearance',
    icon: <BgColorsOutlined />,
    label: '外观设置',
  },
  */
  {
    key: 'data',
    icon: <DatabaseOutlined />,
    label: '数据管理',
  },
  {
    key: 'migration',
    icon: <CloudSyncOutlined />,
    label: '数据迁移',
  },
  {
    key: 'config',
    icon: <ImportOutlined />,
    label: '配置导入导出',
  },
];

const Options = () => {
  const { token } = useToken();
  const [selectedKey, setSelectedKey] = useState('user');
  const [settings, setSettings] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 使用存储服务加载设置
    const loadSettings = async () => {
      try {
        setLoading(true);
        const loadedSettings = await storageService.getSettings();
        setSettings(loadedSettings);
      } catch (error) {
        console.error('加载设置失败:', error);
        message.error('加载设置失败');
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, []);

  const handleMenuClick = (e) => {
    setSelectedKey(e.key);
  };

  const saveSettings = async (updatedSettings) => {
    try {
      const newSettings = { ...settings, ...updatedSettings };
      setSettings(newSettings);

      // 使用存储服务保存设置
      await storageService.saveSettings(newSettings);
      return true;
    } catch (error) {
      console.error('保存设置失败:', error);
      message.error('保存设置失败');
      return false;
    }
  };

  // 加密设置表单
  const SecuritySettings = () => {
    const [form] = Form.useForm();
    const [encryptionEnabled, setEncryptionEnabled] = useState(false);
    const [testLoading, setTestLoading] = useState(false);
    const [passwordVisible, setPasswordVisible] = useState(false);

    useEffect(() => {
      form.setFieldsValue(settings);
      setEncryptionEnabled(settings.encryption || false);
    }, [form, settings]);

    const onFinish = async (values) => {
      const success = await saveSettings(values);
      if (success) {
        message.success('设置已保存');
      }
    };

    const handleEncryptionChange = (checked) => {
      setEncryptionEnabled(checked);
    };

    // 测试加密密钥
    const testEncryptionKey = async () => {
      try {
        setTestLoading(true);
        const values = form.getFieldsValue();

        if (!values.encryptionKey) {
          throw new Error('请输入加密密钥');
        }

        // 使用加密服务测试密钥
        const algorithm = values.encryptionAlgorithm || 'AES-256-GCM';
        const isValid = await EncryptionService.testKey(values.encryptionKey, algorithm);

        if (isValid) {
          message.success(`密钥测试成功！使用 ${algorithm} 算法加密测试数据成功。`);
        } else {
          throw new Error('密钥测试失败');
        }
      } catch (error) {
        message.error('密钥测试失败：' + (error.message || '未知错误'));
      } finally {
        setTestLoading(false);
      }
    };

    return (
      <Card title="加密设置" bordered={false}>
        <Form
          form={form}
          layout="vertical"
          initialValues={settings}
          onFinish={onFinish}
        >
          <Paragraph style={{ marginBottom: 24 }}>
            启用加密可保护您的记忆数据安全。请妥善保管您的密钥，密钥丢失将无法恢复数据。
          </Paragraph>

          <Form.Item
            name="encryption"
            valuePropName="checked"
            label="启用加密"
          >
            <Switch onChange={handleEncryptionChange} />
          </Form.Item>

          <Form.Item
            name="encryptionAlgorithm"
            label="加密算法"
            tooltip="选择用于加密数据的算法"
            rules={[{ required: encryptionEnabled, message: '请选择加密算法' }]}
          >
            <Select disabled={!encryptionEnabled}>
              <Select.Option value="AES-256-GCM">AES-256-GCM (推荐)</Select.Option>
              <Select.Option value="AES-256-CBC">AES-256-CBC</Select.Option>
              <Select.Option value="AES-128-GCM">AES-128-GCM</Select.Option>
              <Select.Option value="AES-128-CBC">AES-128-CBC</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="encryptionKey"
            label="加密密钥"
            tooltip="输入用于加密数据的密钥，请妥善保管"
            rules={[{ required: encryptionEnabled, message: '请输入加密密钥' }]}
          >
            <Input.Password
              disabled={!encryptionEnabled}
              placeholder="输入强密钥，至少 12 个字符"
              visibilityToggle={{ visible: passwordVisible, onVisibleChange: setPasswordVisible }}
            />
          </Form.Item>

          <Form.Item
            name="autoLock"
            valuePropName="checked"
            label="自动锁定"
            tooltip="在一段时间不活动后自动锁定数据"
          >
            <Switch disabled={!encryptionEnabled} />
          </Form.Item>

          <Form.Item
            name="lockTimeout"
            label="锁定超时时间"
            tooltip="设置多长时间不活动后自动锁定"
            dependencies={['autoLock']}
          >
            <Select disabled={!encryptionEnabled || !form.getFieldValue('autoLock')}>
              <Select.Option value={5}>5 分钟</Select.Option>
              <Select.Option value={15}>15 分钟</Select.Option>
              <Select.Option value={30}>30 分钟</Select.Option>
              <Select.Option value={60}>1 小时</Select.Option>
            </Select>
          </Form.Item>

          <Divider />

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SaveOutlined />}
              >
                保存设置
              </Button>
              <Button
                icon={<CheckCircleOutlined />}
                onClick={testEncryptionKey}
                loading={testLoading}
                disabled={!encryptionEnabled}
              >
                测试密钥
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    );
  };

  // 外观设置表单
  const AppearanceSettings = () => {
    const [form] = Form.useForm();
    const [previewColor, setPreviewColor] = useState(settings.primaryColor);
    const [previewTheme, setPreviewTheme] = useState(settings.theme);
    const [darkModeEnabled, setDarkModeEnabled] = useState(settings.darkMode || false);

    useEffect(() => {
      form.setFieldsValue(settings);
      setDarkModeEnabled(settings.darkMode || false);
      setPreviewColor(settings.primaryColor);
      setPreviewTheme(settings.theme);
    }, [form, settings]);

    const onFinish = (values) => {
      saveSettings(values);
      message.success('设置已保存，部分设置可能需要重启扩展生效');
    };

    const handleDarkModeChange = (checked) => {
      setDarkModeEnabled(checked);
    };

    const handleColorChange = (e) => {
      const color = e.target.value;
      setPreviewColor(color);
    };

    const handleThemeChange = (e) => {
      setPreviewTheme(e.target.value);
    };

    // 预览卡片样式
    const getPreviewStyle = () => {
      const baseStyle = {
        padding: '20px',
        borderRadius: '8px',
        marginBottom: '16px',
        transition: 'all 0.3s',
      };

      if (darkModeEnabled) {
        return {
          ...baseStyle,
          backgroundColor: '#1f1f1f',
          color: '#fff',
          border: `1px solid ${previewColor}`,
        };
      }

      return {
        ...baseStyle,
        backgroundColor: '#fff',
        color: '#000',
        border: `1px solid ${previewColor}`,
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
      };
    };

    // 预览按钮样式
    const getButtonStyle = () => {
      return {
        backgroundColor: previewColor,
        borderColor: previewColor,
        color: '#fff',
      };
    };

    return (
      <Card title="外观设置" bordered={false}>
        <Form
          form={form}
          layout="vertical"
          initialValues={settings}
          onFinish={onFinish}
        >
          <Paragraph style={{ marginBottom: 24 }}>
            自定义应用的外观和主题，使其更符合您的偏好。
          </Paragraph>

          <div style={getPreviewStyle()}>
            <h3 style={{ color: previewColor, marginTop: 0 }}>外观预览</h3>
            <p>这是您选择的主题外观预览。</p>
            <Button style={getButtonStyle()}>预览按钮</Button>
          </div>

          <Form.Item
            name="darkMode"
            valuePropName="checked"
            label="暗色模式"
          >
            <Switch onChange={handleDarkModeChange} />
          </Form.Item>

          <Form.Item
            name="theme"
            label="主题"
          >
            <Radio.Group onChange={handleThemeChange}>
              <Space direction="vertical">
                <Radio value="default">默认主题</Radio>
                <Radio value="compact">紧凑主题</Radio>
                <Radio value="colorful">多彩主题</Radio>
              </Space>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            name="primaryColor"
            label="主色调"
            tooltip="输入十六进制颜色代码，例如：#1890ff"
          >
            <Input
              type="color"
              value={previewColor}
              onChange={handleColorChange}
              style={{ width: '100%', height: '32px' }}
            />
          </Form.Item>

          <Form.Item
            name="fontSize"
            label="字体大小"
          >
            <Slider
              min={12}
              max={20}
              marks={{
                12: '12px',
                14: '14px',
                16: '16px',
                18: '18px',
                20: '20px',
              }}
            />
          </Form.Item>

          <Form.Item
            name="compactMode"
            valuePropName="checked"
            label="紧凑模式"
            tooltip="减少空白和间距，显示更多内容"
          >
            <Switch />
          </Form.Item>

          <Divider />

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              icon={<SaveOutlined />}
            >
              保存设置
            </Button>
          </Form.Item>
        </Form>
      </Card>
    );
  };

  // 数据管理组件
  const DataManagementSettings = () => {
    const [exportLoading, setExportLoading] = useState(false);
    const [importLoading, setImportLoading] = useState(false);
    const [clearLoading, setClearLoading] = useState(false);
    const [fileList, setFileList] = useState([]);
    const [previewVisible, setPreviewVisible] = useState(false);
    const [previewContent, setPreviewContent] = useState('');
    const [importedData, setImportedData] = useState(null);
    const [showImportConfirm, setShowImportConfirm] = useState(false);

    // 导出数据
    const exportData = async () => {
      try {
        setExportLoading(true);

        // 使用存储服务导出数据
        const data = await storageService.exportData();

        // 转换为 JSON 字符串
        const jsonString = JSON.stringify(data, null, 2);

        // 创建 Blob 对象
        const blob = new Blob([jsonString], { type: 'application/json' });

        // 创建下载链接
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `memory-keeper-backup-${new Date().toISOString().slice(0, 10)}.json`;

        // 触发下载
        document.body.appendChild(a);
        a.click();

        // 清理
        setTimeout(() => {
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
          message.success('数据导出成功！');
        }, 100);
      } catch (error) {
        console.error('导出数据错误:', error);
        message.error('导出数据失败: ' + error.message);
      } finally {
        setExportLoading(false);
      }
    };

    // 处理文件上传前的预览
    const handlePreview = (file) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const content = e.target.result;
          setPreviewContent(content);
          setPreviewVisible(true);

          // 尝试解析 JSON
          const parsedData = JSON.parse(content);
          setImportedData(parsedData);
        } catch (error) {
          message.error('无法解析文件，请确保是有效的 JSON 格式');
        }
      };
      reader.readAsText(file);
      return false; // 阻止自动上传
    };

    // 处理文件变化
    const handleFileChange = ({ fileList }) => {
      setFileList(fileList);
    };

    // 关闭预览对话框
    const handlePreviewCancel = () => {
      setPreviewVisible(false);
    };

    // 导入数据
    const importData = async () => {
      if (!importedData) {
        message.error('请先选择有效的备份文件');
        return;
      }

      try {
        setImportLoading(true);

        // 使用存储服务导入数据
        await storageService.importData(importedData);

        // 重新加载设置
        const loadedSettings = await storageService.getSettings();
        setSettings(loadedSettings);

        // 清理状态
        setPreviewVisible(false);
        setFileList([]);
        setImportedData(null);
        setShowImportConfirm(false);

        message.success('数据导入成功！');
      } catch (error) {
        console.error('导入数据错误:', error);
        message.error('导入数据失败: ' + error.message);
      } finally {
        setImportLoading(false);
      }
    };

    // 清除所有数据
    const clearAllData = async () => {
      try {
        setClearLoading(true);

        // 使用存储服务清除数据，保留外观设置
        const backup = await storageService.clearData(true);

        // 重新加载设置
        const loadedSettings = await storageService.getSettings();
        setSettings(loadedSettings);

        message.success('数据清除成功，已自动创建备份');
      } catch (error) {
        console.error('清除数据错误:', error);
        message.error('清除数据失败: ' + error.message);
      } finally {
        setClearLoading(false);
      }
    };

    // 恢复自动备份
    const restoreAutoBackup = async () => {
      try {
        // 使用存储服务恢复自动备份
        const backupData = await storageService.restoreAutoBackup();

        // 重新加载设置
        const loadedSettings = await storageService.getSettings();
        setSettings(loadedSettings);

        message.success('备份恢复成功！');
      } catch (error) {
        console.error('恢复备份失败:', error);
        message.error('恢复备份失败: ' + error.message);
      }
    };

    return (
      <Card title="数据管理" bordered={false}>
        <Alert
          message="数据管理说明"
          description="您可以导出所有数据以创建备份，或从备份文件中导入数据。清除数据前会自动创建备份。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />
        <Divider orientation="left">缓存管理</Divider>

        <Space direction="vertical" style={{ width: '100%', marginBottom: 24 }}>
          <Button
            icon={<DatabaseOutlined />}
            onClick={() => window.location.href = 'cache-settings.html'}
          >
            缓存设置
          </Button>

          <Paragraph type="secondary">
            管理缓存设置，优化存储空间和访问速度。
          </Paragraph>
        </Space>

        <Divider orientation="left">数据备份与恢复</Divider>

        <Space direction="vertical" style={{ width: '100%', marginBottom: 24 }}>
          <Space>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={exportData}
              loading={exportLoading}
            >
              导出数据
            </Button>

            <Upload
              beforeUpload={handlePreview}
              onChange={handleFileChange}
              fileList={fileList}
              accept=".json"
              maxCount={1}
            >
              <Button icon={<UploadOutlined />} loading={importLoading}>
                选择备份文件
              </Button>
            </Upload>
          </Space>

          <Paragraph type="secondary">
            导出数据将创建一个包含所有记忆和设置的 JSON 文件。导入数据将覆盖当前的所有数据。
          </Paragraph>
        </Space>

        <Divider orientation="left">数据清除</Divider>

        <Space direction="vertical" style={{ width: '100%', marginBottom: 24 }}>
          <Space>
            <Popconfirm
              title="清除所有数据"
              description="此操作将清除所有记忆和设置，但会保留外观设置。确定继续吗？"
              onConfirm={clearAllData}
              okText="确定"
              cancelText="取消"
              icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
            >
              <Button
                danger
                icon={<DeleteOutlined />}
                loading={clearLoading}
              >
                清除所有数据
              </Button>
            </Popconfirm>

            <Button
              icon={<ImportOutlined />}
              onClick={restoreAutoBackup}
            >
              恢复最近备份
            </Button>
          </Space>

          <Paragraph type="secondary">
            清除数据前会自动创建备份，您可以使用“恢复最近备份”恢复数据。
          </Paragraph>
        </Space>

        {/* 预览对话框 */}
        <Modal
          title="备份文件预览"
          open={previewVisible}
          onCancel={handlePreviewCancel}
          footer={[
            <Button key="back" onClick={handlePreviewCancel}>
              取消
            </Button>,
            <Button
              key="submit"
              type="primary"
              onClick={() => setShowImportConfirm(true)}
              disabled={!importedData}
            >
              导入数据
            </Button>,
          ]}
          width={700}
        >
          <div style={{ maxHeight: '400px', overflow: 'auto', marginBottom: '16px' }}>
            <pre style={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word' }}>
              {previewContent}
            </pre>
          </div>
          {importedData && (
            <div>
              <Divider />
              <Paragraph>
                <strong>备份信息：</strong>
              </Paragraph>
              <ul>
                <li>记忆数量：{importedData.memories?.length || 0}</li>
                <li>备份日期：{new Date(importedData.exportDate).toLocaleString()}</li>
                <li>版本：{importedData.version}</li>
              </ul>
            </div>
          )}
        </Modal>

        {/* 导入确认对话框 */}
        <Modal
          title="确认导入数据"
          open={showImportConfirm}
          onCancel={() => setShowImportConfirm(false)}
          footer={[
            <Button key="back" onClick={() => setShowImportConfirm(false)}>
              取消
            </Button>,
            <Button
              key="submit"
              type="primary"
              onClick={importData}
              loading={importLoading}
            >
              确认导入
            </Button>,
          ]}
        >
          <Alert
            message="警告！"
            description="导入数据将覆盖当前的所有数据。此操作不可撤销。"
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
          {importedData && (
            <div>
              <Paragraph>
                <strong>将导入的数据：</strong>
              </Paragraph>
              <ul>
                <li>记忆数量：{importedData.memories?.length || 0}</li>
                <li>备份日期：{new Date(importedData.exportDate).toLocaleString()}</li>
                <li>版本：{importedData.version}</li>
                {importedData.isAutoBackup && (
                  <li><strong>类型：自动备份</strong></li>
                )}
              </ul>
            </div>
          )}
        </Modal>
      </Card>
    );
  };


   // 用户信息设置组件
  const UserInfoSettings = () => {
    const [form] = Form.useForm();
    const [userId, setUserId] = useState('');
    const [userIdGenerated, setUserIdGenerated] = useState(false);
    const [loading, setLoading] = useState(false);
    const [clearLoading, setClearLoading] = useState(false);
    const [firstTimeSetup, setFirstTimeSetup] = useState(false);

    // 加载用户信息
    useEffect(() => {
      const loadUserInfo = async () => {
        try {
          const settings = await storageService.getSettings();
          if (settings.userInfo) {
            form.setFieldsValue(settings.userInfo);
          }
          if (settings.userId) {
            setUserId(settings.userId);
            setUserIdGenerated(true);
          } else {
            // 如果没有用户ID，说明是首次设置
            setFirstTimeSetup(true);
          }
        } catch (error) {
          console.error('加载用户信息失败:', error);
        }
      };

      loadUserInfo();
    }, [form]);

    // 生成用户ID
    const generateUserId = (userInfo) => {
      // 使用用户名和邮箱生成一致的用户ID
      const { username, email } = userInfo;
      const input = `${username.toLowerCase()}:${(email || '').toLowerCase()}`;

      // 使用简单的哈希算法
      let hash = 0;
      for (let i = 0; i < input.length; i++) {
        const char = input.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
      }

      // 转换为正数并生成固定长度的字符串
      const positiveHash = Math.abs(hash);
      const userId = `user_${positiveHash.toString(16)}`;

      return userId;
    };

    // 清除用户ID
    const clearUserId = async () => {
      try {
        setClearLoading(true);

        // 获取当前设置
        const settings = await storageService.getSettings();

        // 删除用户ID和用户信息
        const updatedSettings = {
          ...settings,
          userId: null,
          userInfo: null
        };

        // 保存更新后的设置
        await storageService.saveSettings(updatedSettings);

        // 重置表单和状态
        form.resetFields();
        setUserId('');
        setUserIdGenerated(false);
        setFirstTimeSetup(true);

        message.success('用户ID已清除，您可以重新设置用户信息');

        // 清除记忆
        await storageService.saveMemories([]);
      } catch (error) {
        console.error('清除用户ID失败:', error);
        message.error('清除用户ID失败');
      } finally {
        setClearLoading(false);
      }
    };

    // 保存用户信息
    const onFinish = async (values) => {
      try {
        setLoading(true);

        // 获取当前设置
        const settings = await storageService.getSettings();

        // 如果还没有用户ID，生成一个
        let currentUserId = settings.userId;
        let updatedUserInfo = values;

        if (!currentUserId) {
          // 首次设置，生成用户ID
          currentUserId = generateUserId(values);
          setUserId(currentUserId);
          setUserIdGenerated(true);
          setFirstTimeSetup(false);
        } else {
          // 已经有用户ID，保留原始的用户名和邮箱
          updatedUserInfo = {
            ...values,
            username: settings.userInfo.username,
            email: settings.userInfo.email
          };
        }

        // 更新设置
        const updatedSettings = {
          ...settings,
          userInfo: updatedUserInfo,
          userId: currentUserId
        };

        // 保存设置
        await storageService.saveSettings(updatedSettings);

        // 更新表单显示的值
        form.setFieldsValue(updatedUserInfo);

        message.success('用户信息已保存');

        // 检查是否需要从云端加载记忆
        const memories = await storageService.getMemories();
        if (memories.length === 0) {
          // 如果没有记忆，尝试从云端加载
          message.info('正在尝试从云端加载记忆...');

          // 发送消息触发记忆加载
          chrome.runtime.sendMessage({ type: 'LOAD_MEMORIES_FROM_CLOUD_REQUESTED' });
        }
      } catch (error) {
        console.error('保存用户信息失败:', error);
        message.error('保存用户信息失败');
      } finally {
        setLoading(false);
      }
    };

    return (
      <Card title="用户信息设置" bordered={false}>
        {firstTimeSetup && (
          <Alert
            message="首次设置"
            description="请设置您的用户信息，这将用于生成您的唯一用户ID。请注意，一旦设置，用户名和邮箱将无法修改。"
            type="info"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        )}

        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
        >
          <Form.Item
            name="username"
            label="用户名"
            tooltip="用户名将用于生成您的唯一ID，设置后无法修改"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="输入用户名" disabled={userIdGenerated} />
          </Form.Item>

          <Form.Item
            name="email"
            label="邮箱"
            tooltip="邮箱将用于生成您的唯一ID，设置后无法修改"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input placeholder="输入邮箱" disabled={userIdGenerated} />
          </Form.Item>

          <Form.Item
            name="nickname"
            label="昵称"
            tooltip="昵称可以随时修改"
          >
            <Input placeholder="输入昵称（可选）" />
          </Form.Item>

          {userIdGenerated && (
            <>
              <Alert
                message="用户名和邮箱已锁定"
                description="为了确保用户ID的一致性，用户名和邮箱已锁定不可修改。您可以修改昵称和其他设置。"
                type="info"
                showIcon
                style={{ marginBottom: '16px' }}
              />
              <Form.Item label="用户ID">
                <Input value={userId} disabled />
                <div style={{ marginTop: '8px', fontSize: '12px', color: '#888' }}>
                  用户ID用于唯一标识您的记忆和设置，即使卸载插件后重新安装，只要使用相同的用户名和邮箱，就会生成相同的用户ID。
                </div>
              </Form.Item>
            </>
          )}

          <Divider />

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SaveOutlined />}
                loading={loading}
              >
                保存用户信息
              </Button>

              {userIdGenerated && (
                <Button
                  icon={<CloudSyncOutlined />}
                  onClick={() => {
                    message.info('正在从云端加载记忆...');
                    chrome.runtime.sendMessage({ type: 'LOAD_MEMORIES_FROM_CLOUD_REQUESTED' });
                  }}
                >
                  从云端加载记忆
                </Button>
              )}

              {userIdGenerated && (
                <Popconfirm
                  title="清除用户ID"
                  description="清除用户ID将允许您重新设置用户信息。这会导致生成新的用户ID，可能影响您的记忆数据。确定要继续吗？"
                  onConfirm={clearUserId}
                  okText="确定"
                  cancelText="取消"
                  icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
                >
                  <Button danger icon={<DeleteOutlined />} loading={clearLoading}>
                    清除用户ID
                  </Button>
                </Popconfirm>
              )}
            </Space>
          </Form.Item>
        </Form>
      </Card>
    );
  };

  // 根据选择的菜单项渲染对应的内容
  const renderContent = () => {
    switch (selectedKey) {
      case 'user':
        return <UserInfoSettings />;
      case 'cloud':
        return <CloudStorageSettings settings={settings} saveSettings={saveSettings}/>;
      case 'sync':
        // 直接在右侧显示同步设置面板
        return <SyncSettingsPanel />;
      case 'security':
        return <SecuritySettings />;
      case 'categories':
        return <CategorySettings />;
      case 'appearance':
        return <AppearanceSettings />;
      case 'data':
        return <DataManagementSettings />;
      case 'migration':
        return <DataMigrationPage />;
      case 'config':
        return <ConfigurationManager />;
      default:
        return <UserInfoSettings />;
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 24px', display: 'flex', alignItems: 'center' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <img src="../icons/icon48.png" alt="Logo" style={{ width: 24, height: 24, marginRight: 8 }} />
          <Title level={3} style={{ margin: '0' }}>设置</Title>
        </div>
      </Header>
      <Layout>
        <Sider
          width={200}
          style={{
            background: token.colorBgContainer,
            borderRight: `1px solid ${token.colorBorderSecondary}`,
            overflow: 'auto',
            height: 'calc(100vh - 64px - 70px)', // 减去 header 和 footer 的高度
          }}
        >
          <Menu
            mode="inline"
            selectedKeys={[selectedKey]}
            style={{ height: '100%', borderRight: 0 }}
            items={menuItems}
            onClick={handleMenuClick}
          />
        </Sider>
        <Layout style={{ padding: '24px' }}>
          <Content
            style={{
              padding: 24,
              margin: 0,
              background: token.colorBgContainer,
              borderRadius: token.borderRadiusLG,
              minHeight: 280,
            }}
          >
            {renderContent()}
          </Content>
        </Layout>
      </Layout>
      <Footer style={{ textAlign: 'center', padding: '24px' }}>
        拾光忆栈 ©{new Date().getFullYear()}
      </Footer>
    </Layout>
  );
};

export default Options;
