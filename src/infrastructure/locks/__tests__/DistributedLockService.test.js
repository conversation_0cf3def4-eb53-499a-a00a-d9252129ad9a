/**
 * DistributedLockService 单元测试
 */

import { DistributedLockService } from '../DistributedLockService.js';

// Mock IStorageRepository
class MockStorageRepository {
  constructor() {
    this.data = {};
    this.available = true;
  }

  async get(keys) {
    if (typeof keys === 'string') {
      return { [keys]: this.data[keys] };
    }
    
    if (Array.isArray(keys)) {
      const result = {};
      keys.forEach(key => {
        if (this.data[key]) {
          result[key] = this.data[key];
        }
      });
      return result;
    }

    return {};
  }

  async set(items) {
    Object.assign(this.data, items);
  }

  async remove(keys) {
    if (typeof keys === 'string') {
      delete this.data[keys];
    } else if (Array.isArray(keys)) {
      keys.forEach(key => delete this.data[key]);
    }
  }

  async clear() {
    this.data = {};
  }

  async has(key) {
    return key in this.data;
  }

  async keys() {
    return Object.keys(this.data);
  }

  async isAvailable() {
    return this.available;
  }

  getType() {
    return 'mock';
  }

  // 测试辅助方法
  setAvailable(available) {
    this.available = available;
  }

  getData() {
    return { ...this.data };
  }
}

describe('DistributedLockService', () => {
  let lockService;
  let mockStorage;

  beforeEach(() => {
    mockStorage = new MockStorageRepository();
    lockService = new DistributedLockService(mockStorage);
    
    // 停止清理定时器以避免测试干扰
    lockService.stopCleanupTimer();
  });

  afterEach(() => {
    lockService.destroy();
  });

  describe('基本锁操作', () => {
    test('应该能够获取锁', async () => {
      const token = await lockService.acquire('test-key');
      
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.length).toBeGreaterThan(0);
    });

    test('应该能够释放锁', async () => {
      const token = await lockService.acquire('test-key');
      const released = await lockService.release('test-key', token);
      
      expect(released).toBe(true);
    });

    test('应该能够检查锁是否存在', async () => {
      // 锁不存在时
      let isLocked = await lockService.isLocked('test-key');
      expect(isLocked).toBe(false);

      // 获取锁后
      const token = await lockService.acquire('test-key');
      isLocked = await lockService.isLocked('test-key');
      expect(isLocked).toBe(true);

      // 释放锁后
      await lockService.release('test-key', token);
      isLocked = await lockService.isLocked('test-key');
      expect(isLocked).toBe(false);
    });

    test('应该能够获取锁信息', async () => {
      const token = await lockService.acquire('test-key', { ttl: 10000 });
      const lockInfo = await lockService.getLockInfo('test-key');
      
      expect(lockInfo).toBeDefined();
      expect(lockInfo.token).toBe(token);
      expect(lockInfo.key).toBe('test-key');
      expect(lockInfo.remainingTtl).toBeGreaterThan(0);
      expect(lockInfo.remainingTtl).toBeLessThanOrEqual(10000);
    });
  });

  describe('锁竞争', () => {
    test('同一个键不能被多次获取', async () => {
      const token1 = await lockService.acquire('test-key');
      
      // 尝试再次获取同一个键的锁应该超时
      await expect(
        lockService.acquire('test-key', { timeout: 500 })
      ).rejects.toThrow('Failed to acquire lock');
      
      // 释放锁后应该能够重新获取
      await lockService.release('test-key', token1);
      const token2 = await lockService.acquire('test-key');
      expect(token2).toBeDefined();
      expect(token2).not.toBe(token1);
    });

    test('不同的键可以同时获取锁', async () => {
      const token1 = await lockService.acquire('key1');
      const token2 = await lockService.acquire('key2');
      
      expect(token1).toBeDefined();
      expect(token2).toBeDefined();
      expect(token1).not.toBe(token2);
      
      expect(await lockService.isLocked('key1')).toBe(true);
      expect(await lockService.isLocked('key2')).toBe(true);
    });
  });

  describe('锁超时机制', () => {
    test('锁应该在TTL后自动过期', async () => {
      const token = await lockService.acquire('test-key', { ttl: 100 });
      
      expect(await lockService.isLocked('test-key')).toBe(true);
      
      // 等待锁过期
      await new Promise(resolve => setTimeout(resolve, 150));
      
      expect(await lockService.isLocked('test-key')).toBe(false);
    });

    test('过期的锁应该能够被重新获取', async () => {
      const token1 = await lockService.acquire('test-key', { ttl: 100 });
      
      // 等待锁过期
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // 应该能够获取新的锁
      const token2 = await lockService.acquire('test-key');
      expect(token2).toBeDefined();
      expect(token2).not.toBe(token1);
    });

    test('应该能够续期锁', async () => {
      const token = await lockService.acquire('test-key', { ttl: 100 });
      
      // 等待一段时间
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // 续期锁
      const renewed = await lockService.renew('test-key', token, 200);
      expect(renewed).toBe(true);
      
      // 锁应该仍然有效
      expect(await lockService.isLocked('test-key')).toBe(true);
      
      // 等待原始TTL时间
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 锁应该仍然有效（因为已续期）
      expect(await lockService.isLocked('test-key')).toBe(true);
    });
  });

  describe('错误处理', () => {
    test('释放锁时需要提供令牌', async () => {
      await expect(
        lockService.release('test-key', null)
      ).rejects.toThrow('Token is required');
    });

    test('使用错误的令牌释放锁应该失败', async () => {
      const token = await lockService.acquire('test-key');
      
      await expect(
        lockService.release('test-key', 'wrong-token')
      ).rejects.toThrow('Invalid token');
    });

    test('续期锁时需要提供令牌', async () => {
      await expect(
        lockService.renew('test-key', null, 1000)
      ).rejects.toThrow('Token is required');
    });

    test('使用错误的令牌续期锁应该失败', async () => {
      const token = await lockService.acquire('test-key');
      
      await expect(
        lockService.renew('test-key', 'wrong-token', 1000)
      ).rejects.toThrow('Invalid token');
    });

    test('释放不存在的锁应该返回false', async () => {
      const released = await lockService.release('non-existent-key', 'any-token');
      expect(released).toBe(false);
    });

    test('续期不存在的锁应该返回false', async () => {
      const renewed = await lockService.renew('non-existent-key', 'any-token', 1000);
      expect(renewed).toBe(false);
    });
  });

  describe('withLock方法', () => {
    test('应该能够使用withLock执行操作', async () => {
      let executed = false;
      
      const result = await lockService.withLock('test-key', async () => {
        executed = true;
        return 'success';
      });
      
      expect(executed).toBe(true);
      expect(result).toBe('success');
      expect(await lockService.isLocked('test-key')).toBe(false);
    });

    test('withLock应该在操作失败时释放锁', async () => {
      let executed = false;
      
      await expect(
        lockService.withLock('test-key', async () => {
          executed = true;
          throw new Error('Operation failed');
        })
      ).rejects.toThrow('Operation failed');
      
      expect(executed).toBe(true);
      expect(await lockService.isLocked('test-key')).toBe(false);
    });
  });

  describe('强制释放锁', () => {
    test('应该能够强制释放锁', async () => {
      const token = await lockService.acquire('test-key');
      
      expect(await lockService.isLocked('test-key')).toBe(true);
      
      const forceReleased = await lockService.forceRelease('test-key');
      expect(forceReleased).toBe(true);
      expect(await lockService.isLocked('test-key')).toBe(false);
    });
  });

  describe('获取所有锁', () => {
    test('应该能够获取所有锁信息', async () => {
      const token1 = await lockService.acquire('key1');
      const token2 = await lockService.acquire('key2');
      
      const allLocks = await lockService.getAllLocks();
      
      expect(allLocks).toHaveLength(2);
      expect(allLocks.map(lock => lock.key)).toContain('key1');
      expect(allLocks.map(lock => lock.key)).toContain('key2');
    });

    test('没有锁时应该返回空数组', async () => {
      const allLocks = await lockService.getAllLocks();
      expect(allLocks).toEqual([]);
    });
  });

  describe('清理过期锁', () => {
    test('应该能够清理过期锁', async () => {
      // 创建一个短TTL的锁
      await lockService.acquire('test-key', { ttl: 50 });
      
      // 等待锁过期
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 清理过期锁
      const cleanedCount = await lockService.cleanupExpiredLocks();
      expect(cleanedCount).toBe(1);
      
      // 验证锁已被清理
      expect(await lockService.isLocked('test-key')).toBe(false);
    });
  });

  describe('服务可用性', () => {
    test('应该能够检查服务是否可用', async () => {
      expect(await lockService.isAvailable()).toBe(true);
      
      mockStorage.setAvailable(false);
      expect(await lockService.isAvailable()).toBe(false);
    });

    test('应该返回正确的服务类型', () => {
      expect(lockService.getType()).toBe('distributed');
    });
  });
});
