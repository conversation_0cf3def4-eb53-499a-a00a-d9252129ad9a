/**
 * 锁服务接口
 * 定义了统一的分布式锁操作接口
 */
export class ILockService {
  /**
   * 获取锁
   * @param {string} key - 锁的键
   * @param {Object} options - 锁选项
   * @param {number} options.timeout - 获取锁的超时时间（毫秒），默认5000
   * @param {number} options.ttl - 锁的生存时间（毫秒），默认30000
   * @param {number} options.retryInterval - 重试间隔（毫秒），默认100
   * @returns {Promise<string>} 锁的令牌，获取失败时抛出异常
   */
  async acquire(key, options = {}) {
    throw new Error('Method acquire() must be implemented by subclass');
  }

  /**
   * 释放锁
   * @param {string} key - 锁的键
   * @param {string} token - 锁的令牌
   * @returns {Promise<boolean>} 是否释放成功
   */
  async release(key, token) {
    throw new Error('Method release() must be implemented by subclass');
  }

  /**
   * 检查锁是否存在
   * @param {string} key - 锁的键
   * @returns {Promise<boolean>} 是否存在
   */
  async isLocked(key) {
    throw new Error('Method isLocked() must be implemented by subclass');
  }

  /**
   * 续期锁
   * @param {string} key - 锁的键
   * @param {string} token - 锁的令牌
   * @param {number} ttl - 新的生存时间（毫秒）
   * @returns {Promise<boolean>} 是否续期成功
   */
  async renew(key, token, ttl) {
    throw new Error('Method renew() must be implemented by subclass');
  }

  /**
   * 强制释放锁
   * @param {string} key - 锁的键
   * @returns {Promise<boolean>} 是否释放成功
   */
  async forceRelease(key) {
    throw new Error('Method forceRelease() must be implemented by subclass');
  }

  /**
   * 获取锁信息
   * @param {string} key - 锁的键
   * @returns {Promise<Object|null>} 锁信息，不存在时返回null
   */
  async getLockInfo(key) {
    throw new Error('Method getLockInfo() must be implemented by subclass');
  }

  /**
   * 获取所有锁
   * @returns {Promise<Object[]>} 锁信息数组
   */
  async getAllLocks() {
    throw new Error('Method getAllLocks() must be implemented by subclass');
  }

  /**
   * 清理过期锁
   * @returns {Promise<number>} 清理的锁数量
   */
  async cleanupExpiredLocks() {
    throw new Error('Method cleanupExpiredLocks() must be implemented by subclass');
  }

  /**
   * 使用锁执行操作
   * @param {string} key - 锁的键
   * @param {Function} operation - 要执行的操作
   * @param {Object} options - 锁选项
   * @returns {Promise<any>} 操作结果
   */
  async withLock(key, operation, options = {}) {
    const token = await this.acquire(key, options);
    try {
      return await operation();
    } finally {
      await this.release(key, token);
    }
  }

  /**
   * 检查锁服务是否可用
   * @returns {Promise<boolean>} 是否可用
   */
  async isAvailable() {
    throw new Error('Method isAvailable() must be implemented by subclass');
  }

  /**
   * 获取锁服务类型
   * @returns {string} 锁服务类型
   */
  getType() {
    throw new Error('Method getType() must be implemented by subclass');
  }
}
