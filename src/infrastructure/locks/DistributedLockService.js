import { ILockService } from './ILockService.js';

/**
 * 分布式锁服务实现
 * 基于存储仓储的分布式锁机制
 */
export class DistributedLockService extends ILockService {
  /**
   * 构造函数
   * @param {IStorageRepository} storageRepository - 存储仓储
   */
  constructor(storageRepository) {
    super();
    this.storage = storageRepository;
    this.lockPrefix = 'lock:';
    this.cleanupInterval = null;
    this.startCleanupTimer();
  }

  /**
   * 生成锁令牌
   * @returns {string} 唯一令牌
   */
  _generateToken() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 创建锁键
   * @param {string} key - 原始键
   * @returns {string} 锁键
   */
  _createLockKey(key) {
    return `${this.lockPrefix}${key}`;
  }

  /**
   * 创建锁信息
   * @param {string} token - 锁令牌
   * @param {number} ttl - 生存时间
   * @returns {Object} 锁信息
   */
  _createLockInfo(token, ttl) {
    return {
      token,
      createdAt: Date.now(),
      expiresAt: Date.now() + ttl,
      ttl
    };
  }

  /**
   * 检查锁是否过期
   * @param {Object} lockInfo - 锁信息
   * @returns {boolean} 是否过期
   */
  _isExpired(lockInfo) {
    return Date.now() > lockInfo.expiresAt;
  }

  /**
   * 等待指定时间
   * @param {number} ms - 等待时间（毫秒）
   * @returns {Promise<void>}
   */
  _sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取锁
   * @param {string} key - 锁的键
   * @param {Object} options - 锁选项
   * @param {number} options.timeout - 获取锁的超时时间（毫秒），默认5000
   * @param {number} options.ttl - 锁的生存时间（毫秒），默认30000
   * @param {number} options.retryInterval - 重试间隔（毫秒），默认100
   * @returns {Promise<string>} 锁的令牌，获取失败时抛出异常
   */
  async acquire(key, options = {}) {
    const {
      timeout = 5000,
      ttl = 30000,
      retryInterval = 100
    } = options;

    const lockKey = this._createLockKey(key);
    const token = this._generateToken();
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      try {
        // 尝试获取现有锁
        const existingLock = await this.storage.get(lockKey);
        
        if (existingLock[lockKey]) {
          const lockInfo = existingLock[lockKey];
          
          // 检查锁是否过期
          if (this._isExpired(lockInfo)) {
            // 锁已过期，尝试删除并重新获取
            await this.storage.remove(lockKey);
          } else {
            // 锁仍然有效，等待重试
            await this._sleep(retryInterval);
            continue;
          }
        }

        // 尝试设置新锁
        const lockInfo = this._createLockInfo(token, ttl);
        await this.storage.set({ [lockKey]: lockInfo });

        // 验证锁是否成功获取（防止竞态条件）
        const verifyLock = await this.storage.get(lockKey);
        if (verifyLock[lockKey] && verifyLock[lockKey].token === token) {
          return token;
        }

        // 锁获取失败，继续重试
        await this._sleep(retryInterval);
      } catch (error) {
        console.warn(`Failed to acquire lock for key '${key}':`, error);
        await this._sleep(retryInterval);
      }
    }

    throw new Error(`Failed to acquire lock for key '${key}' within ${timeout}ms`);
  }

  /**
   * 释放锁
   * @param {string} key - 锁的键
   * @param {string} token - 锁的令牌
   * @returns {Promise<boolean>} 是否释放成功
   */
  async release(key, token) {
    if (!token) {
      throw new Error('Token is required to release lock');
    }

    const lockKey = this._createLockKey(key);

    try {
      const existingLock = await this.storage.get(lockKey);

      if (!existingLock[lockKey]) {
        return false; // 锁不存在
      }

      const lockInfo = existingLock[lockKey];

      // 验证令牌
      if (lockInfo.token !== token) {
        throw new Error('Invalid token');
      }

      // 删除锁
      await this.storage.remove(lockKey);
      return true;
    } catch (error) {
      // 如果是令牌验证错误，重新抛出
      if (error.message === 'Invalid token') {
        throw error;
      }
      console.error(`Failed to release lock for key '${key}':`, error);
      return false;
    }
  }

  /**
   * 检查锁是否存在
   * @param {string} key - 锁的键
   * @returns {Promise<boolean>} 是否存在
   */
  async isLocked(key) {
    const lockKey = this._createLockKey(key);

    try {
      const existingLock = await this.storage.get(lockKey);
      
      if (!existingLock[lockKey]) {
        return false;
      }

      const lockInfo = existingLock[lockKey];
      
      // 检查是否过期
      if (this._isExpired(lockInfo)) {
        // 清理过期锁
        await this.storage.remove(lockKey);
        return false;
      }

      return true;
    } catch (error) {
      console.error(`Failed to check lock status for key '${key}':`, error);
      return false;
    }
  }

  /**
   * 续期锁
   * @param {string} key - 锁的键
   * @param {string} token - 锁的令牌
   * @param {number} ttl - 新的生存时间（毫秒）
   * @returns {Promise<boolean>} 是否续期成功
   */
  async renew(key, token, ttl) {
    if (!token) {
      throw new Error('Token is required to renew lock');
    }

    const lockKey = this._createLockKey(key);

    try {
      const existingLock = await this.storage.get(lockKey);

      if (!existingLock[lockKey]) {
        return false; // 锁不存在
      }

      const lockInfo = existingLock[lockKey];

      // 验证令牌
      if (lockInfo.token !== token) {
        throw new Error('Invalid token');
      }

      // 更新锁信息
      const updatedLockInfo = {
        ...lockInfo,
        expiresAt: Date.now() + ttl,
        ttl
      };

      await this.storage.set({ [lockKey]: updatedLockInfo });
      return true;
    } catch (error) {
      // 如果是令牌验证错误，重新抛出
      if (error.message === 'Invalid token') {
        throw error;
      }
      console.error(`Failed to renew lock for key '${key}':`, error);
      return false;
    }
  }

  /**
   * 强制释放锁
   * @param {string} key - 锁的键
   * @returns {Promise<boolean>} 是否释放成功
   */
  async forceRelease(key) {
    const lockKey = this._createLockKey(key);

    try {
      await this.storage.remove(lockKey);
      return true;
    } catch (error) {
      console.error(`Failed to force release lock for key '${key}':`, error);
      return false;
    }
  }

  /**
   * 获取锁信息
   * @param {string} key - 锁的键
   * @returns {Promise<Object|null>} 锁信息，不存在时返回null
   */
  async getLockInfo(key) {
    const lockKey = this._createLockKey(key);

    try {
      const existingLock = await this.storage.get(lockKey);
      
      if (!existingLock[lockKey]) {
        return null;
      }

      const lockInfo = existingLock[lockKey];
      
      // 检查是否过期
      if (this._isExpired(lockInfo)) {
        await this.storage.remove(lockKey);
        return null;
      }

      return {
        ...lockInfo,
        key,
        remainingTtl: lockInfo.expiresAt - Date.now()
      };
    } catch (error) {
      console.error(`Failed to get lock info for key '${key}':`, error);
      return null;
    }
  }

  /**
   * 获取所有锁
   * @returns {Promise<Object[]>} 锁信息数组
   */
  async getAllLocks() {
    try {
      const allKeys = await this.storage.keys();
      const lockKeys = allKeys.filter(key => key.startsWith(this.lockPrefix));
      
      if (lockKeys.length === 0) {
        return [];
      }

      const locks = await this.storage.get(lockKeys);
      const result = [];

      for (const [lockKey, lockInfo] of Object.entries(locks)) {
        if (lockInfo && !this._isExpired(lockInfo)) {
          const originalKey = lockKey.replace(this.lockPrefix, '');
          result.push({
            ...lockInfo,
            key: originalKey,
            remainingTtl: lockInfo.expiresAt - Date.now()
          });
        }
      }

      return result;
    } catch (error) {
      console.error('Failed to get all locks:', error);
      return [];
    }
  }

  /**
   * 清理过期锁
   * @returns {Promise<number>} 清理的锁数量
   */
  async cleanupExpiredLocks() {
    try {
      const allKeys = await this.storage.keys();
      const lockKeys = allKeys.filter(key => key.startsWith(this.lockPrefix));
      
      if (lockKeys.length === 0) {
        return 0;
      }

      const locks = await this.storage.get(lockKeys);
      const expiredKeys = [];

      for (const [lockKey, lockInfo] of Object.entries(locks)) {
        if (lockInfo && this._isExpired(lockInfo)) {
          expiredKeys.push(lockKey);
        }
      }

      if (expiredKeys.length > 0) {
        await this.storage.remove(expiredKeys);
      }

      return expiredKeys.length;
    } catch (error) {
      console.error('Failed to cleanup expired locks:', error);
      return 0;
    }
  }

  /**
   * 启动清理定时器
   */
  startCleanupTimer() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // 每分钟清理一次过期锁
    this.cleanupInterval = setInterval(async () => {
      try {
        await this.cleanupExpiredLocks();
      } catch (error) {
        console.error('Cleanup timer error:', error);
      }
    }, 60000);
  }

  /**
   * 停止清理定时器
   */
  stopCleanupTimer() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * 检查锁服务是否可用
   * @returns {Promise<boolean>} 是否可用
   */
  async isAvailable() {
    try {
      return await this.storage.isAvailable();
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取锁服务类型
   * @returns {string} 锁服务类型
   */
  getType() {
    return 'DistributedLockService';
  }

  /**
   * 销毁锁服务
   */
  destroy() {
    this.stopCleanupTimer();
  }
}
