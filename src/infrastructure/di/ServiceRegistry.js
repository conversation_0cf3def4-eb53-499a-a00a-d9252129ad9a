/**
 * 服务注册配置
 * 提供统一的服务注册和配置管理
 */

import { TYPES, LIFECYCLE } from './ServiceTypes.js';
import { ServiceFactory } from './ServiceFactory.js';

/**
 * 服务注册器
 * 负责配置和注册所有应用服务
 */
export class ServiceRegistry {
  /**
   * 构造函数
   * @param {Object} config - 配置对象
   */
  constructor(config = {}) {
    this.config = config;
    this.factory = null;
  }

  /**
   * 配置服务
   * @param {Object} customConfig - 自定义配置
   * @returns {Promise<ServiceFactory>} 配置好的服务工厂
   */
  async configureServices(customConfig = {}) {
    const mergedConfig = { ...this.config, ...customConfig };
    
    this.factory = new ServiceFactory(mergedConfig);
    await this.factory.initialize();

    // 注册额外的服务
    this._registerAdditionalServices();

    return this.factory;
  }

  /**
   * 注册额外的服务
   * @private
   */
  _registerAdditionalServices() {
    const container = this.factory.getContainer();

    // 注册工厂服务
    this._registerFactoryServices(container);

    // 注册外部服务
    this._registerExternalServices(container);

    // 注册工具服务
    this._registerToolServices(container);

    // 注册监控服务
    this._registerMonitoringServices(container);

    // 根据环境注册测试服务
    if (this.config.environment === 'test') {
      this._registerTestServices(container);
    }
  }

  /**
   * 注册工厂服务
   * @param {DIContainer} container - 依赖注入容器
   * @private
   */
  _registerFactoryServices(container) {
    // 服务工厂
    container.register(
      TYPES.ServiceFactory,
      () => this.factory,
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 仓储工厂
    container.register(
      TYPES.RepositoryFactory,
      (c) => ({
        createStorageRepository: () => c.resolve(TYPES.IStorageRepository),
        createMemoryRepository: () => {
          // 这里可以根据配置创建不同类型的仓储
          return c.resolve(TYPES.IStorageRepository);
        }
      }),
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 适配器工厂
    container.register(
      TYPES.AdapterFactory,
      (c) => ({
        createStorageAdapter: (type = 'chrome') => {
          switch (type) {
            case 'chrome':
              return c.resolve(TYPES.ChromeStorageAdapter);
            default:
              throw new Error(`Unsupported storage adapter type: ${type}`);
          }
        },
        createRuntimeAdapter: () => c.resolve(TYPES.ChromeRuntimeAdapter),
        createTabsAdapter: () => c.resolve(TYPES.ChromeTabsAdapter)
      }),
      { lifecycle: LIFECYCLE.SINGLETON }
    );
  }

  /**
   * 注册外部服务
   * @param {DIContainer} container - 依赖注入容器
   * @private
   */
  _registerExternalServices(container) {
    // 华为云OBS客户端
    container.register(
      TYPES.HuaweiObsClient,
      (c) => {
        const config = c.resolve(TYPES.AppConfig);
        return this._createHuaweiObsClient(config.huaweiObs || {});
      },
      { lifecycle: LIFECYCLE.SINGLETON, lazy: true }
    );

    // MinIO客户端
    container.register(
      TYPES.MinioClient,
      (c) => {
        const config = c.resolve(TYPES.AppConfig);
        return this._createMinioClient(config.minio || {});
      },
      { lifecycle: LIFECYCLE.SINGLETON, lazy: true }
    );
  }

  /**
   * 注册工具服务
   * @param {DIContainer} container - 依赖注入容器
   * @private
   */
  _registerToolServices(container) {
    // 迁移服务
    container.register(
      TYPES.IMigrationService,
      (c) => ({
        migrate: async (fromVersion, toVersion) => {
          console.log(`Migrating from ${fromVersion} to ${toVersion}`);
          // 实际的迁移逻辑
        },
        getCurrentVersion: () => '1.0.0',
        getTargetVersion: () => '1.0.0'
      }),
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 备份服务
    container.register(
      TYPES.IBackupService,
      (c) => {
        const memoryService = c.resolve(TYPES.IMemoryStorageService);
        const settingsService = c.resolve(TYPES.ISettingsService);
        
        return {
          createBackup: async () => {
            const memories = await memoryService.exportMemories();
            const settings = await settingsService.exportSettings();
            return {
              memories,
              settings,
              timestamp: new Date().toISOString(),
              version: '1.0.0'
            };
          },
          restoreBackup: async (backup) => {
            await memoryService.importMemories(backup.memories);
            await settingsService.importSettings(backup.settings);
          }
        };
      },
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 导入导出服务
    container.register(
      TYPES.IExportImportService,
      (c) => {
        const memoryService = c.resolve(TYPES.IMemoryStorageService);
        
        return {
          exportToJson: async (options) => {
            const data = await memoryService.exportMemories(options);
            return JSON.stringify(data, null, 2);
          },
          importFromJson: async (jsonData, options) => {
            const data = JSON.parse(jsonData);
            return await memoryService.importMemories(data, options);
          },
          exportToCsv: async (options) => {
            // CSV导出逻辑
            throw new Error('CSV export not implemented yet');
          },
          importFromCsv: async (csvData, options) => {
            // CSV导入逻辑
            throw new Error('CSV import not implemented yet');
          }
        };
      },
      { lifecycle: LIFECYCLE.SINGLETON }
    );
  }

  /**
   * 注册监控服务
   * @param {DIContainer} container - 依赖注入容器
   * @private
   */
  _registerMonitoringServices(container) {
    // 性能监控
    container.register(
      TYPES.IPerformanceMonitor,
      () => ({
        startTimer: (name) => {
          const start = performance.now();
          return {
            end: () => {
              const duration = performance.now() - start;
              console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`);
              return duration;
            }
          };
        },
        measure: async (name, fn) => {
          const timer = this.startTimer(name);
          try {
            return await fn();
          } finally {
            timer.end();
          }
        }
      }),
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 健康检查
    container.register(
      TYPES.IHealthChecker,
      (c) => ({
        checkHealth: async () => {
          const results = {};
          
          try {
            const storageService = c.resolve(TYPES.IStorageRepository);
            results.storage = await storageService.isAvailable();
          } catch (error) {
            results.storage = false;
          }

          try {
            const cacheService = c.resolve(TYPES.ICacheService);
            results.cache = await cacheService.isAvailable();
          } catch (error) {
            results.cache = false;
          }

          try {
            const lockService = c.resolve(TYPES.ILockService);
            results.lock = await lockService.isAvailable();
          } catch (error) {
            results.lock = false;
          }

          const overall = Object.values(results).every(status => status);
          
          return {
            overall,
            services: results,
            timestamp: new Date().toISOString()
          };
        }
      }),
      { lifecycle: LIFECYCLE.SINGLETON }
    );
  }

  /**
   * 注册测试服务
   * @param {DIContainer} container - 依赖注入容器
   * @private
   */
  _registerTestServices(container) {
    // Mock存储仓储
    container.register(
      TYPES.MockStorageRepository,
      () => {
        const data = {};
        return {
          async get(keys) {
            if (typeof keys === 'string') {
              return { [keys]: data[keys] };
            }
            const result = {};
            keys.forEach(key => {
              if (data[key]) result[key] = data[key];
            });
            return result;
          },
          async set(items) {
            Object.assign(data, items);
          },
          async remove(keys) {
            if (typeof keys === 'string') {
              delete data[keys];
            } else {
              keys.forEach(key => delete data[key]);
            }
          },
          async clear() {
            Object.keys(data).forEach(key => delete data[key]);
          },
          async has(key) {
            return key in data;
          },
          async keys() {
            return Object.keys(data);
          },
          async isAvailable() {
            return true;
          },
          getType() {
            return 'mock';
          }
        };
      },
      { lifecycle: LIFECYCLE.TRANSIENT }
    );
  }

  /**
   * 创建华为云OBS客户端
   * @param {Object} config - 配置
   * @returns {Object} OBS客户端
   * @private
   */
  _createHuaweiObsClient(config) {
    return {
      config,
      async upload(key, data) {
        console.log(`Mock OBS upload: ${key}`);
        return { success: true, key };
      },
      async download(key) {
        console.log(`Mock OBS download: ${key}`);
        return { success: true, data: null };
      },
      async delete(key) {
        console.log(`Mock OBS delete: ${key}`);
        return { success: true };
      }
    };
  }

  /**
   * 创建MinIO客户端
   * @param {Object} config - 配置
   * @returns {Object} MinIO客户端
   * @private
   */
  _createMinioClient(config) {
    return {
      config,
      async putObject(bucket, key, data) {
        console.log(`Mock MinIO putObject: ${bucket}/${key}`);
        return { success: true };
      },
      async getObject(bucket, key) {
        console.log(`Mock MinIO getObject: ${bucket}/${key}`);
        return { success: true, data: null };
      },
      async removeObject(bucket, key) {
        console.log(`Mock MinIO removeObject: ${bucket}/${key}`);
        return { success: true };
      }
    };
  }

  /**
   * 获取服务工厂
   * @returns {ServiceFactory} 服务工厂
   */
  getServiceFactory() {
    return this.factory;
  }

  /**
   * 销毁注册器
   */
  dispose() {
    if (this.factory) {
      this.factory.dispose();
      this.factory = null;
    }
  }
}

/**
 * 创建默认的服务注册器
 * @param {Object} config - 配置对象
 * @returns {Promise<ServiceRegistry>} 服务注册器
 */
export async function createServiceRegistry(config = {}) {
  const registry = new ServiceRegistry(config);
  await registry.configureServices();
  return registry;
}

export default ServiceRegistry;
