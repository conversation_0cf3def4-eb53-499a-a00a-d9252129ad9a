/**
 * 依赖注入容器实现
 * 提供服务注册、解析和生命周期管理
 */

import { LIFECYCLE, isValidServiceType, getServiceTypeName } from './ServiceTypes.js';

/**
 * 依赖注入容器接口
 */
export class IDIContainer {
  /**
   * 注册服务
   * @param {Symbol} type - 服务类型标识符
   * @param {Function} factory - 服务工厂函数
   * @param {Object} options - 注册选项
   */
  register(type, factory, options = {}) {
    throw new Error('Method register() must be implemented by subclass');
  }

  /**
   * 解析服务
   * @param {Symbol} type - 服务类型标识符
   * @returns {any} 服务实例
   */
  resolve(type) {
    throw new Error('Method resolve() must be implemented by subclass');
  }

  /**
   * 检查服务是否已注册
   * @param {Symbol} type - 服务类型标识符
   * @returns {boolean} 是否已注册
   */
  isRegistered(type) {
    throw new Error('Method isRegistered() must be implemented by subclass');
  }

  /**
   * 创建子容器
   * @returns {IDIContainer} 子容器实例
   */
  createChild() {
    throw new Error('Method createChild() must be implemented by subclass');
  }
}

/**
 * 依赖注入容器实现
 */
export class DIContainer extends IDIContainer {
  /**
   * 构造函数
   * @param {DIContainer} [parent] - 父容器
   */
  constructor(parent = null) {
    super();
    this.parent = parent;
    this.services = new Map();
    this.instances = new Map();
    this.resolving = new Set(); // 用于检测循环依赖
    this.disposed = false;
  }

  /**
   * 注册服务
   * @param {Symbol} type - 服务类型标识符
   * @param {Function} factory - 服务工厂函数
   * @param {Object} options - 注册选项
   */
  register(type, factory, options = {}) {
    if (this.disposed) {
      throw new Error('Container has been disposed');
    }

    if (!isValidServiceType(type)) {
      throw new Error(`Invalid service type: ${getServiceTypeName(type)}`);
    }

    if (typeof factory !== 'function') {
      throw new Error('Factory must be a function');
    }

    const serviceOptions = {
      lifecycle: options.lifecycle || LIFECYCLE.SINGLETON,
      tags: options.tags || [],
      lazy: options.lazy !== false, // 默认延迟初始化
      validator: options.validator || null,
      ...options
    };

    // 验证生命周期类型
    if (!Object.values(LIFECYCLE).includes(serviceOptions.lifecycle)) {
      throw new Error(`Invalid lifecycle: ${serviceOptions.lifecycle}`);
    }

    const metadata = {
      type,
      factory,
      options: serviceOptions,
      instance: null,
      initialized: false
    };

    this.services.set(type, metadata);

    // 如果不是延迟初始化且是单例，立即创建实例
    if (!serviceOptions.lazy && serviceOptions.lifecycle === LIFECYCLE.SINGLETON) {
      this.resolve(type);
    }
  }

  /**
   * 解析服务
   * @param {Symbol} type - 服务类型标识符
   * @returns {any} 服务实例
   */
  resolve(type) {
    if (this.disposed) {
      throw new Error('Container has been disposed');
    }

    if (!isValidServiceType(type)) {
      throw new Error(`Invalid service type: ${getServiceTypeName(type)}`);
    }

    // 检测循环依赖
    if (this.resolving.has(type)) {
      const typeName = getServiceTypeName(type);
      throw new Error(`Circular dependency detected for service: ${typeName}`);
    }

    // 首先在当前容器中查找
    let metadata = this.services.get(type);
    let container = this;

    // 如果当前容器中没有，查找父容器
    if (!metadata && this.parent) {
      metadata = this._findServiceMetadata(type);
      if (metadata) {
        // 对于作用域服务，即使在父容器中找到，也要在当前容器中创建实例
        if (metadata.options.lifecycle === LIFECYCLE.SCOPED) {
          return this._resolveScoped(metadata);
        }
        // 对于单例和瞬态服务，在注册的容器中解析
        return this.parent.resolve(type);
      }
    }

    if (!metadata) {
      const typeName = getServiceTypeName(type);
      throw new Error(`Service not registered: ${typeName}`);
    }

    // 根据生命周期返回实例
    switch (metadata.options.lifecycle) {
      case LIFECYCLE.SINGLETON:
        return this._resolveSingleton(metadata);
      case LIFECYCLE.TRANSIENT:
        return this._resolveTransient(metadata);
      case LIFECYCLE.SCOPED:
        return this._resolveScoped(metadata);
      default:
        throw new Error(`Unsupported lifecycle: ${metadata.options.lifecycle}`);
    }
  }

  /**
   * 在容器层次结构中查找服务元数据
   * @param {Symbol} type - 服务类型标识符
   * @returns {Object|null} 服务元数据
   * @private
   */
  _findServiceMetadata(type) {
    let current = this;
    while (current) {
      const metadata = current.services.get(type);
      if (metadata) {
        return metadata;
      }
      current = current.parent;
    }
    return null;
  }

  /**
   * 解析单例服务
   * @param {Object} metadata - 服务元数据
   * @returns {any} 服务实例
   * @private
   */
  _resolveSingleton(metadata) {
    if (metadata.instance) {
      return metadata.instance;
    }

    this.resolving.add(metadata.type);
    try {
      const instance = metadata.factory(this);
      
      // 验证实例
      if (metadata.options.validator) {
        metadata.options.validator(instance);
      }

      metadata.instance = instance;
      metadata.initialized = true;
      
      return instance;
    } finally {
      this.resolving.delete(metadata.type);
    }
  }

  /**
   * 解析瞬态服务
   * @param {Object} metadata - 服务元数据
   * @returns {any} 服务实例
   * @private
   */
  _resolveTransient(metadata) {
    this.resolving.add(metadata.type);
    try {
      const instance = metadata.factory(this);
      
      // 验证实例
      if (metadata.options.validator) {
        metadata.options.validator(instance);
      }

      return instance;
    } finally {
      this.resolving.delete(metadata.type);
    }
  }

  /**
   * 解析作用域服务
   * @param {Object} metadata - 服务元数据
   * @returns {any} 服务实例
   * @private
   */
  _resolveScoped(metadata) {
    // 作用域服务在当前容器中是单例，但在不同容器中是不同实例
    const instanceKey = `scoped_${metadata.type.toString()}`;
    
    if (this.instances.has(instanceKey)) {
      return this.instances.get(instanceKey);
    }

    this.resolving.add(metadata.type);
    try {
      const instance = metadata.factory(this);
      
      // 验证实例
      if (metadata.options.validator) {
        metadata.options.validator(instance);
      }

      this.instances.set(instanceKey, instance);
      return instance;
    } finally {
      this.resolving.delete(metadata.type);
    }
  }

  /**
   * 检查服务是否已注册
   * @param {Symbol} type - 服务类型标识符
   * @returns {boolean} 是否已注册
   */
  isRegistered(type) {
    if (this.services.has(type)) {
      return true;
    }
    
    return this.parent ? this.parent.isRegistered(type) : false;
  }

  /**
   * 创建子容器
   * @returns {DIContainer} 子容器实例
   */
  createChild() {
    return new DIContainer(this);
  }

  /**
   * 获取所有已注册的服务类型
   * @returns {Symbol[]} 服务类型数组
   */
  getRegisteredTypes() {
    const types = Array.from(this.services.keys());
    
    if (this.parent) {
      const parentTypes = this.parent.getRegisteredTypes();
      // 合并并去重
      return [...new Set([...types, ...parentTypes])];
    }
    
    return types;
  }

  /**
   * 获取服务元数据
   * @param {Symbol} type - 服务类型标识符
   * @returns {Object|null} 服务元数据
   */
  getServiceMetadata(type) {
    const metadata = this.services.get(type);
    if (metadata) {
      return { ...metadata };
    }
    
    return this.parent ? this.parent.getServiceMetadata(type) : null;
  }

  /**
   * 根据标签获取服务
   * @param {string} tag - 标签名称
   * @returns {any[]} 服务实例数组
   */
  getServicesByTag(tag) {
    const services = [];
    
    for (const [type, metadata] of this.services) {
      if (metadata.options.tags.includes(tag)) {
        services.push(this.resolve(type));
      }
    }
    
    if (this.parent) {
      services.push(...this.parent.getServicesByTag(tag));
    }
    
    return services;
  }

  /**
   * 重置容器（清除所有实例，保留注册信息）
   */
  reset() {
    this.instances.clear();
    
    // 重置单例实例
    for (const metadata of this.services.values()) {
      if (metadata.options.lifecycle === LIFECYCLE.SINGLETON) {
        metadata.instance = null;
        metadata.initialized = false;
      }
    }
  }

  /**
   * 销毁容器
   */
  dispose() {
    if (this.disposed) {
      return;
    }

    // 销毁所有实例
    for (const instance of this.instances.values()) {
      if (instance && typeof instance.dispose === 'function') {
        try {
          instance.dispose();
        } catch (error) {
          console.error('Error disposing service instance:', error);
        }
      }
    }

    // 销毁单例实例
    for (const metadata of this.services.values()) {
      if (metadata.instance && typeof metadata.instance.dispose === 'function') {
        try {
          metadata.instance.dispose();
        } catch (error) {
          console.error('Error disposing singleton instance:', error);
        }
      }
    }

    this.services.clear();
    this.instances.clear();
    this.resolving.clear();
    this.disposed = true;
  }

  /**
   * 检查容器是否已销毁
   * @returns {boolean} 是否已销毁
   */
  isDisposed() {
    return this.disposed;
  }
}

export default DIContainer;
