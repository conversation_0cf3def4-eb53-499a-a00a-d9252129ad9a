/**
 * DIContainer 单元测试
 */

import { DIContainer } from '../DIContainer.js';
import { TYPES, LIFECYCLE } from '../ServiceTypes.js';

// 测试服务类
class TestService {
  constructor(name = 'TestService') {
    this.name = name;
    this.initialized = true;
  }

  getName() {
    return this.name;
  }

  dispose() {
    this.disposed = true;
  }
}

class DependentService {
  constructor(testService) {
    this.testService = testService;
    this.name = 'DependentService';
  }

  getTestServiceName() {
    return this.testService.getName();
  }
}

describe('DIContainer', () => {
  let container;

  beforeEach(() => {
    container = new DIContainer();
  });

  afterEach(() => {
    if (container && !container.isDisposed()) {
      container.dispose();
    }
  });

  describe('基本注册和解析', () => {
    test('应该能够注册和解析单例服务', () => {
      const testType = Symbol.for('TestService');
      
      container.register(
        testType,
        () => new TestService(),
        { lifecycle: LIFECYCLE.SINGLETON }
      );

      const service1 = container.resolve(testType);
      const service2 = container.resolve(testType);

      expect(service1).toBeInstanceOf(TestService);
      expect(service2).toBeInstanceOf(TestService);
      expect(service1).toBe(service2); // 应该是同一个实例
    });

    test('应该能够注册和解析瞬态服务', () => {
      const testType = Symbol.for('TestService');
      
      container.register(
        testType,
        () => new TestService(),
        { lifecycle: LIFECYCLE.TRANSIENT }
      );

      const service1 = container.resolve(testType);
      const service2 = container.resolve(testType);

      expect(service1).toBeInstanceOf(TestService);
      expect(service2).toBeInstanceOf(TestService);
      expect(service1).not.toBe(service2); // 应该是不同的实例
    });

    test('应该能够注册和解析作用域服务', () => {
      const testType = Symbol.for('TestService');
      
      container.register(
        testType,
        () => new TestService(),
        { lifecycle: LIFECYCLE.SCOPED }
      );

      const service1 = container.resolve(testType);
      const service2 = container.resolve(testType);

      expect(service1).toBeInstanceOf(TestService);
      expect(service2).toBeInstanceOf(TestService);
      expect(service1).toBe(service2); // 在同一容器中应该是同一个实例

      // 在子容器中应该是不同的实例
      const childContainer = container.createChild();
      const service3 = childContainer.resolve(testType);
      expect(service3).not.toBe(service1);
    });

    test('应该使用默认的单例生命周期', () => {
      const testType = Symbol.for('TestService');
      
      container.register(testType, () => new TestService());

      const service1 = container.resolve(testType);
      const service2 = container.resolve(testType);

      expect(service1).toBe(service2);
    });
  });

  describe('依赖注入', () => {
    test('应该能够注入依赖服务', () => {
      const testType = Symbol.for('TestService');
      const dependentType = Symbol.for('DependentService');
      
      container.register(testType, () => new TestService());
      container.register(
        dependentType,
        (c) => new DependentService(c.resolve(testType))
      );

      const dependentService = container.resolve(dependentType);
      
      expect(dependentService).toBeInstanceOf(DependentService);
      expect(dependentService.getTestServiceName()).toBe('TestService');
    });

    test('应该检测循环依赖', () => {
      const serviceA = Symbol.for('ServiceA');
      const serviceB = Symbol.for('ServiceB');
      
      container.register(serviceA, (c) => ({ serviceB: c.resolve(serviceB) }));
      container.register(serviceB, (c) => ({ serviceA: c.resolve(serviceA) }));

      expect(() => container.resolve(serviceA)).toThrow('Circular dependency detected');
    });
  });

  describe('父子容器', () => {
    test('子容器应该能够解析父容器中的服务', () => {
      const testType = Symbol.for('TestService');
      
      container.register(testType, () => new TestService());
      
      const childContainer = container.createChild();
      const service = childContainer.resolve(testType);
      
      expect(service).toBeInstanceOf(TestService);
    });

    test('子容器中的服务应该优先于父容器', () => {
      const testType = Symbol.for('TestService');
      
      container.register(testType, () => new TestService('Parent'));
      
      const childContainer = container.createChild();
      childContainer.register(testType, () => new TestService('Child'));
      
      const parentService = container.resolve(testType);
      const childService = childContainer.resolve(testType);
      
      expect(parentService.getName()).toBe('Parent');
      expect(childService.getName()).toBe('Child');
    });

    test('应该能够检查服务是否在父容器中注册', () => {
      const testType = Symbol.for('TestService');
      
      container.register(testType, () => new TestService());
      
      const childContainer = container.createChild();
      
      expect(childContainer.isRegistered(testType)).toBe(true);
    });
  });

  describe('服务验证', () => {
    test('应该能够验证服务实例', () => {
      const testType = Symbol.for('TestService');
      const validator = jest.fn();
      
      container.register(
        testType,
        () => new TestService(),
        { validator }
      );

      const service = container.resolve(testType);
      
      expect(validator).toHaveBeenCalledWith(service);
    });

    test('验证失败时应该抛出错误', () => {
      const testType = Symbol.for('TestService');
      const validator = () => {
        throw new Error('Validation failed');
      };
      
      container.register(
        testType,
        () => new TestService(),
        { validator }
      );

      expect(() => container.resolve(testType)).toThrow('Validation failed');
    });
  });

  describe('服务标签', () => {
    test('应该能够根据标签获取服务', () => {
      const service1Type = Symbol.for('Service1');
      const service2Type = Symbol.for('Service2');
      const service3Type = Symbol.for('Service3');
      
      container.register(
        service1Type,
        () => new TestService('Service1'),
        { tags: ['group1'] }
      );
      
      container.register(
        service2Type,
        () => new TestService('Service2'),
        { tags: ['group1', 'group2'] }
      );
      
      container.register(
        service3Type,
        () => new TestService('Service3'),
        { tags: ['group2'] }
      );

      const group1Services = container.getServicesByTag('group1');
      const group2Services = container.getServicesByTag('group2');
      
      expect(group1Services.length).toBe(2);
      expect(group2Services.length).toBe(2);
      
      const group1Names = group1Services.map(s => s.getName());
      expect(group1Names).toContain('Service1');
      expect(group1Names).toContain('Service2');
    });
  });

  describe('服务元数据', () => {
    test('应该能够获取服务元数据', () => {
      const testType = Symbol.for('TestService');
      
      container.register(
        testType,
        () => new TestService(),
        { 
          lifecycle: LIFECYCLE.SINGLETON,
          tags: ['test'],
          lazy: false
        }
      );

      const metadata = container.getServiceMetadata(testType);
      
      expect(metadata).toBeDefined();
      expect(metadata.type).toBe(testType);
      expect(metadata.options.lifecycle).toBe(LIFECYCLE.SINGLETON);
      expect(metadata.options.tags).toContain('test');
      expect(metadata.options.lazy).toBe(false);
    });

    test('应该能够获取所有已注册的服务类型', () => {
      const type1 = Symbol.for('Service1');
      const type2 = Symbol.for('Service2');
      
      container.register(type1, () => new TestService());
      container.register(type2, () => new TestService());

      const registeredTypes = container.getRegisteredTypes();
      
      expect(registeredTypes).toContain(type1);
      expect(registeredTypes).toContain(type2);
      expect(registeredTypes.length).toBe(2);
    });
  });

  describe('错误处理', () => {
    test('解析未注册的服务应该抛出错误', () => {
      const testType = Symbol.for('UnregisteredService');
      
      expect(() => container.resolve(testType)).toThrow('Service not registered');
    });

    test('注册无效的服务类型应该抛出错误', () => {
      expect(() => {
        container.register('invalid-type', () => new TestService());
      }).toThrow('Invalid service type');
    });

    test('注册非函数工厂应该抛出错误', () => {
      const testType = Symbol.for('TestService');
      
      expect(() => {
        container.register(testType, 'not-a-function');
      }).toThrow('Factory must be a function');
    });

    test('注册无效的生命周期应该抛出错误', () => {
      const testType = Symbol.for('TestService');
      
      expect(() => {
        container.register(
          testType,
          () => new TestService(),
          { lifecycle: 'invalid-lifecycle' }
        );
      }).toThrow('Invalid lifecycle');
    });
  });

  describe('容器管理', () => {
    test('应该能够重置容器', () => {
      const testType = Symbol.for('TestService');
      
      container.register(testType, () => new TestService());
      const service1 = container.resolve(testType);
      
      container.reset();
      const service2 = container.resolve(testType);
      
      expect(service1).not.toBe(service2);
    });

    test('应该能够销毁容器', () => {
      const testType = Symbol.for('TestService');
      
      container.register(testType, () => new TestService());
      const service = container.resolve(testType);
      
      container.dispose();
      
      expect(container.isDisposed()).toBe(true);
      expect(service.disposed).toBe(true);
      expect(() => container.resolve(testType)).toThrow('Container has been disposed');
    });

    test('销毁后的容器不应该允许注册新服务', () => {
      container.dispose();
      
      const testType = Symbol.for('TestService');
      
      expect(() => {
        container.register(testType, () => new TestService());
      }).toThrow('Container has been disposed');
    });
  });

  describe('延迟初始化', () => {
    test('延迟初始化的服务应该在首次解析时创建', () => {
      const testType = Symbol.for('TestService');
      const factory = jest.fn(() => new TestService());
      
      container.register(
        testType,
        factory,
        { lazy: true }
      );

      expect(factory).not.toHaveBeenCalled();
      
      container.resolve(testType);
      
      expect(factory).toHaveBeenCalledTimes(1);
    });

    test('非延迟初始化的服务应该在注册时创建', () => {
      const testType = Symbol.for('TestService');
      const factory = jest.fn(() => new TestService());
      
      container.register(
        testType,
        factory,
        { 
          lazy: false,
          lifecycle: LIFECYCLE.SINGLETON
        }
      );

      expect(factory).toHaveBeenCalledTimes(1);
    });
  });
});
