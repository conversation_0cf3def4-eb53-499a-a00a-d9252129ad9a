/**
 * 服务工厂
 * 负责创建和配置各种服务实例
 */

import { TYPES, LIFECYCLE } from './ServiceTypes.js';
import { DIContainer } from './DIContainer.js';

// 基础设施层导入
import { ChromeStorageRepository } from '../repositories/ChromeStorageRepository.js';
import { MemoryCacheService } from '../cache/MemoryCacheService.js';
import { DistributedLockService } from '../locks/DistributedLockService.js';
import { ChromeStorageAdapter } from '../adapters/ChromeStorageAdapter.js';
import { ChromeRuntimeAdapter } from '../adapters/ChromeRuntimeAdapter.js';
import { ChromeTabsAdapter } from '../adapters/ChromeTabsAdapter.js';

// 应用层导入
import { SettingsService } from '../../application/services/SettingsService.js';
import { MemoryStorageService } from '../../application/services/MemoryStorageService.js';

/**
 * 服务工厂类
 * 提供统一的服务创建和配置接口
 */
export class ServiceFactory {
  /**
   * 构造函数
   * @param {Object} config - 配置对象
   */
  constructor(config = {}) {
    this.config = config;
    this.container = new DIContainer();
    this.initialized = false;
  }

  /**
   * 初始化服务工厂
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    // 注册配置服务
    this._registerConfigs();

    // 注册基础设施层服务
    this._registerInfrastructureServices();

    // 注册应用层服务
    this._registerApplicationServices();

    // 注册系统服务
    this._registerSystemServices();

    this.initialized = true;
  }

  /**
   * 注册配置服务
   * @private
   */
  _registerConfigs() {
    // 应用配置
    this.container.register(
      TYPES.AppConfig,
      () => ({
        version: '1.0.0',
        environment: this.config.environment || 'production',
        debug: this.config.debug || false,
        ...this.config.app
      }),
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 存储配置
    this.container.register(
      TYPES.StorageConfig,
      () => ({
        provider: 'chrome',
        maxSize: 8 * 1024 * 1024, // 8MB
        compression: true,
        ...this.config.storage
      }),
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 缓存配置
    this.container.register(
      TYPES.CacheConfig,
      () => ({
        maxSize: 500, // MB
        ttl: 30 * 60 * 1000, // 30分钟
        namespace: 'memory_keeper',
        ...this.config.cache
      }),
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 安全配置
    this.container.register(
      TYPES.SecurityConfig,
      () => ({
        encryption: false,
        algorithm: 'AES-256-GCM',
        keyDerivation: 'PBKDF2',
        ...this.config.security
      }),
      { lifecycle: LIFECYCLE.SINGLETON }
    );
  }

  /**
   * 注册基础设施层服务
   * @private
   */
  _registerInfrastructureServices() {
    // 存储适配器
    this.container.register(
      TYPES.ChromeStorageAdapter,
      () => new ChromeStorageAdapter(),
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    this.container.register(
      TYPES.IStorageAdapter,
      (container) => container.resolve(TYPES.ChromeStorageAdapter),
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 运行时适配器
    this.container.register(
      TYPES.ChromeRuntimeAdapter,
      () => new ChromeRuntimeAdapter(),
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 标签页适配器
    this.container.register(
      TYPES.ChromeTabsAdapter,
      () => new ChromeTabsAdapter(),
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 存储仓储
    this.container.register(
      TYPES.ChromeStorageRepository,
      (container) => {
        const adapter = container.resolve(TYPES.ChromeStorageAdapter);
        return new ChromeStorageRepository(adapter);
      },
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    this.container.register(
      TYPES.IStorageRepository,
      (container) => container.resolve(TYPES.ChromeStorageRepository),
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 缓存服务
    this.container.register(
      TYPES.MemoryCacheService,
      (container) => {
        const storageRepository = container.resolve(TYPES.IStorageRepository);
        const cacheConfig = container.resolve(TYPES.CacheConfig);
        return new MemoryCacheService(storageRepository, cacheConfig);
      },
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    this.container.register(
      TYPES.ICacheService,
      (container) => container.resolve(TYPES.MemoryCacheService),
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 锁服务
    this.container.register(
      TYPES.DistributedLockService,
      (container) => {
        const storageRepository = container.resolve(TYPES.IStorageRepository);
        return new DistributedLockService(storageRepository);
      },
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    this.container.register(
      TYPES.ILockService,
      (container) => container.resolve(TYPES.DistributedLockService),
      { lifecycle: LIFECYCLE.SINGLETON }
    );
  }

  /**
   * 注册应用层服务
   * @private
   */
  _registerApplicationServices() {
    // 设置服务
    this.container.register(
      TYPES.SettingsService,
      (container) => {
        const storageRepository = container.resolve(TYPES.IStorageRepository);
        const cacheService = container.resolve(TYPES.ICacheService);
        const lockService = container.resolve(TYPES.ILockService);
        const deviceId = container.resolve(TYPES.DeviceIdProvider);
        return new SettingsService(storageRepository, cacheService, lockService, deviceId);
      },
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    this.container.register(
      TYPES.ISettingsService,
      (container) => container.resolve(TYPES.SettingsService),
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 记忆存储服务
    this.container.register(
      TYPES.MemoryStorageService,
      (container) => {
        const storageRepository = container.resolve(TYPES.IStorageRepository);
        const cacheService = container.resolve(TYPES.ICacheService);
        const lockService = container.resolve(TYPES.ILockService);
        const deviceId = container.resolve(TYPES.DeviceIdProvider);
        return new MemoryStorageService(storageRepository, cacheService, lockService, deviceId);
      },
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    this.container.register(
      TYPES.IMemoryStorageService,
      (container) => container.resolve(TYPES.MemoryStorageService),
      { lifecycle: LIFECYCLE.SINGLETON }
    );
  }

  /**
   * 注册系统服务
   * @private
   */
  _registerSystemServices() {
    // 设备ID提供者
    this.container.register(
      TYPES.DeviceIdProvider,
      () => this._generateDeviceId(),
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 日志服务
    this.container.register(
      TYPES.Logger,
      (container) => {
        const appConfig = container.resolve(TYPES.AppConfig);
        return this._createLogger(appConfig);
      },
      { lifecycle: LIFECYCLE.SINGLETON }
    );

    // 事件总线
    this.container.register(
      TYPES.EventBus,
      () => this._createEventBus(),
      { lifecycle: LIFECYCLE.SINGLETON }
    );
  }

  /**
   * 生成设备ID
   * @returns {string} 设备ID
   * @private
   */
  _generateDeviceId() {
    // 尝试从localStorage获取
    const localStorageId = localStorage.getItem('memory_keeper_device_id');
    if (localStorageId) {
      return localStorageId;
    }

    // 生成新的设备ID
    const platformPrefix = this._getPlatformPrefix();
    const fingerprint = this._generateFingerprint();
    const deviceId = `${platformPrefix}${fingerprint}`;

    // 保存到localStorage
    localStorage.setItem('memory_keeper_device_id', deviceId);

    return deviceId;
  }

  /**
   * 获取平台前缀
   * @returns {string} 平台前缀
   * @private
   */
  _getPlatformPrefix() {
    const userAgent = navigator.userAgent;
    
    if (userAgent.indexOf('Firefox') !== -1) {
      return 'firefox_';
    } else if (userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Chrome') === -1) {
      return 'safari_';
    } else if (userAgent.indexOf('Edg') !== -1) {
      return 'edge_';
    } else if (userAgent.indexOf('OPR') !== -1 || userAgent.indexOf('Opera') !== -1) {
      return 'opera_';
    } else {
      return 'chrome_';
    }
  }

  /**
   * 生成设备指纹
   * @returns {string} 设备指纹
   * @private
   */
  _generateFingerprint() {
    const screenInfo = `${screen.width}x${screen.height}x${screen.colorDepth}`;
    const timeZoneOffset = new Date().getTimezoneOffset();
    const language = navigator.language;
    const platform = navigator.platform;
    
    const baseInfo = `${screenInfo}|${timeZoneOffset}|${language}|${platform}`;
    
    // 简单哈希函数
    let hash = 0;
    for (let i = 0; i < baseInfo.length; i++) {
      const char = baseInfo.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * 创建日志服务
   * @param {Object} appConfig - 应用配置
   * @returns {Object} 日志服务
   * @private
   */
  _createLogger(appConfig) {
    return {
      debug: appConfig.debug ? console.debug.bind(console) : () => {},
      info: console.info.bind(console),
      warn: console.warn.bind(console),
      error: console.error.bind(console)
    };
  }

  /**
   * 创建事件总线
   * @returns {Object} 事件总线
   * @private
   */
  _createEventBus() {
    const listeners = new Map();
    
    return {
      on(event, listener) {
        if (!listeners.has(event)) {
          listeners.set(event, new Set());
        }
        listeners.get(event).add(listener);
      },
      
      off(event, listener) {
        if (listeners.has(event)) {
          listeners.get(event).delete(listener);
        }
      },
      
      emit(event, data) {
        if (listeners.has(event)) {
          listeners.get(event).forEach(listener => {
            try {
              listener(data);
            } catch (error) {
              console.error('Event listener error:', error);
            }
          });
        }
      },
      
      clear() {
        listeners.clear();
      }
    };
  }

  /**
   * 获取服务实例
   * @param {Symbol} type - 服务类型
   * @returns {any} 服务实例
   */
  getService(type) {
    if (!this.initialized) {
      throw new Error('ServiceFactory must be initialized before use');
    }
    
    return this.container.resolve(type);
  }

  /**
   * 检查服务是否已注册
   * @param {Symbol} type - 服务类型
   * @returns {boolean} 是否已注册
   */
  hasService(type) {
    return this.container.isRegistered(type);
  }

  /**
   * 获取容器实例
   * @returns {DIContainer} 容器实例
   */
  getContainer() {
    return this.container;
  }

  /**
   * 创建子工厂
   * @param {Object} config - 子工厂配置
   * @returns {ServiceFactory} 子工厂实例
   */
  createChild(config = {}) {
    const childFactory = new ServiceFactory({ ...this.config, ...config });
    childFactory.container = this.container.createChild();
    childFactory.initialized = this.initialized;
    return childFactory;
  }

  /**
   * 销毁工厂
   */
  dispose() {
    if (this.container) {
      this.container.dispose();
    }
    this.initialized = false;
  }
}

export default ServiceFactory;
