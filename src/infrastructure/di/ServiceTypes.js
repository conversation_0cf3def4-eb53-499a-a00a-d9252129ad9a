/**
 * 服务类型标识符定义
 * 使用Symbol确保唯一性和类型安全
 */

/**
 * 服务类型标识符
 * 用于依赖注入容器中的服务注册和解析
 */
export const TYPES = {
  // 基础设施层 - 存储相关
  IStorageRepository: Symbol.for('IStorageRepository'),
  ChromeStorageRepository: Symbol.for('ChromeStorageRepository'),
  
  // 基础设施层 - 缓存相关
  ICacheService: Symbol.for('ICacheService'),
  MemoryCacheService: Symbol.for('MemoryCacheService'),
  
  // 基础设施层 - 锁服务相关
  ILockService: Symbol.for('ILockService'),
  DistributedLockService: Symbol.for('DistributedLockService'),
  
  // 基础设施层 - 适配器相关
  IStorageAdapter: Symbol.for('IStorageAdapter'),
  ChromeStorageAdapter: Symbol.for('ChromeStorageAdapter'),
  ChromeRuntimeAdapter: Symbol.for('ChromeRuntimeAdapter'),
  ChromeTabsAdapter: Symbol.for('ChromeTabsAdapter'),
  
  // 应用层 - 服务相关
  ISettingsService: Symbol.for('ISettingsService'),
  SettingsService: Symbol.for('SettingsService'),
  IMemoryStorageService: Symbol.for('IMemoryStorageService'),
  MemoryStorageService: Symbol.for('MemoryStorageService'),
  
  // 领域层 - 仓储接口
  IMemoryRepository: Symbol.for('IMemoryRepository'),
  ICategoryRepository: Symbol.for('ICategoryRepository'),
  IUserRepository: Symbol.for('IUserRepository'),
  
  // 领域层 - 领域服务
  IMemoryDomainService: Symbol.for('IMemoryDomainService'),
  ISearchDomainService: Symbol.for('ISearchDomainService'),
  ISyncDomainService: Symbol.for('ISyncDomainService'),
  ICategoryDomainService: Symbol.for('ICategoryDomainService'),
  
  // 应用层 - 应用服务
  IMemoryApplicationService: Symbol.for('IMemoryApplicationService'),
  ICategoryApplicationService: Symbol.for('ICategoryApplicationService'),
  ISearchApplicationService: Symbol.for('ISearchApplicationService'),
  ISyncApplicationService: Symbol.for('ISyncApplicationService'),
  IUserApplicationService: Symbol.for('IUserApplicationService'),
  
  // 基础设施层 - 技术服务
  IEncryptionService: Symbol.for('IEncryptionService'),
  ISearchIndexService: Symbol.for('ISearchIndexService'),
  ICloudStorageService: Symbol.for('ICloudStorageService'),
  IConflictResolutionService: Symbol.for('IConflictResolutionService'),
  
  // 配置相关
  AppConfig: Symbol.for('AppConfig'),
  StorageConfig: Symbol.for('StorageConfig'),
  CacheConfig: Symbol.for('CacheConfig'),
  SecurityConfig: Symbol.for('SecurityConfig'),
  
  // 工厂相关
  ServiceFactory: Symbol.for('ServiceFactory'),
  RepositoryFactory: Symbol.for('RepositoryFactory'),
  AdapterFactory: Symbol.for('AdapterFactory'),
  
  // 系统服务
  DeviceIdProvider: Symbol.for('DeviceIdProvider'),
  Logger: Symbol.for('Logger'),
  EventBus: Symbol.for('EventBus'),
  
  // 外部服务集成
  HuaweiObsClient: Symbol.for('HuaweiObsClient'),
  MinioClient: Symbol.for('MinioClient'),
  
  // 迁移和工具服务
  IMigrationService: Symbol.for('IMigrationService'),
  IBackupService: Symbol.for('IBackupService'),
  IExportImportService: Symbol.for('IExportImportService'),
  
  // 性能和监控
  IPerformanceMonitor: Symbol.for('IPerformanceMonitor'),
  IHealthChecker: Symbol.for('IHealthChecker'),
  
  // 测试相关（仅在测试环境使用）
  MockStorageRepository: Symbol.for('MockStorageRepository'),
  MockCacheService: Symbol.for('MockCacheService'),
  MockLockService: Symbol.for('MockLockService')
};

/**
 * 服务生命周期类型
 */
export const LIFECYCLE = {
  SINGLETON: 'singleton',
  TRANSIENT: 'transient',
  SCOPED: 'scoped'
};

/**
 * 服务注册选项
 * @typedef {Object} RegisterOptions
 * @property {string} lifecycle - 服务生命周期
 * @property {string[]} [tags] - 服务标签
 * @property {boolean} [lazy] - 是否延迟初始化
 * @property {Function} [validator] - 服务验证器
 */

/**
 * 服务工厂函数类型
 * @typedef {Function} ServiceFactory
 * @param {IDIContainer} container - 依赖注入容器
 * @returns {any} 服务实例
 */

/**
 * 服务元数据
 * @typedef {Object} ServiceMetadata
 * @property {Symbol} type - 服务类型标识符
 * @property {ServiceFactory} factory - 服务工厂函数
 * @property {RegisterOptions} options - 注册选项
 * @property {any} [instance] - 单例实例（仅对单例服务）
 * @property {boolean} initialized - 是否已初始化
 */

/**
 * 检查是否为有效的服务类型
 * @param {any} type - 要检查的类型
 * @returns {boolean} 是否为有效的服务类型
 */
export function isValidServiceType(type) {
  return typeof type === 'symbol';
}

/**
 * 获取服务类型的字符串表示
 * @param {Symbol} type - 服务类型
 * @returns {string} 字符串表示
 */
export function getServiceTypeName(type) {
  if (!isValidServiceType(type)) {
    return 'Unknown';
  }
  
  const entry = Object.entries(TYPES).find(([, value]) => value === type);
  return entry ? entry[0] : type.toString();
}

/**
 * 根据名称获取服务类型
 * @param {string} name - 服务类型名称
 * @returns {Symbol|null} 服务类型，不存在时返回null
 */
export function getServiceTypeByName(name) {
  return TYPES[name] || null;
}

/**
 * 获取所有服务类型
 * @returns {Symbol[]} 所有服务类型数组
 */
export function getAllServiceTypes() {
  return Object.values(TYPES);
}

/**
 * 按分类获取服务类型
 * @param {string} category - 分类名称（infrastructure, application, domain等）
 * @returns {Symbol[]} 该分类下的服务类型数组
 */
export function getServiceTypesByCategory(category) {
  const categoryMap = {
    infrastructure: [
      TYPES.IStorageRepository, TYPES.ChromeStorageRepository,
      TYPES.ICacheService, TYPES.MemoryCacheService,
      TYPES.ILockService, TYPES.DistributedLockService,
      TYPES.IStorageAdapter, TYPES.ChromeStorageAdapter,
      TYPES.ChromeRuntimeAdapter, TYPES.ChromeTabsAdapter,
      TYPES.IEncryptionService, TYPES.ISearchIndexService,
      TYPES.ICloudStorageService, TYPES.IConflictResolutionService
    ],
    application: [
      TYPES.ISettingsService, TYPES.SettingsService,
      TYPES.IMemoryStorageService, TYPES.MemoryStorageService,
      TYPES.IMemoryApplicationService, TYPES.ICategoryApplicationService,
      TYPES.ISearchApplicationService, TYPES.ISyncApplicationService,
      TYPES.IUserApplicationService
    ],
    domain: [
      TYPES.IMemoryRepository, TYPES.ICategoryRepository, TYPES.IUserRepository,
      TYPES.IMemoryDomainService, TYPES.ISearchDomainService,
      TYPES.ISyncDomainService, TYPES.ICategoryDomainService
    ],
    config: [
      TYPES.AppConfig, TYPES.StorageConfig,
      TYPES.CacheConfig, TYPES.SecurityConfig
    ],
    factory: [
      TYPES.ServiceFactory, TYPES.RepositoryFactory, TYPES.AdapterFactory
    ],
    system: [
      TYPES.DeviceIdProvider, TYPES.Logger, TYPES.EventBus
    ],
    external: [
      TYPES.HuaweiObsClient, TYPES.MinioClient
    ],
    tools: [
      TYPES.IMigrationService, TYPES.IBackupService, TYPES.IExportImportService
    ],
    monitoring: [
      TYPES.IPerformanceMonitor, TYPES.IHealthChecker
    ],
    test: [
      TYPES.MockStorageRepository, TYPES.MockCacheService, TYPES.MockLockService
    ]
  };

  return categoryMap[category] || [];
}

export default TYPES;
