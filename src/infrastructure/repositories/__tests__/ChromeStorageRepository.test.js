import { ChromeStorageRepository } from '../ChromeStorageRepository.js';

// 模拟适配器工厂
jest.mock('@infrastructure/adapters/AdapterFactory.js', () => ({
  getLocalStorage: jest.fn(),
  getSyncStorage: jest.fn()
}));

import { getLocalStorage, getSyncStorage } from '@infrastructure/adapters/AdapterFactory.js';

describe('ChromeStorageRepository', () => {
  let repository;
  let mockAdapter;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // 创建模拟适配器
    mockAdapter = {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn(),
      has: jest.fn(),
      keys: jest.fn(),
      getUsage: jest.fn(),
      getBatch: jest.fn(),
      setBatch: jest.fn(),
      onChanged: jest.fn(),
      isAvailable: jest.fn()
    };

    getLocalStorage.mockReturnValue(mockAdapter);
    getSyncStorage.mockReturnValue(mockAdapter);
    
    repository = new ChromeStorageRepository('local');
  });

  describe('构造函数', () => {
    test('应该成功创建本地存储仓储', () => {
      expect(repository).toBeInstanceOf(ChromeStorageRepository);
      expect(repository.getType()).toBe('chrome-local');
      expect(repository.getStorageArea()).toBe('local');
      expect(getLocalStorage).toHaveBeenCalled();
    });

    test('应该成功创建同步存储仓储', () => {
      const syncRepository = new ChromeStorageRepository('sync');
      expect(syncRepository.getType()).toBe('chrome-sync');
      expect(syncRepository.getStorageArea()).toBe('sync');
      expect(getSyncStorage).toHaveBeenCalled();
    });
  });

  describe('get方法', () => {
    test('应该成功获取数据', async () => {
      const mockData = { testKey: 'testValue' };
      mockAdapter.get.mockResolvedValue(mockData);

      const result = await repository.get('testKey');
      
      expect(mockAdapter.get).toHaveBeenCalledWith('testKey');
      expect(result).toEqual(mockData);
    });

    test('当适配器抛出错误时应该包装错误', async () => {
      mockAdapter.get.mockRejectedValue(new Error('Adapter error'));

      await expect(repository.get('testKey')).rejects.toThrow(
        'Failed to get data from local storage: Adapter error'
      );
    });
  });

  describe('set方法', () => {
    test('应该成功设置数据', async () => {
      const testData = { key1: 'value1' };
      mockAdapter.set.mockResolvedValue();

      await repository.set(testData);
      
      expect(mockAdapter.set).toHaveBeenCalledWith(testData);
    });

    test('当传入无效数据时应该抛出错误', async () => {
      await expect(repository.set(null)).rejects.toThrow('Items must be a non-null object');
      await expect(repository.set('invalid')).rejects.toThrow('Items must be a non-null object');
    });

    test('当适配器抛出错误时应该包装错误', async () => {
      mockAdapter.set.mockRejectedValue(new Error('Adapter error'));

      await expect(repository.set({ key: 'value' })).rejects.toThrow(
        'Failed to set data to local storage: Adapter error'
      );
    });
  });

  describe('remove方法', () => {
    test('应该成功删除数据', async () => {
      mockAdapter.remove.mockResolvedValue();

      await repository.remove('testKey');
      
      expect(mockAdapter.remove).toHaveBeenCalledWith('testKey');
    });

    test('当keys参数为空时应该抛出错误', async () => {
      await expect(repository.remove()).rejects.toThrow('Keys parameter is required');
      await expect(repository.remove(null)).rejects.toThrow('Keys parameter is required');
    });
  });

  describe('clear方法', () => {
    test('应该成功清空数据', async () => {
      mockAdapter.clear.mockResolvedValue();

      await repository.clear();
      
      expect(mockAdapter.clear).toHaveBeenCalled();
    });
  });

  describe('has方法', () => {
    test('应该正确检查键是否存在', async () => {
      mockAdapter.has.mockResolvedValue(true);

      const result = await repository.has('testKey');
      
      expect(mockAdapter.has).toHaveBeenCalledWith('testKey');
      expect(result).toBe(true);
    });
  });

  describe('keys方法', () => {
    test('应该返回所有键', async () => {
      const mockKeys = ['key1', 'key2', 'key3'];
      mockAdapter.keys.mockResolvedValue(mockKeys);

      const result = await repository.keys();
      
      expect(mockAdapter.keys).toHaveBeenCalled();
      expect(result).toEqual(mockKeys);
    });
  });

  describe('getUsage方法', () => {
    test('应该返回存储使用情况', async () => {
      const mockUsage = { bytesInUse: 1024, quota: 10485760 };
      mockAdapter.getUsage.mockResolvedValue(mockUsage);

      const result = await repository.getUsage();
      
      expect(mockAdapter.getUsage).toHaveBeenCalled();
      expect(result).toEqual(mockUsage);
    });
  });

  describe('批量操作方法', () => {
    test('getBatch应该返回批量结果', async () => {
      const mockResult = [
        { key: 'key1', value: 'value1', exists: true },
        { key: 'key2', value: null, exists: false }
      ];
      mockAdapter.getBatch.mockResolvedValue(mockResult);

      const result = await repository.getBatch(['key1', 'key2']);
      
      expect(mockAdapter.getBatch).toHaveBeenCalledWith(['key1', 'key2']);
      expect(result).toEqual(mockResult);
    });

    test('getBatch当keys不是数组时应该抛出错误', async () => {
      await expect(repository.getBatch('not-array')).rejects.toThrow('Keys must be an array');
    });

    test('setBatch应该正确设置批量数据', async () => {
      const items = [
        { key: 'key1', value: 'value1' },
        { key: 'key2', value: 'value2' }
      ];
      mockAdapter.setBatch.mockResolvedValue();

      await repository.setBatch(items);
      
      expect(mockAdapter.setBatch).toHaveBeenCalledWith(items);
    });

    test('setBatch当items不是数组时应该抛出错误', async () => {
      await expect(repository.setBatch('not-array')).rejects.toThrow('Items must be an array');
    });
  });

  describe('onChanged方法', () => {
    test('应该成功添加变化监听器', () => {
      const callback = jest.fn();
      const mockUnsubscribe = jest.fn();
      mockAdapter.onChanged.mockReturnValue(mockUnsubscribe);

      const unsubscribe = repository.onChanged(callback);
      
      expect(mockAdapter.onChanged).toHaveBeenCalledWith(callback);
      expect(unsubscribe).toBe(mockUnsubscribe);
    });

    test('当callback不是函数时应该抛出错误', () => {
      expect(() => {
        repository.onChanged('not-function');
      }).toThrow('Callback must be a function');
    });
  });

  describe('isAvailable方法', () => {
    test('当存储可用时应该返回true', async () => {
      mockAdapter.isAvailable.mockResolvedValue(true);

      const result = await repository.isAvailable();
      
      expect(result).toBe(true);
    });

    test('当存储不可用时应该返回false', async () => {
      mockAdapter.isAvailable.mockRejectedValue(new Error('Not available'));

      const result = await repository.isAvailable();
      
      expect(result).toBe(false);
    });
  });

  describe('命名空间方法', () => {
    test('createNamespacedKey应该正确创建命名空间键', () => {
      const result = repository.createNamespacedKey('test', 'key');
      expect(result).toBe('test:key');
    });

    test('createNamespacedKey当namespace为空时应该返回原键', () => {
      const result = repository.createNamespacedKey('', 'key');
      expect(result).toBe('key');
    });

    test('parseNamespacedKey应该正确解析命名空间键', () => {
      const result = repository.parseNamespacedKey('test:key');
      expect(result).toEqual({ namespace: 'test', key: 'key' });
    });

    test('parseNamespacedKey当没有命名空间时应该返回null', () => {
      const result = repository.parseNamespacedKey('key');
      expect(result).toEqual({ namespace: null, key: 'key' });
    });

    test('getByNamespace应该返回命名空间下的数据', async () => {
      const mockKeys = ['test:key1', 'test:key2', 'other:key3'];
      const mockData = {
        'test:key1': 'value1',
        'test:key2': 'value2'
      };
      
      mockAdapter.keys.mockResolvedValue(mockKeys);
      mockAdapter.get.mockResolvedValue(mockData);

      const result = await repository.getByNamespace('test');
      
      expect(result).toEqual({
        key1: 'value1',
        key2: 'value2'
      });
    });

    test('removeByNamespace应该删除命名空间下的数据', async () => {
      const mockKeys = ['test:key1', 'test:key2', 'other:key3'];
      
      mockAdapter.keys.mockResolvedValue(mockKeys);
      mockAdapter.remove.mockResolvedValue();

      const result = await repository.removeByNamespace('test');
      
      expect(mockAdapter.remove).toHaveBeenCalledWith(['test:key1', 'test:key2']);
      expect(result).toBe(2);
    });
  });

  describe('getAdapter方法', () => {
    test('应该返回适配器实例', () => {
      const adapter = repository.getAdapter();
      expect(adapter).toBe(mockAdapter);
    });
  });
});
