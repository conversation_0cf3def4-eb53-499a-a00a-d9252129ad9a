/**
 * 存储仓储接口
 * 定义了统一的数据存储操作接口
 */
export class IStorageRepository {
  /**
   * 获取数据
   * @param {string|string[]} keys - 要获取的键或键数组
   * @returns {Promise<Object>} 包含键值对的对象
   */
  async get(keys) {
    throw new Error('Method get() must be implemented by subclass');
  }

  /**
   * 设置数据
   * @param {Object} items - 要存储的键值对对象
   * @returns {Promise<void>}
   */
  async set(items) {
    throw new Error('Method set() must be implemented by subclass');
  }

  /**
   * 删除数据
   * @param {string|string[]} keys - 要删除的键或键数组
   * @returns {Promise<void>}
   */
  async remove(keys) {
    throw new Error('Method remove() must be implemented by subclass');
  }

  /**
   * 清空所有数据
   * @returns {Promise<void>}
   */
  async clear() {
    throw new Error('Method clear() must be implemented by subclass');
  }

  /**
   * 检查键是否存在
   * @param {string} key - 要检查的键
   * @returns {Promise<boolean>} 是否存在
   */
  async has(key) {
    throw new Error('Method has() must be implemented by subclass');
  }

  /**
   * 获取所有键
   * @returns {Promise<string[]>} 所有键的数组
   */
  async keys() {
    throw new Error('Method keys() must be implemented by subclass');
  }

  /**
   * 获取存储使用情况
   * @returns {Promise<Object>} 存储使用信息
   */
  async getUsage() {
    throw new Error('Method getUsage() must be implemented by subclass');
  }

  /**
   * 批量获取数据
   * @param {string[]} keys - 键数组
   * @returns {Promise<Object[]>} 结果数组
   */
  async getBatch(keys) {
    throw new Error('Method getBatch() must be implemented by subclass');
  }

  /**
   * 批量设置数据
   * @param {Array<{key: string, value: any}>} items - 键值对数组
   * @returns {Promise<void>}
   */
  async setBatch(items) {
    throw new Error('Method setBatch() must be implemented by subclass');
  }

  /**
   * 监听数据变化
   * @param {Function} callback - 变化回调函数
   * @returns {Function} 取消监听的函数
   */
  onChanged(callback) {
    throw new Error('Method onChanged() must be implemented by subclass');
  }

  /**
   * 检查存储是否可用
   * @returns {Promise<boolean>} 是否可用
   */
  async isAvailable() {
    throw new Error('Method isAvailable() must be implemented by subclass');
  }

  /**
   * 获取存储类型
   * @returns {string} 存储类型
   */
  getType() {
    throw new Error('Method getType() must be implemented by subclass');
  }
}
