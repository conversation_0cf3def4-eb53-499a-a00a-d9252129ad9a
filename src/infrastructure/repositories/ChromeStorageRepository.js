import { IStorageRepository } from './IStorageRepository.js';
import { getLocalStorage, getSyncStorage } from '@infrastructure/adapters/AdapterFactory.js';

/**
 * Chrome存储仓储实现
 * 基于Chrome API适配器的存储仓储
 */
export class ChromeStorageRepository extends IStorageRepository {
  /**
   * 构造函数
   * @param {string} storageType - 存储类型 ('local' 或 'sync')
   */
  constructor(storageType = 'local') {
    super();
    this.storageType = storageType;
    this.adapter = storageType === 'sync' ? getSyncStorage() : getLocalStorage();
  }

  /**
   * 获取数据
   * @param {string|string[]} keys - 要获取的键或键数组
   * @returns {Promise<Object>} 包含键值对的对象
   */
  async get(keys) {
    try {
      return await this.adapter.get(keys);
    } catch (error) {
      throw new Error(`Failed to get data from ${this.storageType} storage: ${error.message}`);
    }
  }

  /**
   * 设置数据
   * @param {Object} items - 要存储的键值对对象
   * @returns {Promise<void>}
   */
  async set(items) {
    if (!items || typeof items !== 'object') {
      throw new Error('Items must be a non-null object');
    }

    try {
      await this.adapter.set(items);
    } catch (error) {
      throw new Error(`Failed to set data to ${this.storageType} storage: ${error.message}`);
    }
  }

  /**
   * 删除数据
   * @param {string|string[]} keys - 要删除的键或键数组
   * @returns {Promise<void>}
   */
  async remove(keys) {
    if (!keys) {
      throw new Error('Keys parameter is required');
    }

    try {
      await this.adapter.remove(keys);
    } catch (error) {
      throw new Error(`Failed to remove data from ${this.storageType} storage: ${error.message}`);
    }
  }

  /**
   * 清空所有数据
   * @returns {Promise<void>}
   */
  async clear() {
    try {
      await this.adapter.clear();
    } catch (error) {
      throw new Error(`Failed to clear ${this.storageType} storage: ${error.message}`);
    }
  }

  /**
   * 检查键是否存在
   * @param {string} key - 要检查的键
   * @returns {Promise<boolean>} 是否存在
   */
  async has(key) {
    try {
      return await this.adapter.has(key);
    } catch (error) {
      throw new Error(`Failed to check key existence in ${this.storageType} storage: ${error.message}`);
    }
  }

  /**
   * 获取所有键
   * @returns {Promise<string[]>} 所有键的数组
   */
  async keys() {
    try {
      return await this.adapter.keys();
    } catch (error) {
      throw new Error(`Failed to get keys from ${this.storageType} storage: ${error.message}`);
    }
  }

  /**
   * 获取存储使用情况
   * @returns {Promise<Object>} 存储使用信息
   */
  async getUsage() {
    try {
      return await this.adapter.getUsage();
    } catch (error) {
      throw new Error(`Failed to get usage info from ${this.storageType} storage: ${error.message}`);
    }
  }

  /**
   * 批量获取数据
   * @param {string[]} keys - 键数组
   * @returns {Promise<Object[]>} 结果数组
   */
  async getBatch(keys) {
    if (!Array.isArray(keys)) {
      throw new Error('Keys must be an array');
    }

    try {
      return await this.adapter.getBatch(keys);
    } catch (error) {
      throw new Error(`Failed to get batch data from ${this.storageType} storage: ${error.message}`);
    }
  }

  /**
   * 批量设置数据
   * @param {Array<{key: string, value: any}>} items - 键值对数组
   * @returns {Promise<void>}
   */
  async setBatch(items) {
    if (!Array.isArray(items)) {
      throw new Error('Items must be an array');
    }

    try {
      await this.adapter.setBatch(items);
    } catch (error) {
      throw new Error(`Failed to set batch data to ${this.storageType} storage: ${error.message}`);
    }
  }

  /**
   * 监听数据变化
   * @param {Function} callback - 变化回调函数
   * @returns {Function} 取消监听的函数
   */
  onChanged(callback) {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    try {
      return this.adapter.onChanged(callback);
    } catch (error) {
      throw new Error(`Failed to add change listener to ${this.storageType} storage: ${error.message}`);
    }
  }

  /**
   * 检查存储是否可用
   * @returns {Promise<boolean>} 是否可用
   */
  async isAvailable() {
    try {
      return await this.adapter.isAvailable();
    } catch (error) {
      console.warn(`${this.storageType} storage availability check failed:`, error);
      return false;
    }
  }

  /**
   * 获取存储类型
   * @returns {string} 存储类型
   */
  getType() {
    return `chrome-${this.storageType}`;
  }

  /**
   * 获取存储适配器
   * @returns {Object} 存储适配器实例
   */
  getAdapter() {
    return this.adapter;
  }

  /**
   * 获取存储区域名称
   * @returns {string} 存储区域名称
   */
  getStorageArea() {
    return this.storageType;
  }

  /**
   * 创建命名空间键
   * @param {string} namespace - 命名空间
   * @param {string} key - 原始键
   * @returns {string} 命名空间键
   */
  createNamespacedKey(namespace, key) {
    if (!namespace) return key;
    return `${namespace}:${key}`;
  }

  /**
   * 解析命名空间键
   * @param {string} namespacedKey - 命名空间键
   * @returns {Object} 解析结果 {namespace, key}
   */
  parseNamespacedKey(namespacedKey) {
    const parts = namespacedKey.split(':');
    if (parts.length < 2) {
      return { namespace: null, key: namespacedKey };
    }
    return {
      namespace: parts[0],
      key: parts.slice(1).join(':')
    };
  }

  /**
   * 按命名空间获取数据
   * @param {string} namespace - 命名空间
   * @returns {Promise<Object>} 命名空间下的所有数据
   */
  async getByNamespace(namespace) {
    if (!namespace) {
      throw new Error('Namespace is required');
    }

    try {
      const allKeys = await this.keys();
      const namespaceKeys = allKeys.filter(key => key.startsWith(`${namespace}:`));
      
      if (namespaceKeys.length === 0) {
        return {};
      }

      const result = await this.get(namespaceKeys);
      const namespacedResult = {};

      for (const [key, value] of Object.entries(result)) {
        const { key: originalKey } = this.parseNamespacedKey(key);
        namespacedResult[originalKey] = value;
      }

      return namespacedResult;
    } catch (error) {
      throw new Error(`Failed to get data by namespace '${namespace}': ${error.message}`);
    }
  }

  /**
   * 按命名空间删除数据
   * @param {string} namespace - 命名空间
   * @returns {Promise<number>} 删除的键数量
   */
  async removeByNamespace(namespace) {
    if (!namespace) {
      throw new Error('Namespace is required');
    }

    try {
      const allKeys = await this.keys();
      const namespaceKeys = allKeys.filter(key => key.startsWith(`${namespace}:`));
      
      if (namespaceKeys.length === 0) {
        return 0;
      }

      await this.remove(namespaceKeys);
      return namespaceKeys.length;
    } catch (error) {
      throw new Error(`Failed to remove data by namespace '${namespace}': ${error.message}`);
    }
  }
}
