import { ChromeStorageAdapter } from './ChromeStorageAdapter.js';
import { ChromeRuntimeAdapter } from './ChromeRuntimeAdapter.js';
import { ChromeTabsAdapter } from './ChromeTabsAdapter.js';

/**
 * 适配器工厂
 * 负责创建和管理各种Chrome API适配器实例
 */
export class AdapterFactory {
  constructor() {
    this._storageAdapters = new Map();
    this._runtimeAdapter = null;
    this._tabsAdapter = null;
  }

  /**
   * 创建存储适配器
   * @param {string} storageArea - 存储区域 ('local' 或 'sync')
   * @returns {ChromeStorageAdapter} 存储适配器实例
   */
  createStorageAdapter(storageArea = 'local') {
    // 使用单例模式，避免重复创建
    if (!this._storageAdapters.has(storageArea)) {
      try {
        const adapter = new ChromeStorageAdapter(storageArea);
        this._storageAdapters.set(storageArea, adapter);
      } catch (error) {
        throw new Error(`Failed to create storage adapter for ${storageArea}: ${error.message}`);
      }
    }
    
    return this._storageAdapters.get(storageArea);
  }

  /**
   * 创建运行时适配器
   * @returns {ChromeRuntimeAdapter} 运行时适配器实例
   */
  createRuntimeAdapter() {
    if (!this._runtimeAdapter) {
      try {
        this._runtimeAdapter = new ChromeRuntimeAdapter();
      } catch (error) {
        throw new Error(`Failed to create runtime adapter: ${error.message}`);
      }
    }
    
    return this._runtimeAdapter;
  }

  /**
   * 创建标签页适配器
   * @returns {ChromeTabsAdapter} 标签页适配器实例
   */
  createTabsAdapter() {
    if (!this._tabsAdapter) {
      try {
        this._tabsAdapter = new ChromeTabsAdapter();
      } catch (error) {
        throw new Error(`Failed to create tabs adapter: ${error.message}`);
      }
    }
    
    return this._tabsAdapter;
  }

  /**
   * 获取本地存储适配器
   * @returns {ChromeStorageAdapter} 本地存储适配器
   */
  getLocalStorageAdapter() {
    return this.createStorageAdapter('local');
  }

  /**
   * 获取同步存储适配器
   * @returns {ChromeStorageAdapter} 同步存储适配器
   */
  getSyncStorageAdapter() {
    return this.createStorageAdapter('sync');
  }

  /**
   * 获取运行时适配器
   * @returns {ChromeRuntimeAdapter} 运行时适配器
   */
  getRuntimeAdapter() {
    return this.createRuntimeAdapter();
  }

  /**
   * 获取标签页适配器
   * @returns {ChromeTabsAdapter} 标签页适配器
   */
  getTabsAdapter() {
    return this.createTabsAdapter();
  }

  /**
   * 检查Chrome扩展环境是否可用
   * @returns {Object} 可用性检查结果
   */
  checkAvailability() {
    const result = {
      chrome: !!(typeof chrome !== 'undefined'),
      storage: !!(chrome?.storage),
      storageLocal: !!(chrome?.storage?.local),
      storageSync: !!(chrome?.storage?.sync),
      runtime: !!(chrome?.runtime),
      tabs: !!(chrome?.tabs),
      overall: false
    };

    // 整体可用性取决于基本的chrome对象和storage API
    result.overall = result.chrome && result.storage && result.storageLocal && result.runtime;

    return result;
  }

  /**
   * 验证适配器功能
   * @returns {Promise<Object>} 验证结果
   */
  async validateAdapters() {
    const results = {
      localStorage: { available: false, error: null },
      syncStorage: { available: false, error: null },
      runtime: { available: false, error: null },
      tabs: { available: false, error: null }
    };

    // 测试本地存储适配器
    try {
      const localAdapter = this.getLocalStorageAdapter();
      results.localStorage.available = await localAdapter.isAvailable();
    } catch (error) {
      results.localStorage.error = error.message;
    }

    // 测试同步存储适配器
    try {
      const syncAdapter = this.getSyncStorageAdapter();
      results.syncStorage.available = await syncAdapter.isAvailable();
    } catch (error) {
      results.syncStorage.error = error.message;
    }

    // 测试运行时适配器
    try {
      const runtimeAdapter = this.getRuntimeAdapter();
      results.runtime.available = runtimeAdapter.isAvailable();
    } catch (error) {
      results.runtime.error = error.message;
    }

    // 测试标签页适配器
    try {
      const tabsAdapter = this.getTabsAdapter();
      results.tabs.available = tabsAdapter.isAvailable();
    } catch (error) {
      results.tabs.error = error.message;
    }

    return results;
  }

  /**
   * 清理所有适配器实例
   */
  cleanup() {
    this._storageAdapters.clear();
    this._runtimeAdapter = null;
    this._tabsAdapter = null;
  }

  /**
   * 重置工厂状态
   */
  reset() {
    this.cleanup();
  }

  /**
   * 获取所有已创建的适配器信息
   * @returns {Object} 适配器信息
   */
  getAdapterInfo() {
    return {
      storageAdapters: Array.from(this._storageAdapters.keys()),
      hasRuntimeAdapter: !!this._runtimeAdapter,
      hasTabsAdapter: !!this._tabsAdapter,
      totalAdapters: this._storageAdapters.size + 
                    (this._runtimeAdapter ? 1 : 0) + 
                    (this._tabsAdapter ? 1 : 0)
    };
  }
}

// 导出单例实例
export const adapterFactory = new AdapterFactory();

// 导出便捷函数
export const createStorageAdapter = (storageArea) => adapterFactory.createStorageAdapter(storageArea);
export const createRuntimeAdapter = () => adapterFactory.createRuntimeAdapter();
export const createTabsAdapter = () => adapterFactory.createTabsAdapter();

// 导出常用适配器获取函数
export const getLocalStorage = () => adapterFactory.getLocalStorageAdapter();
export const getSyncStorage = () => adapterFactory.getSyncStorageAdapter();
export const getRuntime = () => adapterFactory.getRuntimeAdapter();
export const getTabs = () => adapterFactory.getTabsAdapter();
