/**
 * 存储适配器接口
 * 定义了统一的存储操作接口，隔离具体的存储实现
 */
export class IStorageAdapter {
  /**
   * 获取存储数据
   * @param {string|string[]} keys - 要获取的键或键数组
   * @returns {Promise<Object>} 包含键值对的对象
   */
  async get(keys) {
    throw new Error('Method get() must be implemented by subclass');
  }

  /**
   * 设置存储数据
   * @param {Object} items - 要存储的键值对对象
   * @returns {Promise<void>}
   */
  async set(items) {
    throw new Error('Method set() must be implemented by subclass');
  }

  /**
   * 删除存储数据
   * @param {string|string[]} keys - 要删除的键或键数组
   * @returns {Promise<void>}
   */
  async remove(keys) {
    throw new Error('Method remove() must be implemented by subclass');
  }

  /**
   * 清空所有存储数据
   * @returns {Promise<void>}
   */
  async clear() {
    throw new Error('Method clear() must be implemented by subclass');
  }

  /**
   * 获取存储使用情况
   * @returns {Promise<Object>} 存储使用信息
   */
  async getUsage() {
    throw new Error('Method getUsage() must be implemented by subclass');
  }

  /**
   * 监听存储变化
   * @param {Function} callback - 变化回调函数
   * @returns {Function} 取消监听的函数
   */
  onChanged(callback) {
    throw new Error('Method onChanged() must be implemented by subclass');
  }

  /**
   * 检查存储是否可用
   * @returns {Promise<boolean>} 是否可用
   */
  async isAvailable() {
    throw new Error('Method isAvailable() must be implemented by subclass');
  }
}
