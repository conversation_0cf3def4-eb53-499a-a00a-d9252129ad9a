import { IStorageAdapter } from './IStorageAdapter.js';

/**
 * Chrome存储适配器
 * 封装Chrome扩展的storage API，提供Promise接口
 */
export class ChromeStorageAdapter extends IStorageAdapter {
  /**
   * 构造函数
   * @param {string} storageArea - 存储区域 ('local' 或 'sync')
   */
  constructor(storageArea = 'local') {
    super();
    this.storageArea = storageArea;
    this.storage = chrome?.storage?.[storageArea];
    
    if (!this.storage) {
      throw new Error(`Chrome storage.${storageArea} is not available`);
    }
  }

  /**
   * 获取存储数据
   * @param {string|string[]|Object} keys - 要获取的键
   * @returns {Promise<Object>} 包含键值对的对象
   */
  async get(keys) {
    return new Promise((resolve, reject) => {
      try {
        this.storage.get(keys, (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(result || {});
          }
        });
      } catch (error) {
        reject(new Error(`Failed to get storage data: ${error.message}`));
      }
    });
  }

  /**
   * 设置存储数据
   * @param {Object} items - 要存储的键值对对象
   * @returns {Promise<void>}
   */
  async set(items) {
    if (!items || typeof items !== 'object') {
      throw new Error('Items must be a non-null object');
    }

    return new Promise((resolve, reject) => {
      try {
        this.storage.set(items, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } catch (error) {
        reject(new Error(`Failed to set storage data: ${error.message}`));
      }
    });
  }

  /**
   * 删除存储数据
   * @param {string|string[]} keys - 要删除的键或键数组
   * @returns {Promise<void>}
   */
  async remove(keys) {
    if (!keys) {
      throw new Error('Keys parameter is required');
    }

    return new Promise((resolve, reject) => {
      try {
        this.storage.remove(keys, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } catch (error) {
        reject(new Error(`Failed to remove storage data: ${error.message}`));
      }
    });
  }

  /**
   * 清空所有存储数据
   * @returns {Promise<void>}
   */
  async clear() {
    return new Promise((resolve, reject) => {
      try {
        this.storage.clear(() => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } catch (error) {
        reject(new Error(`Failed to clear storage: ${error.message}`));
      }
    });
  }

  /**
   * 获取存储使用情况
   * @returns {Promise<Object>} 存储使用信息
   */
  async getUsage() {
    // 只有local storage支持getBytesInUse
    if (this.storageArea !== 'local') {
      return { bytesInUse: 0, quota: 0 };
    }

    return new Promise((resolve, reject) => {
      try {
        this.storage.getBytesInUse(null, (bytesInUse) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            // Chrome local storage quota is approximately 10MB
            const quota = 10 * 1024 * 1024;
            resolve({
              bytesInUse,
              quota,
              percentUsed: (bytesInUse / quota) * 100
            });
          }
        });
      } catch (error) {
        reject(new Error(`Failed to get storage usage: ${error.message}`));
      }
    });
  }

  /**
   * 监听存储变化
   * @param {Function} callback - 变化回调函数
   * @returns {Function} 取消监听的函数
   */
  onChanged(callback) {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    const listener = (changes, areaName) => {
      if (areaName === this.storageArea) {
        callback(changes, areaName);
      }
    };

    chrome.storage.onChanged.addListener(listener);

    // 返回取消监听的函数
    return () => {
      chrome.storage.onChanged.removeListener(listener);
    };
  }

  /**
   * 检查存储是否可用
   * @returns {Promise<boolean>} 是否可用
   */
  async isAvailable() {
    try {
      // 尝试进行一个简单的操作来检查可用性
      await this.get('__availability_check__');
      return true;
    } catch (error) {
      console.warn(`Chrome storage.${this.storageArea} is not available:`, error);
      return false;
    }
  }

  /**
   * 批量操作：获取多个键的数据
   * @param {string[]} keys - 键数组
   * @returns {Promise<Object[]>} 结果数组
   */
  async getBatch(keys) {
    if (!Array.isArray(keys)) {
      throw new Error('Keys must be an array');
    }

    const result = await this.get(keys);
    return keys.map(key => ({
      key,
      value: result[key] || null,
      exists: key in result
    }));
  }

  /**
   * 批量操作：设置多个键值对
   * @param {Array<{key: string, value: any}>} items - 键值对数组
   * @returns {Promise<void>}
   */
  async setBatch(items) {
    if (!Array.isArray(items)) {
      throw new Error('Items must be an array');
    }

    const data = {};
    items.forEach(item => {
      if (!item.key) {
        throw new Error('Each item must have a key property');
      }
      data[item.key] = item.value;
    });

    await this.set(data);
  }

  /**
   * 检查键是否存在
   * @param {string} key - 要检查的键
   * @returns {Promise<boolean>} 是否存在
   */
  async has(key) {
    const result = await this.get(key);
    return key in result;
  }

  /**
   * 获取所有键
   * @returns {Promise<string[]>} 所有键的数组
   */
  async keys() {
    const result = await this.get(null);
    return Object.keys(result);
  }

  /**
   * 获取存储区域名称
   * @returns {string} 存储区域名称
   */
  getStorageArea() {
    return this.storageArea;
  }
}
