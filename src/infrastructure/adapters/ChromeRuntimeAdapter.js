/**
 * Chrome运行时适配器
 * 封装Chrome扩展的runtime API，提供Promise接口
 */
export class ChromeRuntimeAdapter {
  constructor() {
    if (!chrome?.runtime) {
      throw new Error('Chrome runtime API is not available');
    }
  }

  /**
   * 发送消息
   * @param {any} message - 要发送的消息
   * @param {Object} options - 发送选项
   * @returns {Promise<any>} 响应数据
   */
  async sendMessage(message, options = {}) {
    return new Promise((resolve, reject) => {
      try {
        const { tabId, extensionId, timeout = 5000 } = options;
        
        // 设置超时
        const timeoutId = setTimeout(() => {
          reject(new Error('Message timeout'));
        }, timeout);

        const responseCallback = (response) => {
          clearTimeout(timeoutId);
          
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response);
          }
        };

        // 根据选项决定发送方式
        if (tabId) {
          chrome.tabs.sendMessage(tabId, message, responseCallback);
        } else if (extensionId) {
          chrome.runtime.sendMessage(extensionId, message, responseCallback);
        } else {
          chrome.runtime.sendMessage(message, responseCallback);
        }
      } catch (error) {
        reject(new Error(`Failed to send message: ${error.message}`));
      }
    });
  }

  /**
   * 监听消息
   * @param {Function} callback - 消息回调函数
   * @returns {Function} 取消监听的函数
   */
  onMessage(callback) {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    const listener = (message, sender, sendResponse) => {
      try {
        const result = callback(message, sender, sendResponse);
        
        // 如果回调返回Promise，处理异步响应
        if (result instanceof Promise) {
          result
            .then(response => sendResponse(response))
            .catch(error => sendResponse({ error: error.message }));
          return true; // 保持消息通道开放
        }
        
        return result;
      } catch (error) {
        sendResponse({ error: error.message });
        return false;
      }
    };

    chrome.runtime.onMessage.addListener(listener);

    // 返回取消监听的函数
    return () => {
      chrome.runtime.onMessage.removeListener(listener);
    };
  }

  /**
   * 获取扩展URL
   * @param {string} path - 相对路径
   * @returns {string} 完整URL
   */
  getURL(path) {
    if (typeof path !== 'string') {
      throw new Error('Path must be a string');
    }
    
    return chrome.runtime.getURL(path);
  }

  /**
   * 获取扩展ID
   * @returns {string} 扩展ID
   */
  getId() {
    return chrome.runtime.id;
  }

  /**
   * 获取扩展信息
   * @returns {Object} 扩展清单信息
   */
  getManifest() {
    return chrome.runtime.getManifest();
  }

  /**
   * 重新加载扩展
   * @returns {void}
   */
  reload() {
    chrome.runtime.reload();
  }

  /**
   * 监听扩展启动
   * @param {Function} callback - 启动回调函数
   * @returns {Function} 取消监听的函数
   */
  onStartup(callback) {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    chrome.runtime.onStartup.addListener(callback);

    return () => {
      chrome.runtime.onStartup.removeListener(callback);
    };
  }

  /**
   * 监听扩展安装
   * @param {Function} callback - 安装回调函数
   * @returns {Function} 取消监听的函数
   */
  onInstalled(callback) {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    chrome.runtime.onInstalled.addListener(callback);

    return () => {
      chrome.runtime.onInstalled.removeListener(callback);
    };
  }

  /**
   * 监听扩展挂起
   * @param {Function} callback - 挂起回调函数
   * @returns {Function} 取消监听的函数
   */
  onSuspend(callback) {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    chrome.runtime.onSuspend.addListener(callback);

    return () => {
      chrome.runtime.onSuspend.removeListener(callback);
    };
  }

  /**
   * 监听外部连接
   * @param {Function} callback - 连接回调函数
   * @returns {Function} 取消监听的函数
   */
  onConnect(callback) {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    chrome.runtime.onConnect.addListener(callback);

    return () => {
      chrome.runtime.onConnect.removeListener(callback);
    };
  }

  /**
   * 连接到扩展
   * @param {Object} options - 连接选项
   * @returns {Object} 端口对象
   */
  connect(options = {}) {
    const { extensionId, name } = options;
    
    if (extensionId) {
      return chrome.runtime.connect(extensionId, { name });
    } else {
      return chrome.runtime.connect({ name });
    }
  }

  /**
   * 获取后台页面
   * @returns {Promise<Window>} 后台页面窗口对象
   */
  async getBackgroundPage() {
    return new Promise((resolve, reject) => {
      try {
        chrome.runtime.getBackgroundPage((backgroundPage) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(backgroundPage);
          }
        });
      } catch (error) {
        reject(new Error(`Failed to get background page: ${error.message}`));
      }
    });
  }

  /**
   * 打开选项页面
   * @returns {Promise<void>}
   */
  async openOptionsPage() {
    return new Promise((resolve, reject) => {
      try {
        chrome.runtime.openOptionsPage(() => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } catch (error) {
        reject(new Error(`Failed to open options page: ${error.message}`));
      }
    });
  }

  /**
   * 设置卸载URL
   * @param {string} url - 卸载后跳转的URL
   * @returns {Promise<void>}
   */
  async setUninstallURL(url) {
    return new Promise((resolve, reject) => {
      try {
        chrome.runtime.setUninstallURL(url, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } catch (error) {
        reject(new Error(`Failed to set uninstall URL: ${error.message}`));
      }
    });
  }

  /**
   * 检查运行时是否可用
   * @returns {boolean} 是否可用
   */
  isAvailable() {
    return !!(chrome && chrome.runtime);
  }

  /**
   * 获取平台信息
   * @returns {Promise<Object>} 平台信息
   */
  async getPlatformInfo() {
    return new Promise((resolve, reject) => {
      try {
        chrome.runtime.getPlatformInfo((info) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(info);
          }
        });
      } catch (error) {
        reject(new Error(`Failed to get platform info: ${error.message}`));
      }
    });
  }

  /**
   * 获取包目录入口
   * @returns {Promise<DirectoryEntry>} 目录入口
   */
  async getPackageDirectoryEntry() {
    return new Promise((resolve, reject) => {
      try {
        chrome.runtime.getPackageDirectoryEntry((directoryEntry) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(directoryEntry);
          }
        });
      } catch (error) {
        reject(new Error(`Failed to get package directory: ${error.message}`));
      }
    });
  }
}
