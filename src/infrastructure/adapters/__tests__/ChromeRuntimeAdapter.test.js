import { ChromeRuntimeAdapter } from '../ChromeRuntimeAdapter.js';

describe('ChromeRuntimeAdapter', () => {
  let adapter;

  beforeEach(() => {
    jest.clearAllMocks();
    chrome.runtime.lastError = null;
    adapter = new ChromeRuntimeAdapter();
  });

  describe('构造函数', () => {
    test('应该成功创建运行时适配器', () => {
      expect(adapter).toBeInstanceOf(ChromeRuntimeAdapter);
    });

    test('当Chrome runtime不可用时应该抛出错误', () => {
      const originalRuntime = chrome.runtime;
      chrome.runtime = undefined;

      expect(() => {
        new ChromeRuntimeAdapter();
      }).toThrow('Chrome runtime API is not available');

      chrome.runtime = originalRuntime;
    });
  });

  describe('sendMessage方法', () => {
    test('应该成功发送消息', async () => {
      const mockResponse = { success: true, data: 'test' };
      chrome.runtime.sendMessage.mockImplementation((message, callback) => {
        setTimeout(() => callback(mockResponse), 10);
      });

      const response = await adapter.sendMessage({ type: 'test' });
      
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        { type: 'test' },
        expect.any(Function)
      );
      expect(response).toEqual(mockResponse);
    });

    test('应该支持向特定标签页发送消息', async () => {
      const mockResponse = { success: true };
      chrome.tabs.sendMessage.mockImplementation((tabId, message, callback) => {
        setTimeout(() => callback(mockResponse), 10);
      });

      const response = await adapter.sendMessage(
        { type: 'test' },
        { tabId: 123 }
      );
      
      expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(
        123,
        { type: 'test' },
        expect.any(Function)
      );
      expect(response).toEqual(mockResponse);
    });

    test('应该支持向特定扩展发送消息', async () => {
      const mockResponse = { success: true };
      chrome.runtime.sendMessage.mockImplementation((extensionId, message, callback) => {
        setTimeout(() => callback(mockResponse), 10);
      });

      const response = await adapter.sendMessage(
        { type: 'test' },
        { extensionId: 'test-extension-id' }
      );
      
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        'test-extension-id',
        { type: 'test' },
        expect.any(Function)
      );
      expect(response).toEqual(mockResponse);
    });

    test('应该在超时时拒绝Promise', async () => {
      chrome.runtime.sendMessage.mockImplementation(() => {
        // 不调用callback，模拟超时
      });

      await expect(
        adapter.sendMessage({ type: 'test' }, { timeout: 100 })
      ).rejects.toThrow('Message timeout');
    });

    test('当Chrome API返回错误时应该拒绝Promise', async () => {
      chrome.runtime.lastError = { message: 'Message failed' };
      chrome.runtime.sendMessage.mockImplementation((message, callback) => {
        callback(null);
      });

      await expect(
        adapter.sendMessage({ type: 'test' })
      ).rejects.toThrow('Message failed');
    });
  });

  describe('onMessage方法', () => {
    test('应该成功添加消息监听器', () => {
      const callback = jest.fn();
      const removeListener = adapter.onMessage(callback);

      expect(chrome.runtime.onMessage.addListener).toHaveBeenCalled();
      expect(typeof removeListener).toBe('function');
    });

    test('应该正确处理同步回调', () => {
      const callback = jest.fn().mockReturnValue('sync response');
      adapter.onMessage(callback);

      // 获取添加的监听器
      const addedListener = chrome.runtime.onMessage.addListener.mock.calls[0][0];
      const mockSendResponse = jest.fn();
      
      const result = addedListener(
        { type: 'test' },
        { tab: { id: 1 } },
        mockSendResponse
      );
      
      expect(callback).toHaveBeenCalledWith(
        { type: 'test' },
        { tab: { id: 1 } },
        mockSendResponse
      );
      expect(result).toBe('sync response');
    });

    test('应该正确处理异步回调', () => {
      const callback = jest.fn().mockResolvedValue('async response');
      adapter.onMessage(callback);

      const addedListener = chrome.runtime.onMessage.addListener.mock.calls[0][0];
      const mockSendResponse = jest.fn();
      
      const result = addedListener(
        { type: 'test' },
        { tab: { id: 1 } },
        mockSendResponse
      );
      
      expect(result).toBe(true); // 应该返回true保持消息通道开放
      
      // 等待Promise解决
      return new Promise(resolve => {
        setTimeout(() => {
          expect(mockSendResponse).toHaveBeenCalledWith('async response');
          resolve();
        }, 10);
      });
    });

    test('应该处理回调中的错误', () => {
      const callback = jest.fn().mockImplementation(() => {
        throw new Error('Callback error');
      });
      adapter.onMessage(callback);

      const addedListener = chrome.runtime.onMessage.addListener.mock.calls[0][0];
      const mockSendResponse = jest.fn();
      
      const result = addedListener(
        { type: 'test' },
        { tab: { id: 1 } },
        mockSendResponse
      );
      
      expect(mockSendResponse).toHaveBeenCalledWith({ error: 'Callback error' });
      expect(result).toBe(false);
    });

    test('当callback不是函数时应该抛出错误', () => {
      expect(() => {
        adapter.onMessage('not a function');
      }).toThrow('Callback must be a function');
    });
  });

  describe('基本信息方法', () => {
    test('getURL应该返回正确的扩展URL', () => {
      chrome.runtime.getURL.mockReturnValue('chrome-extension://test-id/test.html');
      
      const url = adapter.getURL('test.html');
      
      expect(chrome.runtime.getURL).toHaveBeenCalledWith('test.html');
      expect(url).toBe('chrome-extension://test-id/test.html');
    });

    test('getURL参数不是字符串时应该抛出错误', () => {
      expect(() => {
        adapter.getURL(123);
      }).toThrow('Path must be a string');
    });

    test('getId应该返回扩展ID', () => {
      chrome.runtime.id = 'test-extension-id';
      
      const id = adapter.getId();
      
      expect(id).toBe('test-extension-id');
    });

    test('getManifest应该返回清单信息', () => {
      const mockManifest = { name: 'Test Extension', version: '1.0.0' };
      chrome.runtime.getManifest.mockReturnValue(mockManifest);
      
      const manifest = adapter.getManifest();
      
      expect(chrome.runtime.getManifest).toHaveBeenCalled();
      expect(manifest).toEqual(mockManifest);
    });

    test('reload应该调用Chrome reload方法', () => {
      adapter.reload();
      
      expect(chrome.runtime.reload).toHaveBeenCalled();
    });
  });

  describe('事件监听方法', () => {
    test('onStartup应该添加启动监听器', () => {
      const callback = jest.fn();
      const removeListener = adapter.onStartup(callback);

      expect(chrome.runtime.onStartup.addListener).toHaveBeenCalledWith(callback);
      expect(typeof removeListener).toBe('function');
    });

    test('onInstalled应该添加安装监听器', () => {
      const callback = jest.fn();
      const removeListener = adapter.onInstalled(callback);

      expect(chrome.runtime.onInstalled.addListener).toHaveBeenCalledWith(callback);
      expect(typeof removeListener).toBe('function');
    });

    test('onSuspend应该添加挂起监听器', () => {
      const callback = jest.fn();
      const removeListener = adapter.onSuspend(callback);

      expect(chrome.runtime.onSuspend.addListener).toHaveBeenCalledWith(callback);
      expect(typeof removeListener).toBe('function');
    });

    test('onConnect应该添加连接监听器', () => {
      const callback = jest.fn();
      const removeListener = adapter.onConnect(callback);

      expect(chrome.runtime.onConnect.addListener).toHaveBeenCalledWith(callback);
      expect(typeof removeListener).toBe('function');
    });
  });

  describe('连接方法', () => {
    test('connect应该创建连接', () => {
      const mockPort = { name: 'test-port' };
      chrome.runtime.connect.mockReturnValue(mockPort);
      
      const port = adapter.connect({ name: 'test-port' });
      
      expect(chrome.runtime.connect).toHaveBeenCalledWith({ name: 'test-port' });
      expect(port).toEqual(mockPort);
    });

    test('connect应该支持连接到特定扩展', () => {
      const mockPort = { name: 'test-port' };
      chrome.runtime.connect.mockReturnValue(mockPort);
      
      const port = adapter.connect({
        extensionId: 'test-extension-id',
        name: 'test-port'
      });
      
      expect(chrome.runtime.connect).toHaveBeenCalledWith(
        'test-extension-id',
        { name: 'test-port' }
      );
      expect(port).toEqual(mockPort);
    });
  });

  describe('异步方法', () => {
    test('getBackgroundPage应该返回后台页面', async () => {
      const mockBackgroundPage = { document: {} };
      chrome.runtime.getBackgroundPage.mockImplementation((callback) => {
        callback(mockBackgroundPage);
      });

      const backgroundPage = await adapter.getBackgroundPage();
      
      expect(chrome.runtime.getBackgroundPage).toHaveBeenCalled();
      expect(backgroundPage).toEqual(mockBackgroundPage);
    });

    test('openOptionsPage应该打开选项页面', async () => {
      chrome.runtime.openOptionsPage.mockImplementation((callback) => {
        callback();
      });

      await adapter.openOptionsPage();
      
      expect(chrome.runtime.openOptionsPage).toHaveBeenCalled();
    });

    test('setUninstallURL应该设置卸载URL', async () => {
      chrome.runtime.setUninstallURL.mockImplementation((url, callback) => {
        callback();
      });

      await adapter.setUninstallURL('https://example.com/uninstall');
      
      expect(chrome.runtime.setUninstallURL).toHaveBeenCalledWith(
        'https://example.com/uninstall',
        expect.any(Function)
      );
    });

    test('getPlatformInfo应该返回平台信息', async () => {
      const mockPlatformInfo = { os: 'mac', arch: 'x86-64' };
      chrome.runtime.getPlatformInfo.mockImplementation((callback) => {
        callback(mockPlatformInfo);
      });

      const platformInfo = await adapter.getPlatformInfo();
      
      expect(chrome.runtime.getPlatformInfo).toHaveBeenCalled();
      expect(platformInfo).toEqual(mockPlatformInfo);
    });
  });

  describe('isAvailable方法', () => {
    test('当runtime可用时应该返回true', () => {
      expect(adapter.isAvailable()).toBe(true);
    });

    test('当runtime不可用时应该返回false', () => {
      const originalRuntime = chrome.runtime;
      chrome.runtime = undefined;
      
      expect(adapter.isAvailable()).toBe(false);
      
      chrome.runtime = originalRuntime;
    });
  });
});
