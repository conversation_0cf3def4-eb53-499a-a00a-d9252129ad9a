import { ChromeStorageAdapter } from '../ChromeStorageAdapter.js';

describe('ChromeStorageAdapter', () => {
  let adapter;

  beforeEach(() => {
    // 重置Chrome API模拟
    jest.clearAllMocks();
    chrome.runtime.lastError = null;
    
    adapter = new ChromeStorageAdapter('local');
  });

  describe('构造函数', () => {
    test('应该成功创建本地存储适配器', () => {
      expect(adapter).toBeInstanceOf(ChromeStorageAdapter);
      expect(adapter.getStorageArea()).toBe('local');
    });

    test('应该成功创建同步存储适配器', () => {
      const syncAdapter = new ChromeStorageAdapter('sync');
      expect(syncAdapter.getStorageArea()).toBe('sync');
    });

    test('当Chrome storage不可用时应该抛出错误', () => {
      const originalStorage = chrome.storage;
      chrome.storage = undefined;

      expect(() => {
        new ChromeStorageAdapter('local');
      }).toThrow('Chrome storage.local is not available');

      chrome.storage = originalStorage;
    });
  });

  describe('get方法', () => {
    test('应该成功获取单个键的数据', async () => {
      const mockData = { testKey: 'testValue' };
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        callback(mockData);
      });

      const result = await adapter.get('testKey');
      
      expect(chrome.storage.local.get).toHaveBeenCalledWith('testKey', expect.any(Function));
      expect(result).toEqual(mockData);
    });

    test('应该成功获取多个键的数据', async () => {
      const mockData = { key1: 'value1', key2: 'value2' };
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        callback(mockData);
      });

      const result = await adapter.get(['key1', 'key2']);
      
      expect(chrome.storage.local.get).toHaveBeenCalledWith(['key1', 'key2'], expect.any(Function));
      expect(result).toEqual(mockData);
    });

    test('当Chrome API返回错误时应该拒绝Promise', async () => {
      chrome.runtime.lastError = { message: 'Storage error' };
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        callback({});
      });

      await expect(adapter.get('testKey')).rejects.toThrow('Storage error');
    });

    test('当发生异常时应该拒绝Promise', async () => {
      chrome.storage.local.get.mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      await expect(adapter.get('testKey')).rejects.toThrow('Failed to get storage data: Unexpected error');
    });
  });

  describe('set方法', () => {
    test('应该成功设置数据', async () => {
      const testData = { key1: 'value1', key2: 'value2' };
      chrome.storage.local.set.mockImplementation((items, callback) => {
        callback();
      });

      await adapter.set(testData);
      
      expect(chrome.storage.local.set).toHaveBeenCalledWith(testData, expect.any(Function));
    });

    test('当传入无效数据时应该抛出错误', async () => {
      await expect(adapter.set(null)).rejects.toThrow('Items must be a non-null object');
      await expect(adapter.set('invalid')).rejects.toThrow('Items must be a non-null object');
    });

    test('当Chrome API返回错误时应该拒绝Promise', async () => {
      chrome.runtime.lastError = { message: 'Storage quota exceeded' };
      chrome.storage.local.set.mockImplementation((items, callback) => {
        callback();
      });

      await expect(adapter.set({ key: 'value' })).rejects.toThrow('Storage quota exceeded');
    });
  });

  describe('remove方法', () => {
    test('应该成功删除单个键', async () => {
      chrome.storage.local.remove.mockImplementation((keys, callback) => {
        callback();
      });

      await adapter.remove('testKey');
      
      expect(chrome.storage.local.remove).toHaveBeenCalledWith('testKey', expect.any(Function));
    });

    test('应该成功删除多个键', async () => {
      chrome.storage.local.remove.mockImplementation((keys, callback) => {
        callback();
      });

      await adapter.remove(['key1', 'key2']);
      
      expect(chrome.storage.local.remove).toHaveBeenCalledWith(['key1', 'key2'], expect.any(Function));
    });

    test('当keys参数为空时应该抛出错误', async () => {
      await expect(adapter.remove()).rejects.toThrow('Keys parameter is required');
      await expect(adapter.remove(null)).rejects.toThrow('Keys parameter is required');
    });
  });

  describe('clear方法', () => {
    test('应该成功清空所有数据', async () => {
      chrome.storage.local.clear.mockImplementation((callback) => {
        callback();
      });

      await adapter.clear();
      
      expect(chrome.storage.local.clear).toHaveBeenCalledWith(expect.any(Function));
    });
  });

  describe('getUsage方法', () => {
    test('应该成功获取本地存储使用情况', async () => {
      chrome.storage.local.getBytesInUse.mockImplementation((keys, callback) => {
        callback(1024); // 1KB
      });

      const usage = await adapter.getUsage();
      
      expect(usage).toEqual({
        bytesInUse: 1024,
        quota: 10 * 1024 * 1024, // 10MB
        percentUsed: (1024 / (10 * 1024 * 1024)) * 100
      });
    });

    test('同步存储应该返回默认使用情况', async () => {
      const syncAdapter = new ChromeStorageAdapter('sync');
      const usage = await syncAdapter.getUsage();
      
      expect(usage).toEqual({
        bytesInUse: 0,
        quota: 0
      });
    });
  });

  describe('onChanged方法', () => {
    test('应该成功添加变化监听器', () => {
      const callback = jest.fn();
      const removeListener = adapter.onChanged(callback);

      expect(chrome.storage.onChanged.addListener).toHaveBeenCalled();
      expect(typeof removeListener).toBe('function');
    });

    test('应该只监听指定存储区域的变化', () => {
      const callback = jest.fn();
      adapter.onChanged(callback);

      // 获取添加的监听器
      const addedListener = chrome.storage.onChanged.addListener.mock.calls[0][0];
      
      // 模拟本地存储变化
      addedListener({ key: { newValue: 'value' } }, 'local');
      expect(callback).toHaveBeenCalledWith({ key: { newValue: 'value' } }, 'local');

      // 模拟同步存储变化（不应该触发回调）
      callback.mockClear();
      addedListener({ key: { newValue: 'value' } }, 'sync');
      expect(callback).not.toHaveBeenCalled();
    });

    test('当callback不是函数时应该抛出错误', () => {
      expect(() => {
        adapter.onChanged('not a function');
      }).toThrow('Callback must be a function');
    });
  });

  describe('isAvailable方法', () => {
    test('当存储可用时应该返回true', async () => {
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        callback({});
      });

      const available = await adapter.isAvailable();
      expect(available).toBe(true);
    });

    test('当存储不可用时应该返回false', async () => {
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        chrome.runtime.lastError = { message: 'Storage not available' };
        callback({});
      });

      const available = await adapter.isAvailable();
      expect(available).toBe(false);
    });
  });

  describe('批量操作方法', () => {
    test('getBatch应该返回正确的批量结果', async () => {
      const mockData = { key1: 'value1', key3: 'value3' };
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        callback(mockData);
      });

      const result = await adapter.getBatch(['key1', 'key2', 'key3']);
      
      expect(result).toEqual([
        { key: 'key1', value: 'value1', exists: true },
        { key: 'key2', value: null, exists: false },
        { key: 'key3', value: 'value3', exists: true }
      ]);
    });

    test('setBatch应该正确设置批量数据', async () => {
      chrome.storage.local.set.mockImplementation((items, callback) => {
        callback();
      });

      const items = [
        { key: 'key1', value: 'value1' },
        { key: 'key2', value: 'value2' }
      ];

      await adapter.setBatch(items);
      
      expect(chrome.storage.local.set).toHaveBeenCalledWith(
        { key1: 'value1', key2: 'value2' },
        expect.any(Function)
      );
    });
  });

  describe('辅助方法', () => {
    test('has方法应该正确检查键是否存在', async () => {
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        callback({ existingKey: 'value' });
      });

      const exists = await adapter.has('existingKey');
      const notExists = await adapter.has('nonExistingKey');
      
      expect(exists).toBe(true);
      expect(notExists).toBe(false);
    });

    test('keys方法应该返回所有键', async () => {
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        callback({ key1: 'value1', key2: 'value2', key3: 'value3' });
      });

      const keys = await adapter.keys();
      
      expect(keys).toEqual(['key1', 'key2', 'key3']);
    });
  });
});
