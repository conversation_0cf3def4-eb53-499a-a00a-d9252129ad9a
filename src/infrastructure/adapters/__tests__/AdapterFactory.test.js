import { AdapterFactory, adapterFactory } from '../AdapterFactory.js';
import { ChromeStorageAdapter } from '../ChromeStorageAdapter.js';
import { ChromeRuntimeAdapter } from '../ChromeRuntimeAdapter.js';
import { ChromeTabsAdapter } from '../ChromeTabsAdapter.js';

describe('AdapterFactory', () => {
  let factory;

  beforeEach(() => {
    jest.clearAllMocks();
    chrome.runtime.lastError = null;
    factory = new AdapterFactory();
  });

  afterEach(() => {
    factory.cleanup();
  });

  describe('构造函数', () => {
    test('应该成功创建工厂实例', () => {
      expect(factory).toBeInstanceOf(AdapterFactory);
      expect(factory.getAdapterInfo().totalAdapters).toBe(0);
    });
  });

  describe('createStorageAdapter方法', () => {
    test('应该成功创建本地存储适配器', () => {
      const adapter = factory.createStorageAdapter('local');
      
      expect(adapter).toBeInstanceOf(ChromeStorageAdapter);
      expect(adapter.getStorageArea()).toBe('local');
    });

    test('应该成功创建同步存储适配器', () => {
      const adapter = factory.createStorageAdapter('sync');
      
      expect(adapter).toBeInstanceOf(ChromeStorageAdapter);
      expect(adapter.getStorageArea()).toBe('sync');
    });

    test('应该使用单例模式，相同类型的适配器只创建一次', () => {
      const adapter1 = factory.createStorageAdapter('local');
      const adapter2 = factory.createStorageAdapter('local');
      
      expect(adapter1).toBe(adapter2);
    });

    test('应该为不同存储区域创建不同的适配器实例', () => {
      const localAdapter = factory.createStorageAdapter('local');
      const syncAdapter = factory.createStorageAdapter('sync');
      
      expect(localAdapter).not.toBe(syncAdapter);
      expect(localAdapter.getStorageArea()).toBe('local');
      expect(syncAdapter.getStorageArea()).toBe('sync');
    });

    test('当Chrome storage不可用时应该抛出错误', () => {
      const originalStorage = chrome.storage;
      chrome.storage = undefined;

      expect(() => {
        factory.createStorageAdapter('local');
      }).toThrow('Failed to create storage adapter for local');

      chrome.storage = originalStorage;
    });
  });

  describe('createRuntimeAdapter方法', () => {
    test('应该成功创建运行时适配器', () => {
      const adapter = factory.createRuntimeAdapter();
      
      expect(adapter).toBeInstanceOf(ChromeRuntimeAdapter);
    });

    test('应该使用单例模式', () => {
      const adapter1 = factory.createRuntimeAdapter();
      const adapter2 = factory.createRuntimeAdapter();
      
      expect(adapter1).toBe(adapter2);
    });

    test('当Chrome runtime不可用时应该抛出错误', () => {
      const originalRuntime = chrome.runtime;
      chrome.runtime = undefined;

      expect(() => {
        factory.createRuntimeAdapter();
      }).toThrow('Failed to create runtime adapter');

      chrome.runtime = originalRuntime;
    });
  });

  describe('createTabsAdapter方法', () => {
    test('应该成功创建标签页适配器', () => {
      const adapter = factory.createTabsAdapter();
      
      expect(adapter).toBeInstanceOf(ChromeTabsAdapter);
    });

    test('应该使用单例模式', () => {
      const adapter1 = factory.createTabsAdapter();
      const adapter2 = factory.createTabsAdapter();
      
      expect(adapter1).toBe(adapter2);
    });

    test('当Chrome tabs不可用时应该抛出错误', () => {
      const originalTabs = chrome.tabs;
      chrome.tabs = undefined;

      expect(() => {
        factory.createTabsAdapter();
      }).toThrow('Failed to create tabs adapter');

      chrome.tabs = originalTabs;
    });
  });

  describe('便捷获取方法', () => {
    test('getLocalStorageAdapter应该返回本地存储适配器', () => {
      const adapter = factory.getLocalStorageAdapter();
      
      expect(adapter).toBeInstanceOf(ChromeStorageAdapter);
      expect(adapter.getStorageArea()).toBe('local');
    });

    test('getSyncStorageAdapter应该返回同步存储适配器', () => {
      const adapter = factory.getSyncStorageAdapter();
      
      expect(adapter).toBeInstanceOf(ChromeStorageAdapter);
      expect(adapter.getStorageArea()).toBe('sync');
    });

    test('getRuntimeAdapter应该返回运行时适配器', () => {
      const adapter = factory.getRuntimeAdapter();
      
      expect(adapter).toBeInstanceOf(ChromeRuntimeAdapter);
    });

    test('getTabsAdapter应该返回标签页适配器', () => {
      const adapter = factory.getTabsAdapter();
      
      expect(adapter).toBeInstanceOf(ChromeTabsAdapter);
    });
  });

  describe('checkAvailability方法', () => {
    test('应该正确检查Chrome API可用性', () => {
      const availability = factory.checkAvailability();
      
      expect(availability).toEqual({
        chrome: true,
        storage: true,
        storageLocal: true,
        storageSync: true,
        runtime: true,
        tabs: true,
        overall: true
      });
    });

    test('当Chrome不可用时应该返回false', () => {
      const originalChrome = global.chrome;
      global.chrome = undefined;

      const availability = factory.checkAvailability();
      
      expect(availability.chrome).toBe(false);
      expect(availability.overall).toBe(false);

      global.chrome = originalChrome;
    });

    test('当storage不可用时应该返回false', () => {
      const originalStorage = chrome.storage;
      chrome.storage = undefined;

      const availability = factory.checkAvailability();
      
      expect(availability.storage).toBe(false);
      expect(availability.overall).toBe(false);

      chrome.storage = originalStorage;
    });
  });

  describe('validateAdapters方法', () => {
    test('应该验证所有适配器的可用性', async () => {
      // 模拟所有适配器都可用
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        callback({});
      });

      const results = await factory.validateAdapters();
      
      expect(results.localStorage.available).toBe(true);
      expect(results.syncStorage.available).toBe(true);
      expect(results.runtime.available).toBe(true);
      expect(results.tabs.available).toBe(true);
    });

    test('应该正确处理适配器创建失败', async () => {
      const originalStorage = chrome.storage;
      chrome.storage = undefined;

      const results = await factory.validateAdapters();
      
      expect(results.localStorage.available).toBe(false);
      expect(results.localStorage.error).toContain('Chrome storage.local is not available');

      chrome.storage = originalStorage;
    });
  });

  describe('管理方法', () => {
    test('cleanup应该清理所有适配器', () => {
      // 创建一些适配器
      factory.createStorageAdapter('local');
      factory.createRuntimeAdapter();
      factory.createTabsAdapter();
      
      expect(factory.getAdapterInfo().totalAdapters).toBe(3);
      
      factory.cleanup();
      
      expect(factory.getAdapterInfo().totalAdapters).toBe(0);
    });

    test('reset应该重置工厂状态', () => {
      factory.createStorageAdapter('local');
      factory.createRuntimeAdapter();
      
      factory.reset();
      
      expect(factory.getAdapterInfo().totalAdapters).toBe(0);
    });

    test('getAdapterInfo应该返回正确的适配器信息', () => {
      factory.createStorageAdapter('local');
      factory.createStorageAdapter('sync');
      factory.createRuntimeAdapter();
      
      const info = factory.getAdapterInfo();
      
      expect(info).toEqual({
        storageAdapters: ['local', 'sync'],
        hasRuntimeAdapter: true,
        hasTabsAdapter: false,
        totalAdapters: 3
      });
    });
  });

  describe('单例实例', () => {
    test('adapterFactory应该是AdapterFactory的实例', () => {
      expect(adapterFactory).toBeInstanceOf(AdapterFactory);
    });

    test('多次导入应该返回同一个实例', () => {
      const { adapterFactory: factory1 } = require('../AdapterFactory.js');
      const { adapterFactory: factory2 } = require('../AdapterFactory.js');
      
      expect(factory1).toBe(factory2);
    });
  });

  describe('便捷导出函数', () => {
    test('便捷函数应该正确工作', () => {
      const {
        createStorageAdapter,
        createRuntimeAdapter,
        createTabsAdapter,
        getLocalStorage,
        getSyncStorage,
        getRuntime,
        getTabs
      } = require('../AdapterFactory.js');

      expect(createStorageAdapter('local')).toBeInstanceOf(ChromeStorageAdapter);
      expect(createRuntimeAdapter()).toBeInstanceOf(ChromeRuntimeAdapter);
      expect(createTabsAdapter()).toBeInstanceOf(ChromeTabsAdapter);
      expect(getLocalStorage()).toBeInstanceOf(ChromeStorageAdapter);
      expect(getSyncStorage()).toBeInstanceOf(ChromeStorageAdapter);
      expect(getRuntime()).toBeInstanceOf(ChromeRuntimeAdapter);
      expect(getTabs()).toBeInstanceOf(ChromeTabsAdapter);
    });
  });
});
