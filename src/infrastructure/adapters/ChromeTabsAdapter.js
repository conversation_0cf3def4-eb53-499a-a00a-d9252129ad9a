/**
 * Chrome标签页适配器
 * 封装Chrome扩展的tabs API，提供Promise接口
 */
export class ChromeTabsAdapter {
  constructor() {
    if (!chrome?.tabs) {
      throw new Error('Chrome tabs API is not available');
    }
  }

  /**
   * 查询标签页
   * @param {Object} queryInfo - 查询条件
   * @returns {Promise<Array>} 标签页数组
   */
  async query(queryInfo = {}) {
    return new Promise((resolve, reject) => {
      try {
        chrome.tabs.query(queryInfo, (tabs) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(tabs || []);
          }
        });
      } catch (error) {
        reject(new Error(`Failed to query tabs: ${error.message}`));
      }
    });
  }

  /**
   * 获取当前活动标签页
   * @returns {Promise<Object>} 当前标签页
   */
  async getCurrentTab() {
    const tabs = await this.query({ active: true, currentWindow: true });
    return tabs[0] || null;
  }

  /**
   * 根据ID获取标签页
   * @param {number} tabId - 标签页ID
   * @returns {Promise<Object>} 标签页对象
   */
  async get(tabId) {
    return new Promise((resolve, reject) => {
      try {
        chrome.tabs.get(tabId, (tab) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(tab);
          }
        });
      } catch (error) {
        reject(new Error(`Failed to get tab: ${error.message}`));
      }
    });
  }

  /**
   * 创建新标签页
   * @param {Object} createProperties - 创建属性
   * @returns {Promise<Object>} 新创建的标签页
   */
  async create(createProperties) {
    return new Promise((resolve, reject) => {
      try {
        chrome.tabs.create(createProperties, (tab) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(tab);
          }
        });
      } catch (error) {
        reject(new Error(`Failed to create tab: ${error.message}`));
      }
    });
  }

  /**
   * 更新标签页
   * @param {number} tabId - 标签页ID
   * @param {Object} updateProperties - 更新属性
   * @returns {Promise<Object>} 更新后的标签页
   */
  async update(tabId, updateProperties) {
    return new Promise((resolve, reject) => {
      try {
        chrome.tabs.update(tabId, updateProperties, (tab) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(tab);
          }
        });
      } catch (error) {
        reject(new Error(`Failed to update tab: ${error.message}`));
      }
    });
  }

  /**
   * 关闭标签页
   * @param {number|number[]} tabIds - 标签页ID或ID数组
   * @returns {Promise<void>}
   */
  async remove(tabIds) {
    return new Promise((resolve, reject) => {
      try {
        chrome.tabs.remove(tabIds, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } catch (error) {
        reject(new Error(`Failed to remove tabs: ${error.message}`));
      }
    });
  }

  /**
   * 向标签页发送消息
   * @param {number} tabId - 标签页ID
   * @param {any} message - 消息内容
   * @param {Object} options - 发送选项
   * @returns {Promise<any>} 响应数据
   */
  async sendMessage(tabId, message, options = {}) {
    return new Promise((resolve, reject) => {
      try {
        const { frameId, timeout = 5000 } = options;
        
        const timeoutId = setTimeout(() => {
          reject(new Error('Message timeout'));
        }, timeout);

        const responseCallback = (response) => {
          clearTimeout(timeoutId);
          
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response);
          }
        };

        if (frameId !== undefined) {
          chrome.tabs.sendMessage(tabId, message, { frameId }, responseCallback);
        } else {
          chrome.tabs.sendMessage(tabId, message, responseCallback);
        }
      } catch (error) {
        reject(new Error(`Failed to send message to tab: ${error.message}`));
      }
    });
  }

  /**
   * 执行脚本
   * @param {number} tabId - 标签页ID
   * @param {Object} injectDetails - 注入详情
   * @returns {Promise<Array>} 执行结果
   */
  async executeScript(tabId, injectDetails) {
    return new Promise((resolve, reject) => {
      try {
        chrome.tabs.executeScript(tabId, injectDetails, (results) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(results || []);
          }
        });
      } catch (error) {
        reject(new Error(`Failed to execute script: ${error.message}`));
      }
    });
  }

  /**
   * 插入CSS
   * @param {number} tabId - 标签页ID
   * @param {Object} injectDetails - 注入详情
   * @returns {Promise<void>}
   */
  async insertCSS(tabId, injectDetails) {
    return new Promise((resolve, reject) => {
      try {
        chrome.tabs.insertCSS(tabId, injectDetails, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } catch (error) {
        reject(new Error(`Failed to insert CSS: ${error.message}`));
      }
    });
  }

  /**
   * 移除CSS
   * @param {number} tabId - 标签页ID
   * @param {Object} removeDetails - 移除详情
   * @returns {Promise<void>}
   */
  async removeCSS(tabId, removeDetails) {
    return new Promise((resolve, reject) => {
      try {
        chrome.tabs.removeCSS(tabId, removeDetails, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } catch (error) {
        reject(new Error(`Failed to remove CSS: ${error.message}`));
      }
    });
  }

  /**
   * 监听标签页创建
   * @param {Function} callback - 回调函数
   * @returns {Function} 取消监听的函数
   */
  onCreated(callback) {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    chrome.tabs.onCreated.addListener(callback);

    return () => {
      chrome.tabs.onCreated.removeListener(callback);
    };
  }

  /**
   * 监听标签页更新
   * @param {Function} callback - 回调函数
   * @returns {Function} 取消监听的函数
   */
  onUpdated(callback) {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    chrome.tabs.onUpdated.addListener(callback);

    return () => {
      chrome.tabs.onUpdated.removeListener(callback);
    };
  }

  /**
   * 监听标签页移除
   * @param {Function} callback - 回调函数
   * @returns {Function} 取消监听的函数
   */
  onRemoved(callback) {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    chrome.tabs.onRemoved.addListener(callback);

    return () => {
      chrome.tabs.onRemoved.removeListener(callback);
    };
  }

  /**
   * 监听标签页激活
   * @param {Function} callback - 回调函数
   * @returns {Function} 取消监听的函数
   */
  onActivated(callback) {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    chrome.tabs.onActivated.addListener(callback);

    return () => {
      chrome.tabs.onActivated.removeListener(callback);
    };
  }

  /**
   * 重新加载标签页
   * @param {number} tabId - 标签页ID
   * @param {Object} reloadProperties - 重新加载属性
   * @returns {Promise<void>}
   */
  async reload(tabId, reloadProperties = {}) {
    return new Promise((resolve, reject) => {
      try {
        chrome.tabs.reload(tabId, reloadProperties, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } catch (error) {
        reject(new Error(`Failed to reload tab: ${error.message}`));
      }
    });
  }

  /**
   * 复制标签页
   * @param {number} tabId - 标签页ID
   * @returns {Promise<Object>} 复制的标签页
   */
  async duplicate(tabId) {
    return new Promise((resolve, reject) => {
      try {
        chrome.tabs.duplicate(tabId, (tab) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(tab);
          }
        });
      } catch (error) {
        reject(new Error(`Failed to duplicate tab: ${error.message}`));
      }
    });
  }

  /**
   * 检查tabs API是否可用
   * @returns {boolean} 是否可用
   */
  isAvailable() {
    return !!(chrome && chrome.tabs);
  }
}
