/**
 * Chrome API适配器层统一导出
 * 提供所有适配器的便捷访问入口
 */

// 适配器接口
export { IStorageAdapter } from './IStorageAdapter.js';

// 具体适配器实现
export { ChromeStorageAdapter } from './ChromeStorageAdapter.js';
export { ChromeRuntimeAdapter } from './ChromeRuntimeAdapter.js';
export { ChromeTabsAdapter } from './ChromeTabsAdapter.js';

// 适配器工厂
export { 
  AdapterFactory,
  adapterFactory,
  createStorageAdapter,
  createRuntimeAdapter,
  createTabsAdapter,
  getLocalStorage,
  getSyncStorage,
  getRuntime,
  getTabs
} from './AdapterFactory.js';

// 便捷的默认导出
export default {
  // 工厂实例
  factory: adapterFactory,
  
  // 快速访问适配器
  localStorage: () => getLocalStorage(),
  syncStorage: () => getSyncStorage(),
  runtime: () => getRuntime(),
  tabs: () => getTabs(),
  
  // 适配器类
  StorageAdapter: ChromeStorageAdapter,
  RuntimeAdapter: ChromeRuntimeAdapter,
  TabsAdapter: ChromeTabsAdapter,
  
  // 工厂类
  Factory: AdapterFactory
};
