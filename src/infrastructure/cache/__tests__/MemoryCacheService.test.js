import { MemoryCacheService } from '../MemoryCacheService.js';

describe('MemoryCacheService', () => {
  let cacheService;

  beforeEach(() => {
    cacheService = new MemoryCacheService();
  });

  afterEach(() => {
    // 清理定时器
    cacheService.cache.clear();
    for (const timer of cacheService.timers.values()) {
      clearTimeout(timer);
    }
    cacheService.timers.clear();
  });

  describe('构造函数', () => {
    test('应该成功创建缓存服务', () => {
      expect(cacheService).toBeInstanceOf(MemoryCacheService);
      expect(cacheService.getType()).toBe('memory');
      expect(cacheService.cache).toBeInstanceOf(Map);
      expect(cacheService.timers).toBeInstanceOf(Map);
    });
  });

  describe('基本操作', () => {
    test('set和get应该正常工作', async () => {
      await cacheService.set('testKey', 'testValue');
      const result = await cacheService.get('testKey');
      
      expect(result).toBe('testValue');
    });

    test('get不存在的键应该返回null', async () => {
      const result = await cacheService.get('nonExistentKey');
      expect(result).toBeNull();
    });

    test('delete应该删除缓存项', async () => {
      await cacheService.set('testKey', 'testValue');
      const deleted = await cacheService.delete('testKey');
      const result = await cacheService.get('testKey');
      
      expect(deleted).toBe(true);
      expect(result).toBeNull();
    });

    test('delete不存在的键应该返回false', async () => {
      const deleted = await cacheService.delete('nonExistentKey');
      expect(deleted).toBe(false);
    });

    test('has应该正确检查键是否存在', async () => {
      await cacheService.set('testKey', 'testValue');
      
      const exists = await cacheService.has('testKey');
      const notExists = await cacheService.has('nonExistentKey');
      
      expect(exists).toBe(true);
      expect(notExists).toBe(false);
    });
  });

  describe('TTL功能', () => {
    test('应该支持TTL过期', async () => {
      await cacheService.set('testKey', 'testValue', { ttl: 100 });
      
      // 立即获取应该成功
      let result = await cacheService.get('testKey');
      expect(result).toBe('testValue');
      
      // 等待过期
      await new Promise(resolve => setTimeout(resolve, 150));
      
      result = await cacheService.get('testKey');
      expect(result).toBeNull();
    });

    test('has应该正确处理过期项', async () => {
      await cacheService.set('testKey', 'testValue', { ttl: 100 });
      
      let exists = await cacheService.has('testKey');
      expect(exists).toBe(true);
      
      // 等待过期
      await new Promise(resolve => setTimeout(resolve, 150));
      
      exists = await cacheService.has('testKey');
      expect(exists).toBe(false);
    });

    test('expire应该设置过期时间', async () => {
      await cacheService.set('testKey', 'testValue');
      
      const success = await cacheService.expire('testKey', 100);
      expect(success).toBe(true);
      
      // 等待过期
      await new Promise(resolve => setTimeout(resolve, 150));
      
      const result = await cacheService.get('testKey');
      expect(result).toBeNull();
    });

    test('ttl应该返回正确的剩余时间', async () => {
      await cacheService.set('testKey', 'testValue', { ttl: 1000 });
      
      const remaining = await cacheService.ttl('testKey');
      expect(remaining).toBeGreaterThan(0);
      expect(remaining).toBeLessThanOrEqual(1000);
    });

    test('ttl对于永不过期的项应该返回-1', async () => {
      await cacheService.set('testKey', 'testValue');
      
      const remaining = await cacheService.ttl('testKey');
      expect(remaining).toBe(-1);
    });

    test('ttl对于不存在的项应该返回-2', async () => {
      const remaining = await cacheService.ttl('nonExistentKey');
      expect(remaining).toBe(-2);
    });
  });

  describe('命名空间功能', () => {
    test('应该支持命名空间', async () => {
      await cacheService.set('key', 'value1', { namespace: 'ns1' });
      await cacheService.set('key', 'value2', { namespace: 'ns2' });
      
      const result1 = await cacheService.get('key', 'ns1');
      const result2 = await cacheService.get('key', 'ns2');
      
      expect(result1).toBe('value1');
      expect(result2).toBe('value2');
    });

    test('clear应该支持按命名空间清理', async () => {
      await cacheService.set('key1', 'value1', { namespace: 'ns1' });
      await cacheService.set('key2', 'value2', { namespace: 'ns1' });
      await cacheService.set('key3', 'value3', { namespace: 'ns2' });
      
      await cacheService.clear('ns1');
      
      const result1 = await cacheService.get('key1', 'ns1');
      const result2 = await cacheService.get('key2', 'ns1');
      const result3 = await cacheService.get('key3', 'ns2');
      
      expect(result1).toBeNull();
      expect(result2).toBeNull();
      expect(result3).toBe('value3');
    });

    test('clear不带参数应该清空所有缓存', async () => {
      await cacheService.set('key1', 'value1', { namespace: 'ns1' });
      await cacheService.set('key2', 'value2', { namespace: 'ns2' });
      await cacheService.set('key3', 'value3');
      
      await cacheService.clear();
      
      const size = await cacheService.size();
      expect(size).toBe(0);
    });
  });

  describe('批量操作', () => {
    test('getMultiple应该返回多个值', async () => {
      await cacheService.set('key1', 'value1');
      await cacheService.set('key2', 'value2');
      
      const result = await cacheService.getMultiple(['key1', 'key2', 'key3']);
      
      expect(result).toEqual({
        key1: 'value1',
        key2: 'value2'
      });
    });

    test('setMultiple应该设置多个值', async () => {
      await cacheService.setMultiple({
        key1: 'value1',
        key2: 'value2'
      });
      
      const result1 = await cacheService.get('key1');
      const result2 = await cacheService.get('key2');
      
      expect(result1).toBe('value1');
      expect(result2).toBe('value2');
    });

    test('deleteMultiple应该删除多个值', async () => {
      await cacheService.set('key1', 'value1');
      await cacheService.set('key2', 'value2');
      await cacheService.set('key3', 'value3');
      
      const count = await cacheService.deleteMultiple(['key1', 'key2']);
      
      expect(count).toBe(2);
      
      const result1 = await cacheService.get('key1');
      const result2 = await cacheService.get('key2');
      const result3 = await cacheService.get('key3');
      
      expect(result1).toBeNull();
      expect(result2).toBeNull();
      expect(result3).toBe('value3');
    });
  });

  describe('数值操作', () => {
    test('increment应该增加数值', async () => {
      await cacheService.set('counter', 5);
      
      const result1 = await cacheService.increment('counter');
      const result2 = await cacheService.increment('counter', 3);
      
      expect(result1).toBe(6);
      expect(result2).toBe(9);
    });

    test('increment对于不存在的键应该从0开始', async () => {
      const result = await cacheService.increment('newCounter', 5);
      expect(result).toBe(5);
    });

    test('decrement应该减少数值', async () => {
      await cacheService.set('counter', 10);
      
      const result1 = await cacheService.decrement('counter');
      const result2 = await cacheService.decrement('counter', 3);
      
      expect(result1).toBe(9);
      expect(result2).toBe(6);
    });
  });

  describe('统计和信息', () => {
    test('getStats应该返回统计信息', async () => {
      await cacheService.set('key1', 'value1');
      await cacheService.get('key1'); // hit
      await cacheService.get('key2'); // miss
      
      const stats = await cacheService.getStats();
      
      expect(stats.hits).toBe(1);
      expect(stats.misses).toBe(1);
      expect(stats.sets).toBe(1);
      expect(stats.size).toBe(1);
      expect(stats.hitRate).toBe(0.5);
    });

    test('keys应该返回所有键', async () => {
      await cacheService.set('key1', 'value1');
      await cacheService.set('key2', 'value2', { namespace: 'ns' });
      
      const keys = await cacheService.keys();
      
      expect(keys).toContain('key1');
      expect(keys).toContain('ns:key2');
    });

    test('keys应该支持模式匹配', async () => {
      await cacheService.set('user:1', 'value1');
      await cacheService.set('user:2', 'value2');
      await cacheService.set('post:1', 'value3');
      
      const userKeys = await cacheService.keys('^user:');
      
      expect(userKeys).toHaveLength(2);
      expect(userKeys).toContain('user:1');
      expect(userKeys).toContain('user:2');
    });

    test('size应该返回缓存大小', async () => {
      await cacheService.set('key1', 'value1');
      await cacheService.set('key2', 'value2');
      
      const size = await cacheService.size();
      expect(size).toBe(2);
    });
  });

  describe('可用性检查', () => {
    test('isAvailable应该返回true', async () => {
      const available = await cacheService.isAvailable();
      expect(available).toBe(true);
    });

    test('flush应该成功执行（无操作）', async () => {
      await expect(cacheService.flush()).resolves.toBeUndefined();
    });
  });
});
