/**
 * 缓存服务接口
 * 定义了统一的缓存操作接口
 */
export class ICacheService {
  /**
   * 获取缓存数据
   * @param {string} key - 缓存键
   * @returns {Promise<any>} 缓存值，不存在时返回null
   */
  async get(key) {
    throw new Error('Method get() must be implemented by subclass');
  }

  /**
   * 设置缓存数据
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {Object} options - 缓存选项
   * @param {number} options.ttl - 生存时间（毫秒）
   * @param {string} options.namespace - 命名空间
   * @returns {Promise<void>}
   */
  async set(key, value, options = {}) {
    throw new Error('Method set() must be implemented by subclass');
  }

  /**
   * 删除缓存数据
   * @param {string} key - 缓存键
   * @returns {Promise<boolean>} 是否删除成功
   */
  async delete(key) {
    throw new Error('Method delete() must be implemented by subclass');
  }

  /**
   * 检查缓存是否存在
   * @param {string} key - 缓存键
   * @returns {Promise<boolean>} 是否存在
   */
  async has(key) {
    throw new Error('Method has() must be implemented by subclass');
  }

  /**
   * 清空所有缓存
   * @param {string} namespace - 命名空间，可选
   * @returns {Promise<void>}
   */
  async clear(namespace) {
    throw new Error('Method clear() must be implemented by subclass');
  }

  /**
   * 获取多个缓存数据
   * @param {string[]} keys - 缓存键数组
   * @returns {Promise<Object>} 键值对对象
   */
  async getMultiple(keys) {
    throw new Error('Method getMultiple() must be implemented by subclass');
  }

  /**
   * 设置多个缓存数据
   * @param {Object} items - 键值对对象
   * @param {Object} options - 缓存选项
   * @returns {Promise<void>}
   */
  async setMultiple(items, options = {}) {
    throw new Error('Method setMultiple() must be implemented by subclass');
  }

  /**
   * 删除多个缓存数据
   * @param {string[]} keys - 缓存键数组
   * @returns {Promise<number>} 删除的数量
   */
  async deleteMultiple(keys) {
    throw new Error('Method deleteMultiple() must be implemented by subclass');
  }

  /**
   * 获取缓存统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getStats() {
    throw new Error('Method getStats() must be implemented by subclass');
  }

  /**
   * 获取所有缓存键
   * @param {string} pattern - 键模式，可选
   * @returns {Promise<string[]>} 键数组
   */
  async keys(pattern) {
    throw new Error('Method keys() must be implemented by subclass');
  }

  /**
   * 设置缓存过期时间
   * @param {string} key - 缓存键
   * @param {number} ttl - 生存时间（毫秒）
   * @returns {Promise<boolean>} 是否设置成功
   */
  async expire(key, ttl) {
    throw new Error('Method expire() must be implemented by subclass');
  }

  /**
   * 获取缓存剩余生存时间
   * @param {string} key - 缓存键
   * @returns {Promise<number>} 剩余时间（毫秒），-1表示永不过期，-2表示不存在
   */
  async ttl(key) {
    throw new Error('Method ttl() must be implemented by subclass');
  }

  /**
   * 增加数值缓存
   * @param {string} key - 缓存键
   * @param {number} delta - 增加的值，默认为1
   * @returns {Promise<number>} 增加后的值
   */
  async increment(key, delta = 1) {
    throw new Error('Method increment() must be implemented by subclass');
  }

  /**
   * 减少数值缓存
   * @param {string} key - 缓存键
   * @param {number} delta - 减少的值，默认为1
   * @returns {Promise<number>} 减少后的值
   */
  async decrement(key, delta = 1) {
    throw new Error('Method decrement() must be implemented by subclass');
  }

  /**
   * 获取缓存大小
   * @returns {Promise<number>} 缓存项数量
   */
  async size() {
    throw new Error('Method size() must be implemented by subclass');
  }

  /**
   * 检查缓存服务是否可用
   * @returns {Promise<boolean>} 是否可用
   */
  async isAvailable() {
    throw new Error('Method isAvailable() must be implemented by subclass');
  }

  /**
   * 获取缓存类型
   * @returns {string} 缓存类型
   */
  getType() {
    throw new Error('Method getType() must be implemented by subclass');
  }

  /**
   * 刷新缓存（持久化到存储）
   * @returns {Promise<void>}
   */
  async flush() {
    throw new Error('Method flush() must be implemented by subclass');
  }
}
