import { ICacheService } from './ICacheService.js';

/**
 * 内存缓存服务实现
 * 基于Map的内存缓存，支持TTL和命名空间
 */
export class MemoryCacheService extends ICacheService {
  constructor() {
    super();
    this.cache = new Map();
    this.timers = new Map();
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      clears: 0
    };
  }

  /**
   * 创建缓存键
   * @param {string} key - 原始键
   * @param {string} namespace - 命名空间
   * @returns {string} 完整的缓存键
   */
  _createKey(key, namespace) {
    return namespace ? `${namespace}:${key}` : key;
  }

  /**
   * 设置过期定时器
   * @param {string} fullKey - 完整的缓存键
   * @param {number} ttl - 生存时间（毫秒）
   */
  _setExpiration(fullKey, ttl) {
    // 清除现有定时器
    if (this.timers.has(fullKey)) {
      clearTimeout(this.timers.get(fullKey));
    }

    // 设置新定时器
    if (ttl > 0) {
      const timer = setTimeout(() => {
        this.cache.delete(fullKey);
        this.timers.delete(fullKey);
      }, ttl);
      this.timers.set(fullKey, timer);
    }
  }

  /**
   * 获取缓存数据
   * @param {string} key - 缓存键
   * @param {string} namespace - 命名空间
   * @returns {Promise<any>} 缓存值，不存在时返回null
   */
  async get(key, namespace) {
    const fullKey = this._createKey(key, namespace);
    
    if (this.cache.has(fullKey)) {
      const item = this.cache.get(fullKey);
      
      // 检查是否过期
      if (item.expiresAt && Date.now() > item.expiresAt) {
        this.cache.delete(fullKey);
        this.timers.delete(fullKey);
        this.stats.misses++;
        return null;
      }
      
      this.stats.hits++;
      return item.value;
    }
    
    this.stats.misses++;
    return null;
  }

  /**
   * 设置缓存数据
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {Object} options - 缓存选项
   * @param {number} options.ttl - 生存时间（毫秒）
   * @param {string} options.namespace - 命名空间
   * @returns {Promise<void>}
   */
  async set(key, value, options = {}) {
    const { ttl, namespace } = options;
    const fullKey = this._createKey(key, namespace);
    
    const item = {
      value,
      createdAt: Date.now(),
      expiresAt: ttl ? Date.now() + ttl : null
    };
    
    this.cache.set(fullKey, item);
    this.stats.sets++;
    
    if (ttl) {
      this._setExpiration(fullKey, ttl);
    }
  }

  /**
   * 删除缓存数据
   * @param {string} key - 缓存键
   * @param {string} namespace - 命名空间
   * @returns {Promise<boolean>} 是否删除成功
   */
  async delete(key, namespace) {
    const fullKey = this._createKey(key, namespace);
    
    if (this.cache.has(fullKey)) {
      this.cache.delete(fullKey);
      
      if (this.timers.has(fullKey)) {
        clearTimeout(this.timers.get(fullKey));
        this.timers.delete(fullKey);
      }
      
      this.stats.deletes++;
      return true;
    }
    
    return false;
  }

  /**
   * 检查缓存是否存在
   * @param {string} key - 缓存键
   * @param {string} namespace - 命名空间
   * @returns {Promise<boolean>} 是否存在
   */
  async has(key, namespace) {
    const fullKey = this._createKey(key, namespace);
    
    if (this.cache.has(fullKey)) {
      const item = this.cache.get(fullKey);
      
      // 检查是否过期
      if (item.expiresAt && Date.now() > item.expiresAt) {
        this.cache.delete(fullKey);
        this.timers.delete(fullKey);
        return false;
      }
      
      return true;
    }
    
    return false;
  }

  /**
   * 清空所有缓存
   * @param {string} namespace - 命名空间，可选
   * @returns {Promise<void>}
   */
  async clear(namespace) {
    if (namespace) {
      // 清空指定命名空间
      const prefix = `${namespace}:`;
      const keysToDelete = [];
      
      for (const key of this.cache.keys()) {
        if (key.startsWith(prefix)) {
          keysToDelete.push(key);
        }
      }
      
      for (const key of keysToDelete) {
        this.cache.delete(key);
        if (this.timers.has(key)) {
          clearTimeout(this.timers.get(key));
          this.timers.delete(key);
        }
      }
    } else {
      // 清空所有缓存
      this.cache.clear();
      
      // 清除所有定时器
      for (const timer of this.timers.values()) {
        clearTimeout(timer);
      }
      this.timers.clear();
    }
    
    this.stats.clears++;
  }

  /**
   * 获取多个缓存数据
   * @param {string[]} keys - 缓存键数组
   * @param {string} namespace - 命名空间
   * @returns {Promise<Object>} 键值对对象
   */
  async getMultiple(keys, namespace) {
    const result = {};
    
    for (const key of keys) {
      const value = await this.get(key, namespace);
      if (value !== null) {
        result[key] = value;
      }
    }
    
    return result;
  }

  /**
   * 设置多个缓存数据
   * @param {Object} items - 键值对对象
   * @param {Object} options - 缓存选项
   * @returns {Promise<void>}
   */
  async setMultiple(items, options = {}) {
    const promises = [];
    
    for (const [key, value] of Object.entries(items)) {
      promises.push(this.set(key, value, options));
    }
    
    await Promise.all(promises);
  }

  /**
   * 删除多个缓存数据
   * @param {string[]} keys - 缓存键数组
   * @param {string} namespace - 命名空间
   * @returns {Promise<number>} 删除的数量
   */
  async deleteMultiple(keys, namespace) {
    let count = 0;
    
    for (const key of keys) {
      if (await this.delete(key, namespace)) {
        count++;
      }
    }
    
    return count;
  }

  /**
   * 获取缓存统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getStats() {
    return {
      ...this.stats,
      size: this.cache.size,
      hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0
    };
  }

  /**
   * 获取所有缓存键
   * @param {string} pattern - 键模式，可选
   * @returns {Promise<string[]>} 键数组
   */
  async keys(pattern) {
    const keys = Array.from(this.cache.keys());
    
    if (pattern) {
      const regex = new RegExp(pattern);
      return keys.filter(key => regex.test(key));
    }
    
    return keys;
  }

  /**
   * 设置缓存过期时间
   * @param {string} key - 缓存键
   * @param {number} ttl - 生存时间（毫秒）
   * @param {string} namespace - 命名空间
   * @returns {Promise<boolean>} 是否设置成功
   */
  async expire(key, ttl, namespace) {
    const fullKey = this._createKey(key, namespace);
    
    if (this.cache.has(fullKey)) {
      const item = this.cache.get(fullKey);
      item.expiresAt = Date.now() + ttl;
      this._setExpiration(fullKey, ttl);
      return true;
    }
    
    return false;
  }

  /**
   * 获取缓存剩余生存时间
   * @param {string} key - 缓存键
   * @param {string} namespace - 命名空间
   * @returns {Promise<number>} 剩余时间（毫秒），-1表示永不过期，-2表示不存在
   */
  async ttl(key, namespace) {
    const fullKey = this._createKey(key, namespace);
    
    if (this.cache.has(fullKey)) {
      const item = this.cache.get(fullKey);
      
      if (!item.expiresAt) {
        return -1; // 永不过期
      }
      
      const remaining = item.expiresAt - Date.now();
      return remaining > 0 ? remaining : -2; // 已过期
    }
    
    return -2; // 不存在
  }

  /**
   * 增加数值缓存
   * @param {string} key - 缓存键
   * @param {number} delta - 增加的值，默认为1
   * @param {string} namespace - 命名空间
   * @returns {Promise<number>} 增加后的值
   */
  async increment(key, delta = 1, namespace) {
    const currentValue = await this.get(key, namespace);
    const newValue = (typeof currentValue === 'number' ? currentValue : 0) + delta;
    await this.set(key, newValue, { namespace });
    return newValue;
  }

  /**
   * 减少数值缓存
   * @param {string} key - 缓存键
   * @param {number} delta - 减少的值，默认为1
   * @param {string} namespace - 命名空间
   * @returns {Promise<number>} 减少后的值
   */
  async decrement(key, delta = 1, namespace) {
    return await this.increment(key, -delta, namespace);
  }

  /**
   * 获取缓存大小
   * @returns {Promise<number>} 缓存项数量
   */
  async size() {
    return this.cache.size;
  }

  /**
   * 检查缓存服务是否可用
   * @returns {Promise<boolean>} 是否可用
   */
  async isAvailable() {
    return true;
  }

  /**
   * 获取缓存类型
   * @returns {string} 缓存类型
   */
  getType() {
    return 'MemoryCacheService';
  }

  /**
   * 刷新缓存（内存缓存无需持久化）
   * @returns {Promise<void>}
   */
  async flush() {
    // 内存缓存无需持久化操作
  }
}
