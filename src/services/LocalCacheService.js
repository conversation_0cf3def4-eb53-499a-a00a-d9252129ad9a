/**
 * LocalCacheService - 本地缓存服务类
 * 
 * 用于管理记忆数据的本地缓存，提高访问速度并减少OSS访问
 */
class LocalCacheService {
  constructor() {
    this.storage = window.localStorage;
    this.memoryCache = new Map();
    this.cachePrefix = 'memory_keeper_';
    this.maxCacheSize = 50; // 最大缓存记忆数量
    this.maxCacheAge = 24 * 60 * 60 * 1000; // 缓存过期时间（24小时）
  }
  
  /**
   * 获取缓存项
   * @param {string} key - 缓存键
   * @returns {Promise<any>} 缓存值，如果不存在则返回null
   */
  async getItem(key) {
    // 先从内存缓存获取
    if (this.memoryCache.has(key)) {
      const cacheItem = this.memoryCache.get(key);
      
      // 检查是否过期
      if (Date.now() - cacheItem.timestamp < this.maxCacheAge) {
        return cacheItem.value;
      }
      
      // 如果过期，从内存缓存中移除
      this.memoryCache.delete(key);
    }
    
    // 从localStorage获取
    try {
      const cacheKey = this.cachePrefix + key;
      const cachedData = this.storage.getItem(cacheKey);
      
      if (!cachedData) {
        return null;
      }
      
      const { value, timestamp } = JSON.parse(cachedData);
      
      // 检查是否过期
      if (Date.now() - timestamp < this.maxCacheAge) {
        // 添加到内存缓存
        this.memoryCache.set(key, { value, timestamp });
        return value;
      }
      
      // 如果过期，从localStorage中移除
      this.storage.removeItem(cacheKey);
      return null;
    } catch (error) {
      console.error('获取缓存项失败:', error);
      return null;
    }
  }
  
  /**
   * 设置缓存项
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @returns {Promise<void>}
   */
  async setItem(key, value) {
    try {
      const timestamp = Date.now();
      const cacheItem = { value, timestamp };
      
      // 添加到内存缓存
      this.memoryCache.set(key, cacheItem);
      
      // 如果内存缓存超过最大数量，移除最旧的项
      if (this.memoryCache.size > this.maxCacheSize) {
        const oldestKey = this._getOldestCacheKey();
        if (oldestKey) {
          this.memoryCache.delete(oldestKey);
        }
      }
      
      // 保存到localStorage
      const cacheKey = this.cachePrefix + key;
      this.storage.setItem(cacheKey, JSON.stringify(cacheItem));
    } catch (error) {
      console.error('设置缓存项失败:', error);
      
      // 如果是存储空间不足错误，清理部分缓存
      if (error.name === 'QuotaExceededError') {
        this._cleanupCache();
        
        // 重试保存
        try {
          const timestamp = Date.now();
          const cacheKey = this.cachePrefix + key;
          this.storage.setItem(cacheKey, JSON.stringify({ value, timestamp }));
        } catch (retryError) {
          console.error('重试设置缓存项失败:', retryError);
        }
      }
    }
  }
  
  /**
   * 移除缓存项
   * @param {string} key - 缓存键
   * @returns {Promise<void>}
   */
  async removeItem(key) {
    try {
      // 从内存缓存移除
      this.memoryCache.delete(key);
      
      // 从localStorage移除
      const cacheKey = this.cachePrefix + key;
      this.storage.removeItem(cacheKey);
    } catch (error) {
      console.error('移除缓存项失败:', error);
    }
  }
  
  /**
   * 清空缓存
   * @returns {Promise<void>}
   */
  async clear() {
    try {
      // 清空内存缓存
      this.memoryCache.clear();
      
      // 清空localStorage中的缓存项
      const keysToRemove = [];
      
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i);
        if (key.startsWith(this.cachePrefix)) {
          keysToRemove.push(key);
        }
      }
      
      keysToRemove.forEach(key => {
        this.storage.removeItem(key);
      });
    } catch (error) {
      console.error('清空缓存失败:', error);
    }
  }
  
  /**
   * 获取最旧的缓存键
   * @returns {string|null} 最旧的缓存键，如果缓存为空则返回null
   */
  _getOldestCacheKey() {
    if (this.memoryCache.size === 0) {
      return null;
    }
    
    let oldestKey = null;
    let oldestTimestamp = Infinity;
    
    for (const [key, cacheItem] of this.memoryCache.entries()) {
      if (cacheItem.timestamp < oldestTimestamp) {
        oldestTimestamp = cacheItem.timestamp;
        oldestKey = key;
      }
    }
    
    return oldestKey;
  }
  
  /**
   * 清理缓存
   * 移除一半的缓存项，优先移除最旧的
   */
  _cleanupCache() {
    try {
      // 获取所有缓存项
      const cacheItems = [];
      
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i);
        if (key.startsWith(this.cachePrefix)) {
          try {
            const item = JSON.parse(this.storage.getItem(key));
            cacheItems.push({
              key,
              timestamp: item.timestamp
            });
          } catch (error) {
            // 如果解析失败，直接移除
            this.storage.removeItem(key);
          }
        }
      }
      
      // 按时间戳排序
      cacheItems.sort((a, b) => a.timestamp - b.timestamp);
      
      // 移除一半的缓存项
      const removeCount = Math.ceil(cacheItems.length / 2);
      for (let i = 0; i < removeCount; i++) {
        this.storage.removeItem(cacheItems[i].key);
      }
      
      console.log(`已清理 ${removeCount} 个缓存项`);
    } catch (error) {
      console.error('清理缓存失败:', error);
    }
  }
}

// 导出单例实例
const localCacheService = new LocalCacheService();
export default localCacheService;
