/**
 * StorageMigrationService - 存储迁移服务
 *
 * 用于在不同的存储提供者之间迁移数据，
 * 支持从一种OSS存储迁移到另一种OSS存储。
 */

import ossStorageService from './OssStorageService';
import cloudStorageService from './CloudStorageService';
import storageService from './StorageService';
import { StorageTypes } from './storage';

class StorageMigrationService {
  constructor() {
    this.migrationInProgress = false;
    this.progress = 0;
    this.totalItems = 0;
    this.processedItems = 0;
    this.errors = [];
    this.sourceProvider = null;
    this.destinationProvider = null;
  }

  /**
   * 检查是否可以进行迁移
   * @param {string} sourceProvider - 源存储提供者
   * @param {string} destinationProvider - 目标存储提供者
   * @returns {Promise<Object>} 检查结果
   */
  async checkMigrationPossibility(sourceProvider, destinationProvider) {
    try {
      // 检查源和目标是否相同
      if (sourceProvider === destinationProvider) {
        return {
          possible: false,
          message: '源存储提供者和目标存储提供者不能相同'
        };
      }

      // 获取当前设置
      const settings = await storageService.getSettings();

      // 检查源提供者配置是否完整
      const sourceConfig = settings[sourceProvider];
      if (!sourceConfig || !this._isConfigComplete(sourceProvider, sourceConfig)) {
        return {
          possible: false,
          message: `源存储提供者 ${this._getProviderDisplayName(sourceProvider)} 配置不完整`
        };
      }

      // 检查目标提供者配置是否完整
      const destConfig = settings[destinationProvider];
      if (!destConfig || !this._isConfigComplete(destinationProvider, destConfig)) {
        return {
          possible: false,
          message: `目标存储提供者 ${this._getProviderDisplayName(destinationProvider)} 配置不完整`
        };
      }

      // 尝试连接源提供者
      const currentProvider = settings.storageProvider;
      settings.storageProvider = sourceProvider;
      await storageService.saveSettings(settings);

      await ossStorageService.initialize();
      const sourceConnected = await ossStorageService.isInitialized;

      if (!sourceConnected) {
        // 恢复原始设置
        settings.storageProvider = currentProvider;
        await storageService.saveSettings(settings);

        return {
          possible: false,
          message: `无法连接到源存储提供者 ${this._getProviderDisplayName(sourceProvider)}`
        };
      }

      // 尝试连接目标提供者
      settings.storageProvider = destinationProvider;
      await storageService.saveSettings(settings);

      await cloudStorageService.initialize();
      const destConnected = await cloudStorageService.isInitialized;

      // 恢复原始设置
      settings.storageProvider = currentProvider;
      await storageService.saveSettings(settings);

      if (!destConnected) {
        return {
          possible: false,
          message: `无法连接到目标存储提供者 ${this._getProviderDisplayName(destinationProvider)}`
        };
      }

      return {
        possible: true,
        message: '迁移检查通过，可以进行迁移'
      };
    } catch (error) {
      console.error('检查迁移可能性失败:', error);
      return {
        possible: false,
        message: `检查迁移可能性失败: ${error.message}`
      };
    }
  }

  /**
   * 开始迁移数据
   * @param {string} sourceProvider - 源存储提供者
   * @param {string} destinationProvider - 目标存储提供者
   * @param {Function} progressCallback - 进度回调函数
   * @returns {Promise<Object>} 迁移结果
   */
  async startMigration(sourceProvider, destinationProvider, progressCallback = null) {
    try {
      // 检查是否已经有迁移在进行中
      if (this.migrationInProgress) {
        return {
          success: false,
          message: '已有迁移任务正在进行中'
        };
      }

      // 检查迁移可能性
      const checkResult = await this.checkMigrationPossibility(sourceProvider, destinationProvider);
      if (!checkResult.possible) {
        return {
          success: false,
          message: checkResult.message
        };
      }

      // 设置迁移状态
      this.migrationInProgress = true;
      this.progress = 0;
      this.totalItems = 0;
      this.processedItems = 0;
      this.errors = [];
      this.sourceProvider = sourceProvider;
      this.destinationProvider = destinationProvider;

      // 获取当前设置
      const settings = await storageService.getSettings();
      const currentProvider = settings.storageProvider;

      try {
        // 步骤1: 连接到源存储提供者
        this._updateProgress(5, '正在连接到源存储提供者...', progressCallback);
        settings.storageProvider = sourceProvider;
        await storageService.saveSettings(settings);
        await ossStorageService.initialize();

        // 步骤2: 获取源存储中的所有对象
        this._updateProgress(10, '正在获取源存储中的数据...', progressCallback);
        const sourceObjects = await this._listAllObjects();
        this.totalItems = sourceObjects.length;

        if (this.totalItems === 0) {
          this.migrationInProgress = false;
          return {
            success: false,
            message: '源存储中没有找到数据'
          };
        }

        this._updateProgress(20, `找到 ${this.totalItems} 个对象，准备迁移...`, progressCallback);

        // 步骤3: 连接到目标存储提供者
        this._updateProgress(25, '正在连接到目标存储提供者...', progressCallback);
        settings.storageProvider = destinationProvider;
        await storageService.saveSettings(settings);
        await cloudStorageService.initialize();

        // 步骤4: 首先迁移元数据文件，这是最重要的
        this._updateProgress(30, '迁移元数据文件...', progressCallback);

        // 找出元数据文件
        const metadataObjects = sourceObjects.filter(obj => {
          const key = obj.Key || obj.key;
          return key && key.includes('metadata.json');
        });

        // 初始化迁移结果对象
        let migrationResult = {
          successCount: 0,
          errorCount: 0,
          errors: []
        };

        // 如果有元数据文件，先迁移它们
        if (metadataObjects.length > 0) {
          this._updateProgress(32, `找到 ${metadataObjects.length} 个元数据文件，先迁移这些文件...`, progressCallback);

          // 先迁移元数据文件
          const metadataResult = await this._migrateObjects(metadataObjects, progressCallback);

          if (metadataResult.errorCount > 0) {
            this._updateProgress(35, `元数据文件迁移完成，成功 ${metadataResult.successCount} 个，失败 ${metadataResult.errorCount} 个`, progressCallback);
          } else {
            this._updateProgress(35, `元数据文件迁移成功，共 ${metadataResult.successCount} 个`, progressCallback);
          }

          // 从源对象列表中移除已经迁移的元数据文件
          const metadataKeys = metadataObjects.map(obj => obj.Key || obj.key);
          const remainingObjects = sourceObjects.filter(obj => {
            const key = obj.Key || obj.key;
            return !metadataKeys.includes(key);
          });

          // 步骤5: 迁移其余数据
          this._updateProgress(40, `开始迁移其余 ${remainingObjects.length} 个对象...`, progressCallback);
          const remainingResult = await this._migrateObjects(remainingObjects, progressCallback);

          // 合并结果
          migrationResult = {
            successCount: metadataResult.successCount + remainingResult.successCount,
            errorCount: metadataResult.errorCount + remainingResult.errorCount,
            errors: [...metadataResult.errors, ...remainingResult.errors],
            skippedCount: (metadataResult.skippedCount || 0) + (remainingResult.skippedCount || 0),
            skippedFiles: [...(metadataResult.skippedFiles || []), ...(remainingResult.skippedFiles || [])]
          };
        } else {
          // 没有元数据文件，直接迁移所有对象
          this._updateProgress(35, '未找到元数据文件，直接迁移所有对象...', progressCallback);
          migrationResult = await this._migrateObjects(sourceObjects, progressCallback);
        }

        // 步骤6: 更新设置，将当前存储提供者设置为目标提供者
        this._updateProgress(95, '更新存储设置...', progressCallback);
        settings.storageProvider = destinationProvider;
        await storageService.saveSettings(settings);

        // 步骤7: 完成迁移
        this._updateProgress(100, '迁移完成', progressCallback);
        this.migrationInProgress = false;

        // 构建详细的迁移结果信息
        const skippedCount = migrationResult.skippedCount || 0;
        let message = `迁移完成，成功迁移 ${migrationResult.successCount} 个对象`;
        if (skippedCount > 0) {
          message += `，跳过 ${skippedCount} 个重复文件`;
        }
        if (migrationResult.errorCount > 0) {
          message += `，失败 ${migrationResult.errorCount} 个`;
        }

        return {
          success: true,
          message: message,
          details: {
            ...migrationResult,
            duplicateStats: {
              duplicateFiles: migrationResult.skippedFiles || [],
              totalDuplicates: skippedCount
            }
          }
        };
      } catch (error) {
        // 发生错误，恢复原始设置
        console.error('迁移过程中发生错误:', error);
        settings.storageProvider = currentProvider;
        await storageService.saveSettings(settings);

        this.migrationInProgress = false;

        // 返回错误结果而不是抛出异常
        return {
          success: false,
          message: `迁移过程中发生错误: ${error.message}`,
          error,
          details: {
            successCount: this.processedItems,
            errorCount: this.errors.length,
            errors: this.errors
          }
        };
      }
    } catch (error) {
      console.error('迁移失败:', error);
      this.migrationInProgress = false;
      return {
        success: false,
        message: `迁移失败: ${error.message}`,
        error
      };
    }
  }

  /**
   * 获取迁移进度
   * @returns {Object} 迁移进度信息
   */
  getMigrationProgress() {
    return {
      inProgress: this.migrationInProgress,
      progress: this.progress,
      totalItems: this.totalItems,
      processedItems: this.processedItems,
      errors: this.errors,
      sourceProvider: this.sourceProvider,
      destinationProvider: this.destinationProvider
    };
  }

  /**
   * 取消迁移
   * @returns {Promise<boolean>} 是否成功取消
   */
  async cancelMigration() {
    if (!this.migrationInProgress) {
      return false;
    }

    this.migrationInProgress = false;

    // 恢复原始设置
    const settings = await storageService.getSettings();
    settings.storageProvider = this.sourceProvider;
    await storageService.saveSettings(settings);

    return true;
  }

  /**
   * 列出所有对象
   * @returns {Promise<Array>} 对象列表
   * @private
   */
  async _listAllObjects() {
    try {
      // 确保OSS存储服务已初始化
      if (!ossStorageService.isInitialized) {
        await ossStorageService.initialize();
      }

      // 获取用户ID
      const userId = ossStorageService.userId;
      if (!userId) {
        throw new Error('无法获取用户ID');
      }

      // 列出所有对象
      const allObjects = await ossStorageService.listObjects(`users/${userId}/`);
      return allObjects;
    } catch (error) {
      console.error('列出所有对象失败:', error);
      throw error;
    }
  }

  /**
   * 迁移对象
   * @param {Array} objects - 要迁移的对象列表
   * @param {Function} progressCallback - 进度回调函数
   * @returns {Promise<Object>} 迁移结果
   * @private
   */
  async _migrateObjects(objects, progressCallback) {
    const results = {
      successCount: 0,
      errorCount: 0,
      errors: [],
      skippedCount: 0,
      skippedFiles: []
    };

    // 计算每个对象的进度增量
    const progressIncrement = 65 / objects.length; // 从30%到95%的进度范围

    for (let i = 0; i < objects.length; i++) {
      try {
        const object = objects[i];
        const key = object.Key || object.key;

        if (!key) {
          throw new Error(`对象缺少Key属性: ${JSON.stringify(object)}`);
        }

        // 更新进度
        const currentProgress = 30 + (i * progressIncrement);
        this._updateProgress(
          currentProgress,
          `正在检查 ${i + 1}/${objects.length}: ${key}`,
          progressCallback
        );

        // 检查是否应该跳过该文件（重复文件检测）
        const shouldSkip = await this._shouldSkipFile(key, object);
        if (shouldSkip) {
          console.log(`跳过重复文件: ${key}`);
          results.skippedCount++;
          results.skippedFiles.push({
            key: key,
            md5: shouldSkip.md5,
            size: shouldSkip.size || 0,
            isLargeFile: shouldSkip.size > 10 * 1024 * 1024 // 大于10MB的文件
          });
          this.processedItems++;
          continue;
        }

        // 更新进度为迁移状态
        this._updateProgress(
          currentProgress,
          `正在迁移 ${i + 1}/${objects.length}: ${key}`,
          progressCallback
        );

        // 确定内容类型
        let contentType = 'application/json';
        if (key.endsWith('.jpg') || key.endsWith('.jpeg')) {
          contentType = 'image/jpeg';
        } else if (key.endsWith('.png')) {
          contentType = 'image/png';
        } else if (key.endsWith('.gif')) {
          contentType = 'image/gif';
        } else if (key.endsWith('.mp4')) {
          contentType = 'video/mp4';
        } else if (key.endsWith('.webm')) {
          contentType = 'video/webm';
        } else if (key.endsWith('.mp3')) {
          contentType = 'audio/mpeg';
        } else if (key.endsWith('.wav')) {
          contentType = 'audio/wav';
        }

        // 根据文件类型决定获取方式
        let data;
        if (contentType.startsWith('image/') || contentType.startsWith('video/') || contentType.startsWith('audio/')) {
          // 对于二进制文件，使用签名URL直接获取原始数据
          console.log(`检测到二进制文件: ${key}, 类型: ${contentType}, 使用签名URL获取`);
          try {
            // 获取签名URL
            const signedUrl = await ossStorageService.getSignedUrl(key);
            console.log(`获取到签名URL: ${signedUrl}`);

            // 使用fetch直接获取二进制数据
            const response = await fetch(signedUrl);
            if (!response.ok) {
              throw new Error(`获取文件失败，状态码: ${response.status}`);
            }

            // 获取二进制数据
            data = await response.arrayBuffer();
            console.log(`成功获取二进制数据，大小: ${data.byteLength} 字节`);
          } catch (error) {
            console.error(`使用签名URL获取文件失败，尝试其他方法: ${error.message}`);

            // 尝试不同的方法获取二进制数据，适应不同的存储提供者
            try {
              // 方法1: 如果是华为云OBS，尝试使用SaveByType选项
              if (this.sourceProvider === StorageTypes.HUAWEI_OBS) {
                data = await ossStorageService.getObject(key, { SaveByType: 'arraybuffer' });
                console.log(`使用华为云OBS特有选项成功获取二进制数据`);
              } else {
                // 方法2: 直接获取对象，然后处理返回的数据
                const rawData = await ossStorageService.getObject(key);

                // 如果返回的是ArrayBuffer或Uint8Array，直接使用
                if (rawData instanceof ArrayBuffer || rawData instanceof Uint8Array) {
                  data = rawData;
                  console.log(`直接获取到二进制数据`);
                }
                // 如果是字符串但包含二进制数据特征
                else if (typeof rawData === 'string' &&
                         (rawData.startsWith('\x89PNG') ||
                          rawData.startsWith('\xff\xd8\xff') ||
                          rawData.startsWith('GIF'))) {
                  // 尝试将字符串转换为字节数组
                  const bytes = new Uint8Array(rawData.length);
                  for (let i = 0; i < rawData.length; i++) {
                    bytes[i] = rawData.charCodeAt(i) & 0xff;
                  }
                  data = bytes.buffer;
                  console.log(`将二进制字符串转换为ArrayBuffer，长度: ${data.byteLength}`);
                }
                // 如果是其他格式，尝试再次使用签名URL方法，但使用不同的参数
                else {
                  console.log(`无法直接获取二进制数据，尝试再次使用签名URL`);
                  const alternativeSignedUrl = await ossStorageService.getSignedUrl(key, 7200); // 使用更长的过期时间
                  const altResponse = await fetch(alternativeSignedUrl, { cache: 'no-store' });
                  if (!altResponse.ok) {
                    throw new Error(`替代方法获取文件失败，状态码: ${altResponse.status}`);
                  }
                  data = await altResponse.arrayBuffer();
                  console.log(`使用替代签名URL成功获取二进制数据，大小: ${data.byteLength}`);
                }
              }
            } catch (fallbackError) {
              console.error(`所有获取二进制数据的方法都失败，将尝试直接获取: ${fallbackError.message}`);
              // 最后的尝试，直接获取对象
              data = await ossStorageService.getObject(key);
              console.log(`使用最后的备用方法获取数据，类型: ${typeof data}`);
            }
          }
        } else {
          // 对于文本文件，直接获取
          console.log(`获取文本文件: ${key}, 类型: ${contentType}`);
          data = await ossStorageService.getObject(key);
        }

        // 处理数据格式
        let processedData = data;

        // 如果是内容类型是JSON，确保数据是字符串
        if (contentType === 'application/json') {
          // 如果数据是JSON对象，则转换为字符串
          if (typeof data === 'object' && data !== null && !(data instanceof ArrayBuffer) && !(data instanceof Blob)) {
            processedData = JSON.stringify(data);
          } else if (typeof data === 'string') {
            // 如果已经是字符串，尝试解析并重新序列化以确保格式正确
            try {
              const parsedData = JSON.parse(data);
              processedData = JSON.stringify(parsedData);
            } catch (e) {
              // 如果解析失败，保持原样
              processedData = data;
            }
          }
        }

        // 如果是ArrayBuffer或Uint8Array，处理为正确的格式
        if (data instanceof ArrayBuffer) {
          processedData = new Uint8Array(data);
        }

        // 根据目标提供者类型调整上传方式
        try {
          console.log(`尝试上传对象 ${key} 到 ${this.destinationProvider}`);

          if (this.destinationProvider === StorageTypes.MINIO) {
            // MinIO的putObject方法
            // 对于MinIO，我们需要确保数据格式正确
            let minioData = processedData;

            // 如果是JSON对象，转换为字符串
            if (typeof processedData === 'object' && processedData !== null &&
                !(processedData instanceof ArrayBuffer) &&
                !(processedData instanceof Blob) &&
                !(processedData instanceof Uint8Array)) {
              minioData = JSON.stringify(processedData);
            }

            // 如果是字符串但不是JSON，转换为Blob
            if (typeof minioData === 'string' && contentType !== 'application/json') {
              minioData = new Blob([minioData], { type: contentType });
            }

            // 对于图片和视频文件，添加特殊处理
            if (contentType.startsWith('image/') || contentType.startsWith('video/')) {
              console.log(`特殊处理图片/视频文件: ${key}, 类型: ${contentType}`);

              // 如果是ArrayBuffer，确保转换为Uint8Array
              if (minioData instanceof ArrayBuffer) {
                minioData = new Uint8Array(minioData);
                console.log(`将ArrayBuffer转换为Uint8Array, 长度: ${minioData.length}`);
              }

              // 如果是字符串但可能是二进制数据
              if (typeof minioData === 'string') {
                // 检查是否是二进制字符串
                if (minioData.startsWith('\x89PNG') || minioData.startsWith('\xff\xd8\xff') || minioData.startsWith('GIF')) {
                  console.log('检测到二进制图片数据被解释为字符串，进行转换');
                  try {
                    // 尝试将字符串转换为字节数组
                    const bytes = new Uint8Array(minioData.length);
                    for (let i = 0; i < minioData.length; i++) {
                      bytes[i] = minioData.charCodeAt(i) & 0xff;
                    }
                    minioData = bytes;
                    console.log(`成功将二进制字符串转换为Uint8Array，长度: ${bytes.length}`);
                  } catch (error) {
                    console.error('转换二进制字符串失败:', error);
                  }
                }
              }
            }

            console.log(`上传到MinIO: ${key}, 类型: ${contentType}, 数据类型: ${typeof minioData}`);

            // 使用华为云OBS格式的选项对象调用MinIO的putObject方法
            // 这样可以确保元数据和内容类型都正确传递
            await cloudStorageService.provider.putObject(key, minioData, {
              contentType: contentType
            });
          } else if (this.destinationProvider === StorageTypes.HUAWEI_OBS) {
            // 华为云OBS的putObject方法
            console.log(`上传到华为云OBS: ${key}, 类型: ${contentType}`);
            await cloudStorageService.provider.putObject(key, processedData, {
              contentType: contentType
            });
          } else {
            // 其他提供者，尝试两种方式
            console.log(`上传到其他提供者: ${this.destinationProvider}, 键: ${key}`);
            try {
              // 先尝试MinIO的方式
              await cloudStorageService.provider.putObject(key, processedData, contentType);
            } catch (innerError) {
              console.log(`第一种上传方式失败，尝试第二种方式: ${innerError.message}`);
              // 如果失败，尝试华为云OBS的方式
              await cloudStorageService.provider.putObject(key, processedData, {
                contentType: contentType
              });
            }
          }

          console.log(`成功上传对象: ${key}`);
        } catch (uploadError) {
          console.error(`上传失败: ${uploadError.message}`);
          console.error(`尝试直接使用OSS存储服务上传: ${key}`);

          // 如果上述方法都失败，尝试直接使用OSS存储服务
          try {
            const simplifiedKey = key.replace(`users/${ossStorageService.userId}/`, '');
            await ossStorageService.putObject(simplifiedKey, processedData, contentType);
            console.log(`使用OSS存储服务成功上传: ${simplifiedKey}`);
          } catch (ossError) {
            console.error(`OSS存储服务上传也失败: ${ossError.message}`);
            throw uploadError; // 抛出原始错误
          }
        }

        // 更新计数
        results.successCount++;
        this.processedItems++;
      } catch (error) {
        const objectKey = objects[i].Key || objects[i].key;
        console.error(`迁移对象失败: ${objectKey}`, error);

        // 记录详细错误信息
        const errorInfo = {
          key: objectKey,
          error: error.message,
          stack: error.stack,
          sourceProvider: this.sourceProvider,
          destinationProvider: this.destinationProvider,
          timestamp: new Date().toISOString()
        };

        // 记录错误
        results.errorCount++;
        results.errors.push(errorInfo);
        this.errors.push(errorInfo);

        // 如果是元数据文件，这是关键文件，尝试特殊处理
        if (objectKey.includes('metadata.json') || objectKey.endsWith('.json')) {
          console.warn(`关键文件 ${objectKey} 迁移失败，尝试备用方法...`);
          try {
            // 尝试直接使用不同的方式获取和上传
            const rawData = await ossStorageService.provider.getObject(objectKey);
            let jsonData;

            // 尝试解析数据
            if (typeof rawData === 'string') {
              try {
                jsonData = JSON.parse(rawData);
              } catch (e) {
                jsonData = { content: rawData };
              }
            } else if (rawData instanceof ArrayBuffer) {
              const decoder = new TextDecoder('utf-8');
              const text = decoder.decode(rawData);
              try {
                jsonData = JSON.parse(text);
              } catch (e) {
                jsonData = { content: text };
              }
            } else {
              jsonData = rawData;
            }

            // 尝试直接使用目标存储服务的原生方法
            if (this.destinationProvider === StorageTypes.MINIO) {
              await cloudStorageService.provider.putObject(objectKey, JSON.stringify(jsonData), 'application/json');
              console.log(`关键文件 ${objectKey} 使用备用方法迁移成功`);

              // 更新计数
              results.successCount++;
              this.processedItems++;

              // 从错误列表中移除
              results.errors = results.errors.filter(e => e.key !== objectKey);
              this.errors = this.errors.filter(e => e.key !== objectKey);
              results.errorCount--;
            }
          } catch (fallbackError) {
            console.error(`关键文件备用方法也失败:`, fallbackError);
          }
        }
      }
    }

    return results;
  }

  /**
   * 更新进度
   * @param {number} progress - 进度百分比
   * @param {string} message - 进度消息
   * @param {Function} callback - 回调函数
   * @private
   */
  _updateProgress(progress, message, callback) {
    this.progress = Math.min(Math.round(progress), 100);

    if (callback && typeof callback === 'function') {
      callback({
        progress: this.progress,
        message,
        processedItems: this.processedItems,
        totalItems: this.totalItems,
        errors: this.errors
      });
    }
  }

  /**
   * 检查配置是否完整
   * @param {string} provider - 提供者类型
   * @param {Object} config - 配置对象
   * @returns {boolean} 配置是否完整
   * @private
   */
  _isConfigComplete(provider, config) {
    if (!config) return false;

    switch (provider) {
      case StorageTypes.HUAWEI_OBS:
        return !!(
          config.accessKeyId &&
          config.secretAccessKey &&
          config.region &&
          config.endpoint &&
          config.bucketName
        );
      case StorageTypes.MINIO:
        return !!(
          config.accessKey &&
          config.secretKey &&
          config.endPoint &&
          config.bucketName
        );
      default:
        return false;
    }
  }

  /**
   * 检查是否应该跳过文件（重复文件检测）
   * @param {string} key - 文件键
   * @param {Object} sourceObject - 源文件对象
   * @returns {Promise<Object|null>} 如果应该跳过，返回文件信息；否则返回null
   * @private
   */
  async _shouldSkipFile(key, sourceObject) {
    try {
      // 获取源文件的元数据
      let sourceMetadata;
      try {
        sourceMetadata = await ossStorageService.provider.getObjectMetadata(key);
      } catch (error) {
        console.warn(`获取源文件元数据失败: ${key}`, error);
        // 如果获取元数据失败，不跳过，继续迁移
        return null;
      }

      // 检查目标文件是否存在
      let targetMetadata;
      try {
        targetMetadata = await cloudStorageService.provider.getObjectMetadata(key);
      } catch (error) {
        // 目标文件不存在，需要迁移
        console.log(`目标文件不存在: ${key}`);
        return null;
      }

      // 比较ETag（MD5哈希值）
      const sourceETag = this._normalizeETag(sourceMetadata.etag);
      const targetETag = this._normalizeETag(targetMetadata.etag);

      if (sourceETag && targetETag && sourceETag === targetETag) {
        // 文件相同，可以跳过
        console.log(`文件MD5相同，跳过: ${key}, MD5: ${sourceETag}`);
        return {
          md5: sourceETag,
          size: sourceMetadata.size || 0
        };
      }

      // 如果ETag不存在或不同，需要迁移
      console.log(`文件MD5不同或不存在，需要迁移: ${key}`);
      return null;
    } catch (error) {
      console.error(`检查文件是否重复失败: ${key}`, error);
      // 出错时默认不跳过，确保数据安全
      return null;
    }
  }

  /**
   * 标准化ETag值（移除引号和空格）
   * @param {string} etag - ETag值
   * @returns {string} 标准化后的ETag
   * @private
   */
  _normalizeETag(etag) {
    if (!etag) return '';
    return etag.replace(/["\'\s]/g, '').toLowerCase();
  }

  /**
   * 获取提供者显示名称
   * @param {string} provider - 提供者类型
   * @returns {string} 显示名称
   * @private
   */
  _getProviderDisplayName(provider) {
    switch (provider) {
      case StorageTypes.HUAWEI_OBS:
        return '华为云OBS';
      case StorageTypes.MINIO:
        return 'MinIO';
      case StorageTypes.ALIYUN_OSS:
        return '阿里云OSS';
      case StorageTypes.AMAZON_S3:
        return 'Amazon S3';
      case StorageTypes.TENCENT_COS:
        return '腾讯云COS';
      default:
        return provider;
    }
  }
}

// 导出单例实例
const storageMigrationService = new StorageMigrationService();
export default storageMigrationService;
