/**
 * CloudStorageService - 云存储服务类
 *
 * 用于与云存储服务（如华为云OBS）交互，
 * 提供数据同步、上传、下载等功能。
 */

// 导入存储管理器
import { storageManager, StorageTypes } from './storage';
import storageService from './StorageService';
import EncryptionService from './EncryptionService';

class CloudStorageService {
  constructor() {
    this.provider = null;
    this.isInitialized = false;
  }

  /**
   * 初始化云存储服务
   * @returns {Promise<boolean>} 初始化是否成功
   */
  async initialize() {
    try {
      // 获取云存储设置
      const settings = await storageService.getSettings();
      const providerName = settings.storageProvider;
      let config = null;
      if (providerName === StorageTypes.HUAWEI_OBS) {
        // 初始化华为云OBS存储提供者
        config = settings.huaweiObs;
        await storageManager.registerProvider(StorageTypes.HUAWEI_OBS, StorageTypes.HUAWEI_OBS, config, false);
      } else if (providerName === StorageTypes.MINIO) {
        // 初始化MinIO存储提供者
        config = settings.minio;
        await storageManager.registerProvider(StorageTypes.MINIO, StorageTypes.MINIO, config, false);
      } else {
        console.error('未知的存储提供者：', providerName);
        return false;
      }

      this.provider = storageManager.getProvider(providerName);
      this.bucketName = this.provider.getBucketName();
      this.isInitialized = true;

      console.log('云存储服务初始化成功');
      return true;
    } catch (error) {
      console.error('初始化云存储服务失败:', error);
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * 检查连接是否有效
   * @returns {Promise<boolean>} 连接是否有效
   */
  async testConnection() {
    console.log("当前初始化状态:" + this.isInitialized);
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.provider) {
      throw new Error('云存储服务未初始化');
    }

    return this.provider.testConnection();
  }

  /**
   * 同步数据到云存储
   * @returns {Promise<Object>} 同步结果
   */
  async syncToCloud() {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.provider) {
      throw new Error('云存储服务未初始化');
    }

    try {
      // 获取所有数据
      const data = await storageService.exportData();

      // 检查是否需要加密
      const settings = await storageService.getSettings();
      let content = JSON.stringify(data);

      if (settings.encryption && settings.encryptionKey) {
        // 加密数据
        content = await EncryptionService.encrypt(content, settings.encryptionKey, settings.encryptionAlgorithm);
      }

      // 上传到云存储
      const key = `memorykeeper-backup-${new Date().toISOString().slice(0, 10)}.json`;

      try {
        // 准备元数据
        const metadata = {
          'sync-date': new Date().toISOString(),
          'encrypted': settings.encryption ? 'true' : 'false',
          'encryption-algorithm': settings.encryption ? settings.encryptionAlgorithm : '',
        };

        // 使用抽象接口上传
        const result = await this.provider.putObject(key, content, 'application/json', metadata);

        return {
          success: true,
          key,
          syncDate: new Date().toISOString(),
          encrypted: settings.encryption,
          result
        };
      } catch (error) {
        console.error(`上传失败: ${key}`, error);
        throw error;
      }
    } catch (error) {
      console.error('同步到云存储失败:', error);
      throw error;
    }
  }

  /**
   * 从云存储同步数据
   * @param {string} key - 要下载的对象键（可选，默认使用最新的备份）
   * @returns {Promise<Object>} 同步结果
   */
  async syncFromCloud(key) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.provider) {
      throw new Error('云存储服务未初始化');
    }

    try {
      // 如果没有指定键，获取最新的备份
      if (!key) {
        const listResult = await this.listBackups();
        if (listResult.backups.length === 0) {
          throw new Error('没有找到备份');
        }

        // 按日期排序，获取最新的备份
        const latestBackup = listResult.backups.sort((a, b) => {
          return new Date(b.lastModified) - new Date(a.lastModified);
        })[0];

        key = latestBackup.key;
      }

      // 从云存储下载
      const result = await this.provider.getObject(key);

      // 获取元数据
      let metadata = {};
      let content = '';

      // 处理不同提供者的返回结果格式
      if (result.InterfaceResult) {
        // 华为 OBS 格式
        metadata = result.InterfaceResult.Metadata || {};
        content = result.InterfaceResult.Content.toString();
      } else if (result instanceof ArrayBuffer || result instanceof Uint8Array) {
        // ArrayBuffer 或 Uint8Array 格式
        const decoder = new TextDecoder('utf-8');
        content = decoder.decode(result);
      } else {
        // 其他格式
        content = typeof result === 'string' ? result : JSON.stringify(result);
      }

      const isEncrypted = metadata['encrypted'] === 'true';
      const encryptionAlgorithm = metadata['encryption-algorithm'] || '';

      // 如果数据是加密的，需要解密
      if (isEncrypted) {
        const settings = await storageService.getSettings();
        if (!settings.encryptionKey) {
          throw new Error('需要加密密钥来解密数据');
        }

        content = await EncryptionService.decrypt(content, settings.encryptionKey, encryptionAlgorithm || settings.encryptionAlgorithm);
      }

      // 解析数据
      const data = JSON.parse(content);

      // 导入数据
      await storageService.importData(data);

      return {
        success: true,
        key,
        syncDate: new Date().toISOString(),
        data,
      };
    } catch (error) {
      console.error('从云存储同步失败:', error);
      throw error;
    }
  }

  /**
   * 列出所有备份
   * @returns {Promise<Object>} 备份列表
   */
  async listBackups() {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.provider) {
      throw new Error('云存储服务未初始化');
    }

    return this.provider.listBackups({
        Prefix: 'memorykeeper-backup-',
      })
  }

  /**
   * 删除备份
   * @param {string} key - 要删除的对象键
   * @returns {Promise<Object>} 删除结果
   */
  async deleteBackup(key) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.provider) {
      throw new Error('云存储服务未初始化');
    }

    try {
      const result = await this.provider.deleteObject(key);
      return {
        success: true,
        key,
        result
      };
    } catch (error) {
      console.error(`删除备份失败: ${key}`, error);
      throw error;
    }
  }
}

// 导出单例实例
const cloudStorageService = new CloudStorageService();
export default cloudStorageService;
