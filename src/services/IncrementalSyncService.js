/**
 * IncrementalSyncService - 增量同步服务
 * 
 * 用于实现基于版本的增量同步机制，只同步变更的数据块，
 * 而不是整个元数据，提高同步效率并减少网络传输。
 */
import storageService from './StorageService';
import ossStorageService from './OssStorageService';
import indexedDBCacheService from './IndexedDBCacheService';

class IncrementalSyncService {
  constructor() {
    this.isInitialized = false;
    this.syncInProgress = false;
    this.lastSyncTimestamp = 0;
    this.changedChunks = new Set(); // 存储已更改的块ID
    this.changedMemories = new Set(); // 存储已更改的记忆ID
    this.deletedMemories = new Set(); // 存储已删除的记忆ID
    this.conflictResolutionStrategies = {
      AUTO_NEWEST: 'auto_newest', // 自动选择最新版本
      AUTO_LOCAL: 'auto_local', // 自动选择本地版本
      AUTO_REMOTE: 'auto_remote', // 自动选择远程版本
      MANUAL: 'manual' // 手动解决冲突
    };
  }

  /**
   * 初始化服务
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // 确保OSS存储服务已初始化
      if (!ossStorageService.isInitialized) {
        await ossStorageService.initialize();
      }

      // 从本地缓存获取上次同步时间
      const syncInfo = await indexedDBCacheService.getItem('last_sync_info', 'sync');
      if (syncInfo) {
        this.lastSyncTimestamp = syncInfo.timestamp || 0;
        this.changedChunks = new Set(syncInfo.changedChunks || []);
        this.changedMemories = new Set(syncInfo.changedMemories || []);
        this.deletedMemories = new Set(syncInfo.deletedMemories || []);
      }

      this.isInitialized = true;
      console.log('增量同步服务初始化成功');
    } catch (error) {
      console.error('初始化增量同步服务失败:', error);
      throw error;
    }
  }

  /**
   * 记录块变更
   * @param {string} chunkId - 变更的块ID
   * @returns {Promise<void>}
   */
  async recordChunkChange(chunkId) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    this.changedChunks.add(chunkId);
    await this._saveChangeRecord();
  }

  /**
   * 记录记忆变更
   * @param {string} memoryId - 变更的记忆ID
   * @returns {Promise<void>}
   */
  async recordMemoryChange(memoryId) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    this.changedMemories.add(memoryId);
    await this._saveChangeRecord();
  }

  /**
   * 记录记忆删除
   * @param {string} memoryId - 删除的记忆ID
   * @returns {Promise<void>}
   */
  async recordMemoryDeletion(memoryId) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    this.deletedMemories.add(memoryId);
    // 如果记忆ID在变更集中，移除它
    this.changedMemories.delete(memoryId);
    await this._saveChangeRecord();
  }

  /**
   * 保存变更记录到本地缓存
   * @returns {Promise<void>}
   * @private
   */
  async _saveChangeRecord() {
    const syncInfo = {
      timestamp: Date.now(),
      changedChunks: Array.from(this.changedChunks),
      changedMemories: Array.from(this.changedMemories),
      deletedMemories: Array.from(this.deletedMemories)
    };

    await indexedDBCacheService.setItem('last_sync_info', syncInfo, 'sync');
  }

  /**
   * 执行增量同步
   * @param {Object} options - 同步选项
   * @param {string} [options.conflictStrategy=AUTO_NEWEST] - 冲突解决策略
   * @param {Function} [options.progressCallback] - 进度回调函数
   * @param {Function} [options.conflictCallback] - 冲突回调函数，用于手动解决冲突
   * @returns {Promise<Object>} 同步结果
   */
  async sync(options = {}) {
    if (this.syncInProgress) {
      throw new Error('同步已在进行中，请等待当前同步完成');
    }

    if (!this.isInitialized) {
      await this.initialize();
    }

    this.syncInProgress = true;

    try {
      const {
        conflictStrategy = this.conflictResolutionStrategies.AUTO_NEWEST,
        progressCallback,
        conflictCallback
      } = options;

      // 更新进度
      const updateProgress = (phase, current, total, message) => {
        if (progressCallback) {
          progressCallback({
            phase,
            current,
            total,
            percentage: total > 0 ? Math.floor((current / total) * 100) : 0,
            message
          });
        }
      };

      updateProgress('preparing', 0, 1, '准备同步...');

      // 1. 获取本地和远程元数据
      const localMetadata = await ossStorageService.getUserMetadata(false); // 从本地缓存获取
      updateProgress('preparing', 1, 3, '获取本地元数据完成');

      const remoteMetadata = await ossStorageService.getUserMetadata(true); // 强制从云端获取
      updateProgress('preparing', 2, 3, '获取远程元数据完成');

      // 2. 检查版本冲突
      if (localMetadata.version !== remoteMetadata.version) {
        console.log(`检测到元数据版本不一致: 本地=${localMetadata.version}, 远程=${remoteMetadata.version}`);
        
        // 如果本地版本更高，可能是其他设备未同步我们的更改
        if (localMetadata.version > remoteMetadata.version) {
          console.log('本地版本更高，将上传本地更改');
          // 继续执行上传流程
        } 
        // 如果远程版本更高，需要先合并更改
        else {
          console.log('远程版本更高，需要先合并更改');
          // 执行合并流程
          await this._mergeRemoteChanges(localMetadata, remoteMetadata, {
            conflictStrategy,
            conflictCallback,
            progressCallback: (progress) => updateProgress('merging', progress.current, progress.total, progress.message)
          });
        }
      }

      updateProgress('preparing', 3, 3, '版本检查完成');

      // 3. 上传本地更改
      if (this.changedChunks.size > 0 || this.changedMemories.size > 0 || this.deletedMemories.size > 0) {
        await this._uploadLocalChanges({
          progressCallback: (progress) => updateProgress('uploading', progress.current, progress.total, progress.message)
        });
      } else {
        console.log('没有本地更改需要上传');
      }

      // 4. 更新同步状态
      this.lastSyncTimestamp = Date.now();
      this.changedChunks.clear();
      this.changedMemories.clear();
      this.deletedMemories.clear();
      await this._saveChangeRecord();

      return {
        success: true,
        timestamp: this.lastSyncTimestamp,
        message: '增量同步成功'
      };
    } catch (error) {
      console.error('增量同步失败:', error);
      return {
        success: false,
        message: `增量同步失败: ${error.message}`,
        error
      };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * 合并远程更改
   * @param {Object} localMetadata - 本地元数据
   * @param {Object} remoteMetadata - 远程元数据
   * @param {Object} options - 合并选项
   * @returns {Promise<void>}
   * @private
   */
  async _mergeRemoteChanges(localMetadata, remoteMetadata, options = {}) {
    const {
      conflictStrategy = this.conflictResolutionStrategies.AUTO_NEWEST,
      conflictCallback,
      progressCallback
    } = options;

    // 更新进度
    const updateProgress = (current, total, message) => {
      if (progressCallback) {
        progressCallback({
          current,
          total,
          percentage: total > 0 ? Math.floor((current / total) * 100) : 0,
          message
        });
      }
    };

    // 1. 找出远程新增或修改的块
    const remoteChangedChunks = [];
    
    // 检查元数据中的块信息
    if (remoteMetadata.memory_chunks && localMetadata.memory_chunks) {
      for (let i = 0; i < remoteMetadata.memory_chunks.length; i++) {
        const remoteChunk = remoteMetadata.memory_chunks[i];
        
        // 查找对应的本地块
        const localChunkIndex = localMetadata.memory_chunks.findIndex(chunk => chunk.id === remoteChunk.id);
        
        // 如果本地没有这个块，或者远程块的版本更高，则需要下载
        if (localChunkIndex === -1 || 
            remoteChunk.version > localMetadata.memory_chunks[localChunkIndex].version ||
            new Date(remoteChunk.lastModified) > new Date(localMetadata.memory_chunks[localChunkIndex].lastModified)) {
          remoteChangedChunks.push(remoteChunk.id);
        }
      }
    }

    // 2. 找出远程删除的记忆
    const remoteDeletedMemories = new Set();
    if (remoteMetadata.deleted_memories) {
      Object.keys(remoteMetadata.deleted_memories).forEach(memoryId => {
        // 检查本地是否还有这个记忆
        if (localMetadata.memory_id_to_chunk && localMetadata.memory_id_to_chunk[memoryId]) {
          remoteDeletedMemories.add(memoryId);
        }
      });
    }

    // 3. 下载并合并远程更改的块
    const totalChanges = remoteChangedChunks.length + remoteDeletedMemories.size;
    let processedChanges = 0;

    updateProgress(processedChanges, totalChanges, '开始合并远程更改...');

    // 处理远程更改的块
    for (const chunkId of remoteChangedChunks) {
      updateProgress(processedChanges, totalChanges, `正在下载块 ${chunkId}...`);
      
      // 下载远程块
      const remoteChunk = await ossStorageService.getChunk(chunkId);
      
      // 获取本地块
      let localChunk;
      try {
        localChunk = await ossStorageService.getChunk(chunkId, false); // 从本地缓存获取
      } catch (error) {
        // 如果本地没有这个块，创建一个空块
        localChunk = { memories: {} };
      }
      
      // 合并块中的记忆
      const mergedChunk = await this._mergeChunk(localChunk, remoteChunk, {
        conflictStrategy,
        conflictCallback
      });
      
      // 保存合并后的块
      await ossStorageService.saveChunk(chunkId, mergedChunk);
      
      processedChanges++;
      updateProgress(processedChanges, totalChanges, `块 ${chunkId} 合并完成`);
    }

    // 处理远程删除的记忆
    for (const memoryId of remoteDeletedMemories) {
      updateProgress(processedChanges, totalChanges, `正在处理已删除的记忆 ${memoryId}...`);
      
      // 根据冲突策略决定是否删除本地记忆
      let shouldDelete = true;
      
      if (conflictStrategy === this.conflictResolutionStrategies.AUTO_LOCAL) {
        // 如果策略是优先本地，则不删除本地记忆
        shouldDelete = false;
      } else if (conflictStrategy === this.conflictResolutionStrategies.MANUAL && conflictCallback) {
        // 如果策略是手动解决，调用回调函数
        const decision = await conflictCallback({
          type: 'deletion',
          memoryId,
          remoteDeletedAt: remoteMetadata.deleted_memories[memoryId],
          message: `记忆 ${memoryId} 已在远程删除，是否也在本地删除？`
        });
        
        shouldDelete = decision.action === 'delete';
      }
      
      if (shouldDelete) {
        // 获取记忆所在的块
        const chunkId = localMetadata.memory_id_to_chunk[memoryId];
        if (chunkId) {
          // 从块中删除记忆
          const chunk = await ossStorageService.getChunk(chunkId, false);
          if (chunk && chunk.memories && chunk.memories[memoryId]) {
            delete chunk.memories[memoryId];
            await ossStorageService.saveChunk(chunkId, chunk);
          }
        }
      }
      
      processedChanges++;
      updateProgress(processedChanges, totalChanges, `已删除记忆 ${memoryId} 处理完成`);
    }

    // 4. 更新本地元数据
    // 合并元数据，保留本地的更改记录
    const mergedMetadata = await ossStorageService._mergeMetadata(localMetadata, remoteMetadata);
    
    // 保存合并后的元数据
    await ossStorageService.updateUserMetadata(mergedMetadata);
    
    updateProgress(totalChanges, totalChanges, '远程更改合并完成');
  }

  /**
   * 合并块数据
   * @param {Object} localChunk - 本地块数据
   * @param {Object} remoteChunk - 远程块数据
   * @param {Object} options - 合并选项
   * @returns {Promise<Object>} 合并后的块数据
   * @private
   */
  async _mergeChunk(localChunk, remoteChunk, options = {}) {
    const {
      conflictStrategy = this.conflictResolutionStrategies.AUTO_NEWEST,
      conflictCallback
    } = options;

    // 创建合并后的块
    const mergedChunk = {
      ...remoteChunk,
      memories: { ...localChunk.memories }
    };

    // 处理远程块中的每个记忆
    for (const memoryId in remoteChunk.memories) {
      const remoteMemory = remoteChunk.memories[memoryId];
      const localMemory = localChunk.memories[memoryId];

      // 如果本地没有这个记忆，直接使用远程版本
      if (!localMemory) {
        mergedChunk.memories[memoryId] = remoteMemory;
        continue;
      }

      // 检查是否有冲突
      const hasConflict = this._hasMemoryConflict(localMemory, remoteMemory);

      if (hasConflict) {
        // 根据冲突策略解决冲突
        let resolvedMemory;

        switch (conflictStrategy) {
          case this.conflictResolutionStrategies.AUTO_NEWEST:
            // 使用最新修改的版本
            resolvedMemory = this._getNewerMemory(localMemory, remoteMemory);
            break;

          case this.conflictResolutionStrategies.AUTO_LOCAL:
            // 使用本地版本
            resolvedMemory = localMemory;
            break;

          case this.conflictResolutionStrategies.AUTO_REMOTE:
            // 使用远程版本
            resolvedMemory = remoteMemory;
            break;

          case this.conflictResolutionStrategies.MANUAL:
            // 手动解决冲突
            if (conflictCallback) {
              const decision = await conflictCallback({
                type: 'memory',
                memoryId,
                localMemory,
                remoteMemory,
                message: `记忆 ${memoryId} 存在冲突，请选择要保留的版本`
              });

              if (decision.action === 'use_local') {
                resolvedMemory = localMemory;
              } else if (decision.action === 'use_remote') {
                resolvedMemory = remoteMemory;
              } else if (decision.action === 'use_merged' && decision.mergedMemory) {
                resolvedMemory = decision.mergedMemory;
              } else {
                // 默认使用最新版本
                resolvedMemory = this._getNewerMemory(localMemory, remoteMemory);
              }
            } else {
              // 如果没有提供冲突回调，默认使用最新版本
              resolvedMemory = this._getNewerMemory(localMemory, remoteMemory);
            }
            break;

          default:
            // 默认使用最新版本
            resolvedMemory = this._getNewerMemory(localMemory, remoteMemory);
        }

        // 更新合并后的记忆
        mergedChunk.memories[memoryId] = resolvedMemory;
      } else {
        // 如果远程版本更新，使用远程版本
        if (this._isRemoteMemoryNewer(localMemory, remoteMemory)) {
          mergedChunk.memories[memoryId] = remoteMemory;
        }
        // 否则保留本地版本
      }
    }

    return mergedChunk;
  }

  /**
   * 检查记忆是否有冲突
   * @param {Object} localMemory - 本地记忆
   * @param {Object} remoteMemory - 远程记忆
   * @returns {boolean} 是否有冲突
   * @private
   */
  _hasMemoryConflict(localMemory, remoteMemory) {
    // 如果两边都有版本号，且版本号不同，则可能有冲突
    if (localMemory.version && remoteMemory.version && localMemory.version !== remoteMemory.version) {
      // 如果本地版本更高，但远程的最后修改时间也更新，则有冲突
      if (localMemory.version > remoteMemory.version && 
          new Date(remoteMemory.lastModified) > new Date(localMemory.lastModified)) {
        return true;
      }
      
      // 如果远程版本更高，但本地的最后修改时间也更新，则有冲突
      if (remoteMemory.version > localMemory.version && 
          new Date(localMemory.lastModified) > new Date(remoteMemory.lastModified)) {
        return true;
      }
    }
    
    // 如果最后修改设备不同，且最后修改时间接近（5分钟内），则可能有冲突
    if (localMemory.lastModifiedBy && remoteMemory.lastModifiedBy && 
        localMemory.lastModifiedBy !== remoteMemory.lastModifiedBy) {
      const timeDiff = Math.abs(new Date(localMemory.lastModified) - new Date(remoteMemory.lastModified));
      if (timeDiff < 5 * 60 * 1000) { // 5分钟
        return true;
      }
    }
    
    return false;
  }

  /**
   * 获取更新的记忆版本
   * @param {Object} localMemory - 本地记忆
   * @param {Object} remoteMemory - 远程记忆
   * @returns {Object} 更新的记忆版本
   * @private
   */
  _getNewerMemory(localMemory, remoteMemory) {
    const localTime = new Date(localMemory.lastModified || localMemory.createdAt);
    const remoteTime = new Date(remoteMemory.lastModified || remoteMemory.createdAt);
    
    return localTime > remoteTime ? localMemory : remoteMemory;
  }

  /**
   * 检查远程记忆是否比本地更新
   * @param {Object} localMemory - 本地记忆
   * @param {Object} remoteMemory - 远程记忆
   * @returns {boolean} 远程记忆是否更新
   * @private
   */
  _isRemoteMemoryNewer(localMemory, remoteMemory) {
    // 如果有版本号，比较版本号
    if (localMemory.version && remoteMemory.version) {
      return remoteMemory.version > localMemory.version;
    }
    
    // 否则比较最后修改时间
    const localTime = new Date(localMemory.lastModified || localMemory.createdAt);
    const remoteTime = new Date(remoteMemory.lastModified || remoteMemory.createdAt);
    
    return remoteTime > localTime;
  }

  /**
   * 上传本地更改
   * @param {Object} options - 上传选项
   * @returns {Promise<void>}
   * @private
   */
  async _uploadLocalChanges(options = {}) {
    const { progressCallback } = options;

    // 更新进度
    const updateProgress = (current, total, message) => {
      if (progressCallback) {
        progressCallback({
          current,
          total,
          percentage: total > 0 ? Math.floor((current / total) * 100) : 0,
          message
        });
      }
    };

    // 计算需要上传的总数
    const totalUploads = this.changedChunks.size + this.changedMemories.size + (this.deletedMemories.size > 0 ? 1 : 0);
    let uploadedCount = 0;

    updateProgress(uploadedCount, totalUploads, '准备上传本地更改...');

    // 1. 上传更改的块
    for (const chunkId of this.changedChunks) {
      updateProgress(uploadedCount, totalUploads, `正在上传块 ${chunkId}...`);
      
      try {
        // 获取本地块
        const chunk = await ossStorageService.getChunk(chunkId, false);
        
        // 更新块的版本和最后修改信息
        chunk.version = (chunk.version || 0) + 1;
        chunk.lastModified = new Date().toISOString();
        chunk.lastModifiedBy = storageService.getDeviceId();
        
        // 上传块
        await ossStorageService.saveChunk(chunkId, chunk, true); // 强制上传到云端
        
        uploadedCount++;
        updateProgress(uploadedCount, totalUploads, `块 ${chunkId} 上传完成`);
      } catch (error) {
        console.error(`上传块 ${chunkId} 失败:`, error);
        // 继续处理其他块
      }
    }

    // 2. 上传更改的单个记忆
    for (const memoryId of this.changedMemories) {
      updateProgress(uploadedCount, totalUploads, `正在上传记忆 ${memoryId}...`);
      
      try {
        // 获取元数据
        const metadata = await ossStorageService.getUserMetadata(false);
        
        // 查找记忆所在的块
        const chunkId = metadata.memory_id_to_chunk[memoryId];
        
        if (chunkId) {
          // 获取块
          const chunk = await ossStorageService.getChunk(chunkId, false);
          
          // 检查记忆是否在块中
          if (chunk && chunk.memories && chunk.memories[memoryId]) {
            // 更新记忆的版本和最后修改信息
            const memory = chunk.memories[memoryId];
            memory.version = (memory.version || 0) + 1;
            memory.lastModified = new Date().toISOString();
            memory.lastModifiedBy = storageService.getDeviceId();
            
            // 更新块
            chunk.version = (chunk.version || 0) + 1;
            chunk.lastModified = new Date().toISOString();
            chunk.lastModifiedBy = storageService.getDeviceId();
            
            // 上传块
            await ossStorageService.saveChunk(chunkId, chunk, true); // 强制上传到云端
          }
        }
        
        uploadedCount++;
        updateProgress(uploadedCount, totalUploads, `记忆 ${memoryId} 上传完成`);
      } catch (error) {
        console.error(`上传记忆 ${memoryId} 失败:`, error);
        // 继续处理其他记忆
      }
    }

    // 3. 处理删除的记忆
    if (this.deletedMemories.size > 0) {
      updateProgress(uploadedCount, totalUploads, '正在处理已删除的记忆...');
      
      try {
        // 获取元数据
        const metadata = await ossStorageService.getUserMetadata(false);
        
        // 更新已删除记忆列表
        if (!metadata.deleted_memories) {
          metadata.deleted_memories = {};
        }
        
        // 添加新删除的记忆
        for (const memoryId of this.deletedMemories) {
          metadata.deleted_memories[memoryId] = new Date().toISOString();
          
          // 如果记忆ID在记忆到块的映射中，从映射中移除
          if (metadata.memory_id_to_chunk && metadata.memory_id_to_chunk[memoryId]) {
            delete metadata.memory_id_to_chunk[memoryId];
          }
        }
        
        // 更新元数据
        await ossStorageService.updateUserMetadata(metadata, true); // 强制上传到云端
        
        uploadedCount++;
        updateProgress(uploadedCount, totalUploads, '已删除记忆处理完成');
      } catch (error) {
        console.error('处理已删除记忆失败:', error);
      }
    }

    updateProgress(totalUploads, totalUploads, '本地更改上传完成');
  }
}

// 导出单例实例
const incrementalSyncService = new IncrementalSyncService();
export default incrementalSyncService;
