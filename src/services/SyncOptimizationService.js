/**
 * SyncOptimizationService - 同步优化服务
 * 
 * 用于优化同步过程中的网络传输效率，
 * 包括数据压缩、批量处理和传输优化。
 */
import pako from 'pako'; // 需要添加这个依赖用于压缩

class SyncOptimizationService {
  constructor() {
    this.compressionEnabled = true;
    this.batchSize = 5; // 默认批量处理大小
    this.retryCount = 3; // 默认重试次数
    this.retryDelay = 1000; // 默认重试延迟（毫秒）
    
    // 网络状态监控
    this.networkStatus = {
      online: navigator.onLine,
      type: this._getConnectionType(),
      lastCheckTime: Date.now()
    };
    
    // 监听网络状态变化
    window.addEventListener('online', this._updateNetworkStatus.bind(this));
    window.addEventListener('offline', this._updateNetworkStatus.bind(this));
    
    // 定期检查网络状态
    setInterval(this._updateNetworkStatus.bind(this), 30000); // 每30秒检查一次
  }
  
  /**
   * 更新网络状态
   * @private
   */
  _updateNetworkStatus() {
    this.networkStatus = {
      online: navigator.onLine,
      type: this._getConnectionType(),
      lastCheckTime: Date.now()
    };
    
    console.log('网络状态更新:', this.networkStatus);
  }
  
  /**
   * 获取连接类型
   * @returns {string} 连接类型
   * @private
   */
  _getConnectionType() {
    // 尝试获取网络连接信息
    const connection = navigator.connection || 
                      navigator.mozConnection || 
                      navigator.webkitConnection;
    
    if (connection) {
      return connection.effectiveType || connection.type || 'unknown';
    }
    
    return 'unknown';
  }
  
  /**
   * 压缩数据
   * @param {Object|string} data - 要压缩的数据
   * @returns {Object} 压缩结果
   */
  compressData(data) {
    if (!this.compressionEnabled) {
      return { compressed: false, data };
    }
    
    try {
      // 如果是对象，先转换为JSON字符串
      const jsonString = typeof data === 'string' ? data : JSON.stringify(data);
      
      // 使用pako压缩
      const compressed = pako.deflate(jsonString, { to: 'string' });
      
      // 计算压缩率
      const originalSize = jsonString.length;
      const compressedSize = compressed.length;
      const compressionRatio = (1 - compressedSize / originalSize) * 100;
      
      console.log(`数据压缩: ${originalSize} -> ${compressedSize} 字节 (节省 ${compressionRatio.toFixed(2)}%)`);
      
      return {
        compressed: true,
        data: compressed,
        originalSize,
        compressedSize,
        compressionRatio
      };
    } catch (error) {
      console.error('数据压缩失败:', error);
      return { compressed: false, data };
    }
  }
  
  /**
   * 解压数据
   * @param {Object} compressedData - 压缩的数据对象
   * @returns {Object|string} 解压后的数据
   */
  decompressData(compressedData) {
    if (!compressedData.compressed) {
      return compressedData.data;
    }
    
    try {
      // 使用pako解压
      const decompressed = pako.inflate(compressedData.data, { to: 'string' });
      
      // 如果原始数据是对象，解析JSON
      try {
        return JSON.parse(decompressed);
      } catch (e) {
        // 如果解析失败，返回字符串
        return decompressed;
      }
    } catch (error) {
      console.error('数据解压失败:', error);
      throw error;
    }
  }
  
  /**
   * 批量处理数据
   * @param {Array} items - 要处理的项目数组
   * @param {Function} processFunc - 处理函数
   * @param {Object} options - 选项
   * @returns {Promise<Array>} 处理结果
   */
  async batchProcess(items, processFunc, options = {}) {
    const {
      batchSize = this.batchSize,
      concurrency = 1,
      progressCallback
    } = options;
    
    const results = [];
    const batches = [];
    
    // 将项目分成批次
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    
    // 更新进度
    const updateProgress = (processed) => {
      if (progressCallback) {
        progressCallback({
          total: items.length,
          processed,
          percentage: Math.floor((processed / items.length) * 100)
        });
      }
    };
    
    // 根据并发数处理批次
    if (concurrency <= 1) {
      // 串行处理
      let processed = 0;
      
      for (const batch of batches) {
        const batchResults = await Promise.all(batch.map(item => processFunc(item)));
        results.push(...batchResults);
        
        processed += batch.length;
        updateProgress(processed);
      }
    } else {
      // 并行处理
      let processed = 0;
      let currentBatch = 0;
      
      const processBatch = async () => {
        while (currentBatch < batches.length) {
          const batchIndex = currentBatch++;
          const batch = batches[batchIndex];
          
          const batchResults = await Promise.all(batch.map(item => processFunc(item)));
          results.push(...batchResults);
          
          processed += batch.length;
          updateProgress(processed);
        }
      };
      
      // 创建并发处理任务
      const tasks = [];
      for (let i = 0; i < concurrency; i++) {
        tasks.push(processBatch());
      }
      
      // 等待所有任务完成
      await Promise.all(tasks);
    }
    
    return results;
  }
  
  /**
   * 带重试的异步操作
   * @param {Function} operation - 异步操作函数
   * @param {Object} options - 选项
   * @returns {Promise<any>} 操作结果
   */
  async withRetry(operation, options = {}) {
    const {
      retryCount = this.retryCount,
      retryDelay = this.retryDelay,
      retryCondition = (error) => true, // 默认所有错误都重试
      onRetry = (error, attempt) => {}
    } = options;
    
    let lastError;
    
    for (let attempt = 0; attempt <= retryCount; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        // 检查是否应该重试
        if (attempt < retryCount && retryCondition(error)) {
          // 通知重试回调
          onRetry(error, attempt + 1);
          
          // 计算延迟时间（可以实现指数退避）
          const delay = retryDelay * Math.pow(2, attempt);
          
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          // 达到最大重试次数或不满足重试条件
          break;
        }
      }
    }
    
    // 所有重试都失败
    throw lastError;
  }
  
  /**
   * 根据网络状态调整同步策略
   * @returns {Object} 调整后的同步选项
   */
  adjustSyncOptions() {
    // 更新网络状态
    this._updateNetworkStatus();
    
    const options = {
      compressionEnabled: this.compressionEnabled,
      batchSize: this.batchSize,
      retryCount: this.retryCount,
      retryDelay: this.retryDelay,
      concurrency: 1
    };
    
    // 根据网络状态调整选项
    if (!this.networkStatus.online) {
      // 离线状态，增加重试次数和延迟
      options.retryCount = 5;
      options.retryDelay = 2000;
      return options;
    }
    
    // 根据网络类型调整
    switch (this.networkStatus.type) {
      case '4g':
      case 'wifi':
        // 良好的网络条件，可以增加并发和批量大小
        options.batchSize = 10;
        options.concurrency = 3;
        break;
        
      case '3g':
        // 中等网络条件，使用默认设置
        break;
        
      case '2g':
      case 'slow-2g':
        // 较差的网络条件，减小批量大小，增加重试
        options.batchSize = 3;
        options.retryCount = 4;
        options.retryDelay = 1500;
        break;
        
      default:
        // 未知网络类型，使用默认设置
        break;
    }
    
    return options;
  }
  
  /**
   * 优化数据对象，移除不必要的字段
   * @param {Object} data - 要优化的数据对象
   * @param {Array} fieldsToKeep - 要保留的字段数组
   * @returns {Object} 优化后的数据对象
   */
  optimizeDataObject(data, fieldsToKeep) {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    // 如果是数组，对每个元素进行优化
    if (Array.isArray(data)) {
      return data.map(item => this.optimizeDataObject(item, fieldsToKeep));
    }
    
    // 创建优化后的对象
    const optimized = {};
    
    // 只保留指定的字段
    for (const field of fieldsToKeep) {
      if (data.hasOwnProperty(field)) {
        optimized[field] = data[field];
      }
    }
    
    return optimized;
  }
  
  /**
   * 检测并处理重复数据
   * @param {Array} items - 数据项数组
   * @param {Function} getKey - 获取项目键的函数
   * @returns {Array} 去重后的数组
   */
  deduplicateItems(items, getKey) {
    if (!Array.isArray(items)) {
      return items;
    }
    
    const seen = new Map();
    const result = [];
    
    for (const item of items) {
      const key = getKey(item);
      
      if (!seen.has(key)) {
        seen.set(key, item);
        result.push(item);
      }
    }
    
    return result;
  }
}

// 导出单例实例
const syncOptimizationService = new SyncOptimizationService();
export default syncOptimizationService;
