/**
 * ConflictResolutionService - 冲突解决服务
 * 
 * 用于检测和解决同步过程中的数据冲突，
 * 提供自动和手动冲突解决选项。
 */
import storageService from './StorageService';

class ConflictResolutionService {
  constructor() {
    // 冲突解决策略
    this.strategies = {
      AUTO_NEWEST: 'auto_newest', // 自动选择最新版本
      AUTO_LOCAL: 'auto_local', // 自动选择本地版本
      AUTO_REMOTE: 'auto_remote', // 自动选择远程版本
      MANUAL: 'manual' // 手动解决冲突
    };
    
    // 冲突类型
    this.conflictTypes = {
      MEMORY: 'memory', // 记忆内容冲突
      METADATA: 'metadata', // 元数据冲突
      DELETION: 'deletion', // 删除冲突
      CATEGORY: 'category', // 分类冲突
      TAG: 'tag' // 标签冲突
    };
    
    // 存储待解决的冲突
    this.pendingConflicts = [];
  }
  
  /**
   * 检测记忆冲突
   * @param {Object} localMemory - 本地记忆
   * @param {Object} remoteMemory - 远程记忆
   * @returns {boolean} 是否存在冲突
   */
  detectMemoryConflict(localMemory, remoteMemory) {
    // 如果任一记忆不存在，则没有冲突
    if (!localMemory || !remoteMemory) {
      return false;
    }
    
    // 如果版本号相同，则没有冲突
    if (localMemory.version && remoteMemory.version && localMemory.version === remoteMemory.version) {
      return false;
    }
    
    // 如果最后修改设备相同，则没有冲突
    if (localMemory.lastModifiedBy && remoteMemory.lastModifiedBy && 
        localMemory.lastModifiedBy === remoteMemory.lastModifiedBy) {
      return false;
    }
    
    // 如果内容相同，则没有冲突
    if (JSON.stringify(localMemory.content) === JSON.stringify(remoteMemory.content) &&
        localMemory.title === remoteMemory.title &&
        JSON.stringify(localMemory.tags) === JSON.stringify(remoteMemory.tags) &&
        localMemory.category === remoteMemory.category) {
      return false;
    }
    
    // 检查修改时间差异
    const localTime = new Date(localMemory.lastModified || localMemory.createdAt);
    const remoteTime = new Date(remoteMemory.lastModified || remoteMemory.createdAt);
    const timeDiff = Math.abs(localTime - remoteTime);
    
    // 如果修改时间相差超过1天，且一方的版本明显更高，则可能不是冲突
    if (timeDiff > 24 * 60 * 60 * 1000) {
      // 如果版本号相差2以上，则认为高版本是有意的更新，不是冲突
      if (localMemory.version && remoteMemory.version && 
          Math.abs(localMemory.version - remoteMemory.version) > 2) {
        return false;
      }
    }
    
    // 其他情况视为冲突
    return true;
  }
  
  /**
   * 检测元数据冲突
   * @param {Object} localMetadata - 本地元数据
   * @param {Object} remoteMetadata - 远程元数据
   * @returns {Object} 冲突信息
   */
  detectMetadataConflicts(localMetadata, remoteMetadata) {
    const conflicts = {
      hasConflicts: false,
      categories: false,
      tags: false,
      settings: false,
      details: []
    };
    
    // 检查分类冲突
    if (localMetadata.categories && remoteMetadata.categories) {
      const localCategories = new Map(localMetadata.categories.map(c => [c.id, c]));
      const remoteCategories = new Map(remoteMetadata.categories.map(c => [c.id, c]));
      
      // 检查本地修改的分类
      for (const [id, localCategory] of localCategories.entries()) {
        const remoteCategory = remoteCategories.get(id);
        
        // 如果远程没有这个分类，或者名称不同，则有冲突
        if (!remoteCategory || localCategory.name !== remoteCategory.name) {
          conflicts.hasConflicts = true;
          conflicts.categories = true;
          conflicts.details.push({
            type: this.conflictTypes.CATEGORY,
            id,
            local: localCategory,
            remote: remoteCategory,
            message: `分类 "${localCategory.name}" (ID: ${id}) 在本地和远程不一致`
          });
        }
      }
      
      // 检查远程有但本地没有的分类
      for (const [id, remoteCategory] of remoteCategories.entries()) {
        if (!localCategories.has(id)) {
          conflicts.hasConflicts = true;
          conflicts.categories = true;
          conflicts.details.push({
            type: this.conflictTypes.CATEGORY,
            id,
            local: null,
            remote: remoteCategory,
            message: `远程有新分类 "${remoteCategory.name}" (ID: ${id})，本地不存在`
          });
        }
      }
    }
    
    // 检查标签冲突（如果有标签元数据）
    if (localMetadata.tags && remoteMetadata.tags) {
      // 实现标签冲突检测逻辑
      // ...
    }
    
    return conflicts;
  }
  
  /**
   * 解决记忆冲突
   * @param {Object} localMemory - 本地记忆
   * @param {Object} remoteMemory - 远程记忆
   * @param {string} strategy - 冲突解决策略
   * @returns {Object} 解决后的记忆
   */
  resolveMemoryConflict(localMemory, remoteMemory, strategy = this.strategies.AUTO_NEWEST) {
    switch (strategy) {
      case this.strategies.AUTO_NEWEST:
        return this._resolveByNewest(localMemory, remoteMemory);
        
      case this.strategies.AUTO_LOCAL:
        return localMemory;
        
      case this.strategies.AUTO_REMOTE:
        return remoteMemory;
        
      case this.strategies.MANUAL:
        // 手动解决需要外部处理，这里只返回合并的记忆
        return this._createMergedMemory(localMemory, remoteMemory);
        
      default:
        return this._resolveByNewest(localMemory, remoteMemory);
    }
  }
  
  /**
   * 根据最新修改时间解决冲突
   * @param {Object} localMemory - 本地记忆
   * @param {Object} remoteMemory - 远程记忆
   * @returns {Object} 解决后的记忆
   * @private
   */
  _resolveByNewest(localMemory, remoteMemory) {
    const localTime = new Date(localMemory.lastModified || localMemory.createdAt);
    const remoteTime = new Date(remoteMemory.lastModified || remoteMemory.createdAt);
    
    // 如果时间相同，优先使用版本号更高的
    if (localTime.getTime() === remoteTime.getTime()) {
      if (localMemory.version && remoteMemory.version) {
        return localMemory.version >= remoteMemory.version ? localMemory : remoteMemory;
      }
    }
    
    return localTime >= remoteTime ? localMemory : remoteMemory;
  }
  
  /**
   * 创建合并的记忆
   * @param {Object} localMemory - 本地记忆
   * @param {Object} remoteMemory - 远程记忆
   * @returns {Object} 合并后的记忆
   * @private
   */
  _createMergedMemory(localMemory, remoteMemory) {
    // 获取更新的基本信息
    const newerMemory = this._resolveByNewest(localMemory, remoteMemory);
    const olderMemory = newerMemory === localMemory ? remoteMemory : localMemory;
    
    // 创建合并的记忆
    const mergedMemory = { ...newerMemory };
    
    // 合并标题（如果不同，使用更新的版本）
    if (localMemory.title !== remoteMemory.title) {
      mergedMemory.title = newerMemory.title;
    }
    
    // 合并内容（如果不同，可能需要特殊处理）
    if (JSON.stringify(localMemory.content) !== JSON.stringify(remoteMemory.content)) {
      // 这里可以实现更复杂的内容合并逻辑
      // 例如，如果内容是文本，可以尝试合并文本差异
      // 如果是结构化数据，可以尝试合并字段
      
      // 简单实现：使用更新的内容，但在内容中添加冲突标记
      mergedMemory.content = newerMemory.content;
      
      // 如果内容是字符串，添加冲突标记
      if (typeof newerMemory.content === 'string' && typeof olderMemory.content === 'string') {
        mergedMemory.content = `${newerMemory.content}\n\n--- 冲突内容 ---\n${olderMemory.content}\n--- 冲突结束 ---`;
      }
    }
    
    // 合并标签（合并两边的标签，去重）
    if (localMemory.tags || remoteMemory.tags) {
      const localTags = localMemory.tags || [];
      const remoteTags = remoteMemory.tags || [];
      mergedMemory.tags = [...new Set([...localTags, ...remoteTags])];
    }
    
    // 更新版本和修改信息
    mergedMemory.version = Math.max(localMemory.version || 0, remoteMemory.version || 0) + 1;
    mergedMemory.lastModified = new Date().toISOString();
    mergedMemory.lastModifiedBy = storageService.getDeviceId();
    mergedMemory.mergedFrom = {
      local: {
        version: localMemory.version,
        lastModified: localMemory.lastModified,
        lastModifiedBy: localMemory.lastModifiedBy
      },
      remote: {
        version: remoteMemory.version,
        lastModified: remoteMemory.lastModified,
        lastModifiedBy: remoteMemory.lastModifiedBy
      }
    };
    
    return mergedMemory;
  }
  
  /**
   * 添加待解决的冲突
   * @param {Object} conflict - 冲突信息
   */
  addPendingConflict(conflict) {
    this.pendingConflicts.push({
      ...conflict,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
      createdAt: new Date().toISOString()
    });
  }
  
  /**
   * 获取所有待解决的冲突
   * @returns {Array} 冲突列表
   */
  getPendingConflicts() {
    return [...this.pendingConflicts];
  }
  
  /**
   * 解决指定的冲突
   * @param {string} conflictId - 冲突ID
   * @param {Object} resolution - 解决方案
   */
  resolveConflict(conflictId, resolution) {
    const index = this.pendingConflicts.findIndex(c => c.id === conflictId);
    if (index !== -1) {
      // 移除已解决的冲突
      this.pendingConflicts.splice(index, 1);
      return true;
    }
    return false;
  }
  
  /**
   * 清除所有待解决的冲突
   */
  clearPendingConflicts() {
    this.pendingConflicts = [];
  }
}

// 导出单例实例
const conflictResolutionService = new ConflictResolutionService();
export default conflictResolutionService;
