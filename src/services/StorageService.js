/**
 * StorageService - 抽象存储服务类
 *
 * 用于管理本地设置、云存储设置、加密算法和密钥等，
 * 并提供与云存储交互的功能。
 */
import localCacheService from './LocalCacheService';
import indexedDBCacheService from './IndexedDBCacheService';

class StorageService {
  constructor() {
    // 本地缓存服务
    this.localCache = localCacheService;
    // IndexedDB缓存服务
    this.dbCache = indexedDBCacheService;
    // 是否使用IndexedDB缓存
    this.useIndexedDB = true;
    // 锁定机制
    this._locks = {};
    this._lockTimeout = 30000; // 锁定超时时间（毫秒）
    this._deviceId = this._getDeviceIdSync(); // 先使用同步方式获取临时ID
    // 异步初始化设备ID
    this._initDeviceId();
    // 默认设置
    this.defaultSettings = {
      // 分类设置
      categories: [
        { id: '1', name: '工作', color: '#1890ff', icon: 'BulbOutlined' },
        { id: '2', name: '生活', color: '#52c41a', icon: 'HeartOutlined' },
        { id: '3', name: '学习', color: '#722ed1', icon: 'BookOutlined' },
        { id: '4', name: '其他', color: '#faad14', icon: 'StarOutlined' },
      ],
      defaultCategory: '4',

      // 云存储设置
      huaweiObs: {
        accessKeyId: '',
        secretAccessKey: '',
        region: 'cn-north-4',
        endpoint: 'obs.cn-north-4.myhuaweicloud.com',
        bucketName: ''
      },
      minio: {
        accessKey: '',
        secretKey: '',
        endPoint: 'play.min.io',
        port: 9000,
        useSSL: true,
        bucketName: ''
      },

      // 加密设置
      encryption: false,
      encryptionAlgorithm: 'AES-256-GCM',
      encryptionKey: '',
      autoLock: false,
      lockTimeout: 15,

      // 外观设置
      darkMode: false,
      theme: 'default',
      fontSize: 14,
      primaryColor: '#1890ff',
      compactMode: false,

      // 并发控制设置
      dataVersion: 1,
      lastModified: new Date().toISOString(),
      lastModifiedBy: '',
    };

    // 初始化
    this.initialize();
  }

  /**
   * 异步初始化设备ID
   * @private
   */
  async _initDeviceId() {
    try {
      // 异步获取设备ID
      const deviceId = await this._generateDeviceId();
      // 更新设备ID
      this._deviceId = deviceId;
      console.log(`设备ID初始化完成: ${deviceId}`);
    } catch (error) {
      console.error('初始化设备ID失败:', error);
    }
  }

  /**
   * 初始化存储服务
   */
  async initialize() {
    try {
      // 初始化IndexedDB缓存
      if (this.useIndexedDB) {
        await this.dbCache.initialize();
        console.log('IndexedDB缓存服务初始化成功');
      }

      // 等待设备ID初始化完成
      await this.getDeviceIdAsync();

      // 检查是否已经初始化
      const settings = await this.getSettings();
      if (!settings) {
        // 如果没有设置，初始化默认设置
        const initialSettings = { ...this.defaultSettings, lastModifiedBy: this._deviceId };
        await this.saveSettings(initialSettings);
      } else if (!settings.dataVersion) {
        // 如果没有版本号，添加版本号
        settings.dataVersion = 1;
        settings.lastModified = new Date().toISOString();
        settings.lastModifiedBy = this._deviceId;
        await this.saveSettings(settings);
      }

      // 检查缓存设置
      if (!settings.cacheSettings) {
        // 初始化缓存设置
        settings.cacheSettings = {
          useIndexedDB: true,
          maxCacheSize: 500, // MB
          prefetchEnabled: true,
          compressionEnabled: true,
          cacheExpirationTimes: {
            memory: 7, // 天
            chunk: 3, // 天
            metadata: 1, // 小时
            index: 12, // 小时
            image: 30, // 天
            search_index: 7 // 天
          }
        };
        await this.saveSettings(settings);
      }

      // 应用缓存设置
      this.useIndexedDB = settings.cacheSettings.useIndexedDB;

      // 设置IndexedDB缓存过期时间
      if (this.useIndexedDB && settings.cacheSettings.cacheExpirationTimes) {
        const times = settings.cacheSettings.cacheExpirationTimes;
        const config = this.dbCache.config.expirationTimes;

        // 将天和小时转换为毫秒
        if (times.memory) config.memory = times.memory * 24 * 60 * 60 * 1000;
        if (times.chunk) config.chunk = times.chunk * 24 * 60 * 60 * 1000;
        if (times.metadata) config.metadata = times.metadata * 60 * 60 * 1000;
        if (times.index) config.index = times.index * 60 * 60 * 1000;
        if (times.image) config.image = times.image * 24 * 60 * 60 * 1000;
        if (times.search_index) config.search_index = times.search_index * 24 * 60 * 60 * 1000;
      }

      console.log('存储服务初始化成功，缓存模式:', this.useIndexedDB ? 'IndexedDB' : 'LocalStorage');
    } catch (error) {
      console.error('初始化存储服务失败:', error);
    }
  }

  /**
   * 生成设备ID
   * @returns {string} 设备ID
   * @private
   */
  _generateDeviceId() {
    // 尝试从多个存储位置获取设备ID
    // 1. 先从 localStorage 获取
    const localStorageId = localStorage.getItem('memory_keeper_device_id');
    if (localStorageId) {
      return localStorageId;
    }

    // 2. 尝试从 chrome.storage.local 获取
    return new Promise((resolve) => {
      chrome.storage.local.get(['device_id'], async (result) => {
        if (result.device_id) {
          // 如果在 chrome.storage 中找到，同步到 localStorage
          localStorage.setItem('memory_keeper_device_id', result.device_id);
          return resolve(result.device_id);
        }

        // 3. 如果都没有，生成新的设备ID
        // 生成平台类型前缀
        let platformPrefix = 'chrome_';

        // 检测平台类型
        if (navigator.userAgent.indexOf('Firefox') !== -1) {
          platformPrefix = 'firefox_';
        } else if (navigator.userAgent.indexOf('Safari') !== -1 && navigator.userAgent.indexOf('Chrome') === -1) {
          platformPrefix = 'safari_';
        } else if (navigator.userAgent.indexOf('Edg') !== -1) {
          platformPrefix = 'edge_';
        } else if (navigator.userAgent.indexOf('OPR') !== -1 || navigator.userAgent.indexOf('Opera') !== -1) {
          platformPrefix = 'opera_';
        }

        // 生成更稳定的设备标识符
        // 使用硬件信息和浏览器指纹组合生成相对稳定的ID
        let deviceFingerprint = '';

        // 收集硬件信息
        const screenInfo = `${screen.width}x${screen.height}x${screen.colorDepth}`;
        const timeZoneOffset = new Date().getTimezoneOffset();
        const language = navigator.language;
        const platform = navigator.platform;

        // 组合信息生成指纹
        const baseInfo = `${screenInfo}|${timeZoneOffset}|${language}|${platform}|${navigator.userAgent}`;

        // 使用简单的哈希函数生成指纹
        let hash = 0;
        for (let i = 0; i < baseInfo.length; i++) {
          const char = baseInfo.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // Convert to 32bit integer
        }
        deviceFingerprint = Math.abs(hash).toString(36);

        // 生成带平台前缀的设备ID
        const newId = `${platformPrefix}${deviceFingerprint}`;

        // 存储设备ID到多个位置
        localStorage.setItem('memory_keeper_device_id', newId);
        chrome.storage.local.set({ device_id: newId });

        console.log(`生成新的设备ID: ${newId}`);
        resolve(newId);
      });
    });
  }

  /**
   * 获取设备ID（同步版本）
   * @returns {string} 设备ID
   * @private
   */
  _getDeviceIdSync() {
    // 尝试从 localStorage 获取
    const localStorageId = localStorage.getItem('memory_keeper_device_id');
    if (localStorageId) {
      return localStorageId;
    }

    // 如果没有，生成一个临时ID
    // 注意：这个临时ID将在异步初始化完成后被替换
    const tempId = 'temp_' + Date.now().toString(36);
    return tempId;
  }

  /**
   * 获取设备ID
   * @returns {string} 设备ID
   */
  getDeviceId() {
    return this._deviceId || this._getDeviceIdSync();
  }

  /**
   * 异步获取设备ID
   * @returns {Promise<string>} 设备ID
   */
  async getDeviceIdAsync() {
    if (!this._deviceId) {
      this._deviceId = await this._generateDeviceId();
    }
    return this._deviceId;
  }

  /**
   * 尝试获取资源锁
   * @param {string} resourceName - 资源名称
   * @param {number} [timeout=5000] - 超时时间（毫秒）
   * @returns {Promise<boolean>} 是否成功获取锁
   */
  async acquireLock(resourceName, timeout = 5000) {
    console.log(`尝试获取资源锁: ${resourceName}`);

    // 检查锁是否已存在
    const existingLock = await this._getLock(resourceName);

    if (existingLock) {
      // 检查锁是否过期
      const now = Date.now();
      if (now - existingLock.timestamp < this._lockTimeout) {
        // 如果锁是由当前设备持有，则视为成功
        if (existingLock.deviceId === this._deviceId) {
          console.log(`资源 ${resourceName} 已被当前设备锁定，重用现有锁`);
          return true;
        }

        // 锁未过期且不是由当前设备持有，等待超时或锁释放
        console.log(`资源 ${resourceName} 已被设备 ${existingLock.deviceId} 锁定，等待...`);

        // 等待指定的超时时间
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
          await new Promise(resolve => setTimeout(resolve, 500)); // 等待500毫秒

          // 重新检查锁
          const currentLock = await this._getLock(resourceName);

          // 如果锁已释放或已过期
          if (!currentLock || Date.now() - currentLock.timestamp >= this._lockTimeout) {
            break;
          }
        }

        // 再次尝试获取锁
        const retryLock = await this._getLock(resourceName);
        if (retryLock && Date.now() - retryLock.timestamp < this._lockTimeout && retryLock.deviceId !== this._deviceId) {
          console.log(`无法获取资源 ${resourceName} 的锁，超时`);
          return false;
        }
      } else {
        console.log(`资源 ${resourceName} 的锁已过期，将被覆盖`);
      }
    }

    // 创建新锁
    const lock = {
      deviceId: this._deviceId,
      timestamp: Date.now()
    };

    // 保存锁
    await this._setLock(resourceName, lock);
    console.log(`成功获取资源 ${resourceName} 的锁`);
    return true;
  }

  /**
   * 释放资源锁
   * @param {string} resourceName - 资源名称
   * @returns {Promise<boolean>} 是否成功释放锁
   */
  async releaseLock(resourceName) {
    console.log(`释放资源锁: ${resourceName}`);

    // 检查锁是否存在且由当前设备持有
    const existingLock = await this._getLock(resourceName);

    if (existingLock && existingLock.deviceId === this._deviceId) {
      // 删除锁
      await this._removeLock(resourceName);
      console.log(`成功释放资源 ${resourceName} 的锁`);
      return true;
    }

    console.log(`无法释放资源 ${resourceName} 的锁，锁不存在或不是由当前设备持有`);
    return false;
  }

  /**
   * 获取锁信息
   * @param {string} resourceName - 资源名称
   * @returns {Promise<Object|null>} 锁信息
   * @private
   */
  async _getLock(resourceName) {
    return new Promise((resolve) => {
      const lockKey = `memory_keeper_lock_${resourceName}`;
      chrome.storage.local.get([lockKey], (result) => {
        if (chrome.runtime.lastError || !result[lockKey]) {
          resolve(null);
        } else {
          resolve(result[lockKey]);
        }
      });
    });
  }

  /**
   * 设置锁信息
   * @param {string} resourceName - 资源名称
   * @param {Object} lock - 锁信息
   * @returns {Promise<void>}
   * @private
   */
  async _setLock(resourceName, lock) {
    return new Promise((resolve, reject) => {
      const lockKey = `memory_keeper_lock_${resourceName}`;
      const data = {};
      data[lockKey] = lock;

      chrome.storage.local.set(data, () => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve();
        }
      });
    });
  }

  /**
   * 删除锁信息
   * @param {string} resourceName - 资源名称
   * @returns {Promise<void>}
   * @private
   */
  async _removeLock(resourceName) {
    return new Promise((resolve, reject) => {
      const lockKey = `memory_keeper_lock_${resourceName}`;

      chrome.storage.local.remove(lockKey, () => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve();
        }
      });
    });
  }

  /**
   * 获取所有设置
   * @returns {Promise<Object>} 设置对象
   */
  getSettings() {
    return new Promise((resolve, reject) => {
      // 使用本地存储而不是同步存储，避免大小限制
      chrome.storage.local.get(['settings'], (result) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(result.settings || this.defaultSettings);
        }
      });
    });
  }

  /**
   * 保存所有设置
   * @param {Object} settings - 要保存的设置对象
   * @param {boolean} [useLock=true] - 是否使用锁机制
   * @returns {Promise<void>}
   */
  async saveSettings(settings, useLock = true) {
    // 如果需要使用锁机制，先获取锁
    if (useLock) {
      const lockAcquired = await this.acquireLock('settings', 10000); // 尝试获取锁，最多等待10秒
      if (!lockAcquired) {
        throw new Error('无法获取设置数据锁，可能有其他设备正在修改数据');
      }
    }

    try {
      // 更新版本信息
      if (!settings.dataVersion) {
        settings.dataVersion = 1;
      } else {
        settings.dataVersion++;
      }

      settings.lastModified = new Date().toISOString();
      settings.lastModifiedBy = this._deviceId;

      // 添加日志，记录设置大小
      const settingsStr = JSON.stringify({ settings });
      console.log(`保存设置，大小：${settingsStr.length} 字节，版本：${settings.dataVersion}`);
      console.log('设置内容：', settings);

      // 检查分类数量
      if (settings.categories) {
        console.log(`分类数量：${settings.categories.length}`);
      }

      // 如果设置过大，可能会超出 Chrome 存储限制
      if (settingsStr.length > 8000) {
        console.warn('警告：设置大小接近 Chrome 存储限制 (8KB/项)');
      }

      // 保存设置
      await new Promise((resolve, reject) => {
        chrome.storage.local.set({ settings }, (result) => {
          if (chrome.runtime.lastError) {
            console.error('保存设置失败：', chrome.runtime.lastError);
            reject(chrome.runtime.lastError);
          } else {
            console.log('设置保存成功');
            resolve();
          }
        });
      });
    } finally {
      // 如果使用了锁机制，释放锁
      if (useLock) {
        await this.releaseLock('settings');
      }
    }
  }

  /**
   * 获取特定设置项
   * @param {string} key - 设置项的键
   * @returns {Promise<any>} 设置项的值
   */
  async getSetting(key) {
    const settings = await this.getSettings();
    return settings[key];
  }

  /**
   * 更新特定设置项
   * @param {string} key - 设置项的键
   * @param {any} value - 设置项的新值
   * @returns {Promise<void>}
   */
  async updateSetting(key, value) {
    // 尝试获取锁
    const lockAcquired = await this.acquireLock('settings', 10000);
    if (!lockAcquired) {
      throw new Error('无法获取设置数据锁，请稍后再试');
    }

    try {
      const settings = await this.getSettings();
      settings[key] = value;
      // 不使用锁保存，因为已经获取了锁
      await this.saveSettings(settings, false);
    } finally {
      // 释放锁
      await this.releaseLock('settings');
    }
  }

  /**
   * 检查用户信息是否已设置
   * @returns {Promise<boolean>} 用户信息是否已设置
   */
  async isUserInfoSet() {
    const settings = await this.getSettings();
    return !!(settings.userId && settings.userInfo && settings.userInfo.username);
  }

  /**
   * 获取缓存项
   * @param {string} key - 缓存键
   * @param {string} [type='default'] - 缓存类型
   * @returns {Promise<any>} 缓存值，如果不存在则返回null
   */
  async getCacheItem(key, type = 'default') {
    if (this.useIndexedDB) {
      return this.dbCache.getItem(key, type);
    } else {
      return this.localCache.getItem(key);
    }
  }

  /**
   * 设置缓存项
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {string} [type='default'] - 缓存类型
   * @returns {Promise<void>}
   */
  async setCacheItem(key, value, type = 'default') {
    if (this.useIndexedDB) {
      return this.dbCache.setItem(key, value, type);
    } else {
      return this.localCache.setItem(key, value);
    }
  }

  /**
   * 移除缓存项
   * @param {string} key - 缓存键
   * @param {string} [type='default'] - 缓存类型
   * @returns {Promise<void>}
   */
  async removeCacheItem(key, type = 'default') {
    if (this.useIndexedDB) {
      return this.dbCache.removeItem(key, type);
    } else {
      return this.localCache.removeItem(key);
    }
  }

  /**
   * 清空缓存
   * @returns {Promise<void>}
   */
  async clearCache() {
    if (this.useIndexedDB) {
      await this.dbCache.clear();
    }
    await this.localCache.clear();
    console.log('缓存已清空');
  }

  /**
   * 获取缓存统计信息
   * @returns {Promise<Object>} 缓存统计信息
   */
  async getCacheStats() {
    if (this.useIndexedDB) {
      return this.dbCache.getStats();
    } else {
      // 简单的LocalStorage统计
      const stats = {
        total: 0,
        byType: {}
      };

      // 遍历localStorage
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key.startsWith('memory_keeper_')) {
          stats.total++;

          // 尝试提取类型
          const typeParts = key.split('_');
          if (typeParts.length > 2) {
            const type = typeParts[2];
            if (!stats.byType[type]) {
              stats.byType[type] = { count: 0 };
            }
            stats.byType[type].count++;
          }
        }
      }

      return stats;
    }
  }

  /**
   * 更新缓存设置
   * @param {Object} cacheSettings - 缓存设置
   * @returns {Promise<void>}
   */
  async updateCacheSettings(cacheSettings) {
    // 获取当前设置
    const settings = await this.getSettings();

    // 更新缓存设置
    settings.cacheSettings = { ...settings.cacheSettings, ...cacheSettings };

    // 保存设置
    await this.saveSettings(settings);

    // 应用新设置
    this.useIndexedDB = settings.cacheSettings.useIndexedDB;

    // 如果更新了过期时间，应用到IndexedDB缓存
    if (this.useIndexedDB && settings.cacheSettings.cacheExpirationTimes) {
      const times = settings.cacheSettings.cacheExpirationTimes;
      const config = this.dbCache.config.expirationTimes;

      // 将天和小时转换为毫秒
      if (times.memory) config.memory = times.memory * 24 * 60 * 60 * 1000;
      if (times.chunk) config.chunk = times.chunk * 24 * 60 * 60 * 1000;
      if (times.metadata) config.metadata = times.metadata * 60 * 60 * 1000;
      if (times.index) config.index = times.index * 60 * 60 * 1000;
      if (times.image) config.image = times.image * 24 * 60 * 60 * 1000;
      if (times.search_index) config.search_index = times.search_index * 24 * 60 * 60 * 1000;
    }

    console.log('缓存设置已更新');
  }

  /**
   * 获取所有记忆
   * @returns {Promise<Array>} 记忆数组
   */
  getMemories() {
    return new Promise((resolve, reject) => {
      chrome.storage.local.get(['memories'], (result) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(result.memories || []);
        }
      });
    });
  }

  /**
   * 保存所有记忆
   * @param {Array} memories - 要保存的记忆数组
   * @param {boolean} [useLock=true] - 是否使用锁机制
   * @returns {Promise<void>}
   */
  async saveMemories(memories, useLock = true) {
    // 如果需要使用锁机制，先获取锁
    if (useLock) {
      const lockAcquired = await this.acquireLock('memories', 10000); // 尝试获取锁，最多等待10秒
      if (!lockAcquired) {
        throw new Error('无法获取记忆数据锁，可能有其他设备正在修改数据');
      }
    }

    try {
      // 获取当前设置，更新版本号
      const settings = await this.getSettings();
      settings.dataVersion = (settings.dataVersion || 0) + 1;
      settings.lastModified = new Date().toISOString();
      settings.lastModifiedBy = this._deviceId;

      // 添加日志，记录记忆大小
      const memoriesStr = JSON.stringify({ memories });
      console.log(`保存记忆，数量：${memories.length}，大小：${memoriesStr.length} 字节，版本：${settings.dataVersion}`);

      // 使用事务模式保存数据
      await new Promise((resolve, reject) => {
        chrome.storage.local.set({
          memories,
          settings
        }, () => {
          if (chrome.runtime.lastError) {
            console.error('保存记忆失败：', chrome.runtime.lastError);
            reject(chrome.runtime.lastError);
          } else {
            console.log('记忆保存成功');
            resolve();
          }
        });
      });
    } finally {
      // 如果使用了锁机制，释放锁
      if (useLock) {
        await this.releaseLock('memories');
      }
    }
  }

  /**
   * 添加新记忆
   * @param {Object} memory - 要添加的记忆对象
   * @returns {Promise<Object>} 添加后的记忆对象（包含ID）
   */
  async addMemory(memory) {
    // 尝试获取锁
    const lockAcquired = await this.acquireLock('memories', 10000);
    if (!lockAcquired) {
      throw new Error('无法获取记忆数据锁，请稍后再试');
    }

    try {
      const memories = await this.getMemories();

      // 创建新记忆对象
      const newMemory = {
        id: Date.now().toString(),
        title: memory.title || '无标题记忆',
        content: memory.content,
        category: memory.category || (await this.getSetting('defaultCategory')) || '',
        createdAt: new Date().toISOString(),
        url: memory.url || '',
        tags: memory.tags || [],
        version: 1,
        lastModified: new Date().toISOString(),
        lastModifiedBy: this._deviceId
      };

      // 添加到记忆数组
      const updatedMemories = [...memories, newMemory];

      // 保存更新后的记忆数组（不使用锁，因为已经获取了锁）
      await this.saveMemories(updatedMemories, false);

      return newMemory;
    } finally {
      // 释放锁
      await this.releaseLock('memories');
    }
  }

  /**
   * 更新记忆
   * @param {string} id - 记忆ID
   * @param {Object} updates - 要更新的字段
   * @returns {Promise<Object>} 更新后的记忆对象
   */
  async updateMemory(id, updates) {
    // 尝试获取锁
    const lockAcquired = await this.acquireLock('memories', 10000);
    if (!lockAcquired) {
      throw new Error('无法获取记忆数据锁，请稍后再试');
    }

    try {
      const memories = await this.getMemories();

      // 查找要更新的记忆
      const memoryToUpdate = memories.find(memory => memory.id === id);

      if (!memoryToUpdate) {
        throw new Error(`找不到ID为 ${id} 的记忆`);
      }

      // 更新记忆并增加版本号
      const updatedMemory = {
        ...memoryToUpdate,
        ...updates,
        version: (memoryToUpdate.version || 0) + 1,
        lastModified: new Date().toISOString(),
        lastModifiedBy: this._deviceId
      };

      // 更新记忆数组
      const updatedMemories = memories.map(memory => {
        if (memory.id === id) {
          return updatedMemory;
        }
        return memory;
      });

      // 保存更新后的记忆数组（不使用锁，因为已经获取了锁）
      await this.saveMemories(updatedMemories, false);

      // 返回更新后的记忆对象
      return updatedMemory;
    } finally {
      // 释放锁
      await this.releaseLock('memories');
    }
  }

  /**
   * 删除记忆
   * @param {string} id - 记忆ID
   * @returns {Promise<void>}
   */
  async deleteMemory(id) {
    // 尝试获取锁
    const lockAcquired = await this.acquireLock('memories', 10000);
    if (!lockAcquired) {
      throw new Error('无法获取记忆数据锁，请稍后再试');
    }

    try {
      const memories = await this.getMemories();

      // 检查记忆是否存在
      const memoryExists = memories.some(memory => memory.id === id);
      if (!memoryExists) {
        console.warn(`要删除的记忆 ${id} 不存在`);
        return;
      }

      // 过滤掉要删除的记忆
      const updatedMemories = memories.filter(memory => memory.id !== id);

      // 保存更新后的记忆数组（不使用锁，因为已经获取了锁）
      await this.saveMemories(updatedMemories, false);

      // 记录删除操作
      const settings = await this.getSettings();
      if (!settings.deletedMemories) {
        settings.deletedMemories = {};
      }

      // 添加到已删除记忆列表
      settings.deletedMemories[id] = {
        deletedAt: new Date().toISOString(),
        deletedBy: this._deviceId
      };

      // 保存设置
      await this.saveSettings(settings, false);
    } finally {
      // 释放锁
      await this.releaseLock('memories');
    }
  }

  /**
   * 导出所有数据
   * @returns {Promise<Object>} 导出的数据对象
   */
  async exportData() {
    const [memories, settings] = await Promise.all([
      this.getMemories(),
      this.getSettings()
    ]);

    return {
      memories,
      settings,
      exportDate: new Date().toISOString(),
      version: '1.0.0'
    };
  }

  /**
   * 导入数据
   * @param {Object} data - 要导入的数据对象
   * @returns {Promise<void>}
   */
  async importData(data) {
    // 验证数据格式
    if (!data.memories || !data.settings || !data.version) {
      throw new Error('无效的数据格式');
    }

    // 尝试获取所有锁
    const settingsLockAcquired = await this.acquireLock('settings', 10000);
    if (!settingsLockAcquired) {
      throw new Error('无法获取设置数据锁，请稍后再试');
    }

    const memoriesLockAcquired = await this.acquireLock('memories', 10000);
    if (!memoriesLockAcquired) {
      // 如果无法获取记忆锁，释放设置锁
      await this.releaseLock('settings');
      throw new Error('无法获取记忆数据锁，请稍后再试');
    }

    try {
      // 更新数据版本和设备信息
      data.settings.dataVersion = (data.settings.dataVersion || 0) + 1;
      data.settings.lastModified = new Date().toISOString();
      data.settings.lastModifiedBy = this._deviceId;
      data.settings.importedAt = new Date().toISOString();

      // 为所有记忆添加版本信息（如果没有）
      const updatedMemories = data.memories.map(memory => {
        if (!memory.version) {
          return {
            ...memory,
            version: 1,
            lastModified: new Date().toISOString(),
            lastModifiedBy: this._deviceId
          };
        }
        return memory;
      });

      // 保存导入的数据（不使用锁，因为已经获取了锁）
      await this.saveSettings(data.settings, false);
      await this.saveMemories(updatedMemories, false);

      console.log('数据导入成功，共导入记忆 ' + updatedMemories.length + ' 条');
    } finally {
      // 释放所有锁
      await this.releaseLock('memories');
      await this.releaseLock('settings');
    }
  }

  /**
   * 清除所有数据
   * @param {boolean} keepSettings - 是否保留设置
   * @returns {Promise<Object>} 备份的数据对象
   */
  async clearData(keepSettings = false) {
    // 尝试获取所有锁
    const memoriesLockAcquired = await this.acquireLock('memories', 10000);
    if (!memoriesLockAcquired) {
      throw new Error('无法获取记忆数据锁，请稍后再试');
    }

    let settingsLockAcquired = true;
    if (!keepSettings) {
      settingsLockAcquired = await this.acquireLock('settings', 10000);
      if (!settingsLockAcquired) {
        // 如果无法获取设置锁，释放记忆锁
        await this.releaseLock('memories');
        throw new Error('无法获取设置数据锁，请稍后再试');
      }
    }

    try {
      // 先导出数据作为备份
      const backup = await this.exportData();

      // 保存备份到本地存储
      await new Promise((resolve, reject) => {
        chrome.storage.local.set({ autoBackup: JSON.stringify(backup) }, () => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
          } else {
            resolve();
          }
        });
      });

      // 清除记忆（不使用锁，因为已经获取了锁）
      await this.saveMemories([], false);

      // 如果不保留设置，重置设置为默认值
      if (!keepSettings) {
        const defaultSettings = {
          ...this.defaultSettings,
          dataVersion: 1,
          lastModified: new Date().toISOString(),
          lastModifiedBy: this._deviceId,
          clearDate: new Date().toISOString()
        };
        await this.saveSettings(defaultSettings, false);
      }

      console.log('数据清除成功，已创建备份');
      return backup;
    } finally {
      // 释放所有锁
      await this.releaseLock('memories');
      if (!keepSettings && settingsLockAcquired) {
        await this.releaseLock('settings');
      }
    }
  }

  /**
   * 恢复自动备份
   * @returns {Promise<Object>} 恢复的数据对象
   */
  async restoreAutoBackup() {
    try {
      // 获取备份数据
      const backupData = await new Promise((resolve, reject) => {
        chrome.storage.local.get(['autoBackup'], (result) => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
            return;
          }

          if (!result.autoBackup) {
            reject(new Error('找不到自动备份数据'));
            return;
          }

          try {
            const data = JSON.parse(result.autoBackup);
            resolve(data);
          } catch (error) {
            reject(new Error('备份数据解析失败: ' + error.message));
          }
        });
      });

      // 尝试获取所有锁
      const settingsLockAcquired = await this.acquireLock('settings', 10000);
      if (!settingsLockAcquired) {
        throw new Error('无法获取设置数据锁，请稍后再试');
      }

      const memoriesLockAcquired = await this.acquireLock('memories', 10000);
      if (!memoriesLockAcquired) {
        // 如果无法获取记忆锁，释放设置锁
        await this.releaseLock('settings');
        throw new Error('无法获取记忆数据锁，请稍后再试');
      }

      try {
        // 更新数据版本和设备信息
        backupData.settings.dataVersion = (backupData.settings.dataVersion || 0) + 1;
        backupData.settings.lastModified = new Date().toISOString();
        backupData.settings.lastModifiedBy = this._deviceId;
        backupData.settings.restoredAt = new Date().toISOString();

        // 为所有记忆添加版本信息（如果没有）
        const updatedMemories = backupData.memories.map(memory => {
          if (!memory.version) {
            return {
              ...memory,
              version: 1,
              lastModified: new Date().toISOString(),
              lastModifiedBy: this._deviceId
            };
          }
          return memory;
        });

        // 保存恢复的数据（不使用锁，因为已经获取了锁）
        await this.saveSettings(backupData.settings, false);
        await this.saveMemories(updatedMemories, false);

        console.log('恢复备份成功，共恢复记忆 ' + updatedMemories.length + ' 条');
        return backupData;
      } finally {
        // 释放所有锁
        await this.releaseLock('memories');
        await this.releaseLock('settings');
      }
    } catch (error) {
      console.error('恢复备份失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
const storageService = new StorageService();
export default storageService;
