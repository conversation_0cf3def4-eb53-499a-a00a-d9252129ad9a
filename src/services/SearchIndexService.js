/**
 * SearchIndexService - 搜索索引服务类
 *
 * 用于构建和维护记忆内容的全文搜索索引，支持高效的关键词搜索
 */
class SearchIndexService {
  constructor() {
    this.initialized = false;
    this.ossStorageService = null;
    this.localCacheService = null;
    this.indexChunks = {}; // 索引分片缓存
    this.stopWords = new Set([
      '的', '了', '和', '是', '在', '我', '有', '这', '个', '你', '们', '他', '她', '它', '那', '就', '都', '而', '及', '与',
      'the', 'a', 'an', 'and', 'or', 'but', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
      'in', 'on', 'at', 'to', 'for', 'of', 'by', 'about', 'like', 'with', 'from'
    ]);
  }

  /**
   * 初始化搜索索引服务
   * @param {Object} ossStorageService - OSS存储服务实例
   * @param {Object} localCacheService - 本地缓存服务实例
   */
  initialize(ossStorageService, localCacheService) {
    this.ossStorageService = ossStorageService;
    this.localCacheService = localCacheService;
    this.storageService = require('./StorageService').default;
    this.initialized = true;
    console.log('搜索索引服务初始化完成');
  }

  /**
   * 检查服务是否已初始化
   * @private
   */
  _checkInitialized() {
    if (!this.initialized) {
      throw new Error('搜索索引服务未初始化');
    }
  }

  /**
   * 为记忆创建或更新搜索索引
   * @param {Object} memory - 记忆对象
   * @returns {Promise<void>}
   */
  async indexMemory(memory) {
    this._checkInitialized();

    try {
      console.log(`为记忆 ${memory.id} 创建搜索索引`);

      // 提取记忆中的关键词
      const keywords = this._extractKeywords(memory);

      // 获取当前索引
      const searchIndex = await this._getSearchIndex();

      // 更新索引
      for (const keyword of keywords) {
        // 如果关键词不存在于索引中，创建新条目
        if (!searchIndex.keywords[keyword]) {
          searchIndex.keywords[keyword] = {
            count: 0,
            memories: []
          };
        }

        // 如果记忆ID不在关键词的记忆列表中，添加它
        if (!searchIndex.keywords[keyword].memories.includes(memory.id)) {
          searchIndex.keywords[keyword].memories.push(memory.id);
          searchIndex.keywords[keyword].count++;
        }
      }

      // 更新记忆到关键词的映射
      searchIndex.memory_to_keywords[memory.id] = keywords;

      // 保存更新后的索引
      await this._saveSearchIndex(searchIndex);

      console.log(`记忆 ${memory.id} 的搜索索引已更新`);
    } catch (error) {
      console.error(`为记忆创建搜索索引失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 从索引中移除记忆
   * @param {string} memoryId - 记忆ID
   * @returns {Promise<void>}
   */
  async removeMemoryFromIndex(memoryId) {
    this._checkInitialized();

    try {
      console.log(`从搜索索引中移除记忆 ${memoryId}`);

      // 获取当前索引
      const searchIndex = await this._getSearchIndex();

      // 获取记忆关联的关键词
      const keywords = searchIndex.memory_to_keywords[memoryId] || [];

      // 从每个关键词的记忆列表中移除该记忆
      for (const keyword of keywords) {
        if (searchIndex.keywords[keyword]) {
          searchIndex.keywords[keyword].memories = searchIndex.keywords[keyword].memories.filter(id => id !== memoryId);
          searchIndex.keywords[keyword].count = searchIndex.keywords[keyword].memories.length;

          // 如果关键词没有关联的记忆，可以考虑删除该关键词
          if (searchIndex.keywords[keyword].count === 0) {
            delete searchIndex.keywords[keyword];
          }
        }
      }

      // 删除记忆到关键词的映射
      delete searchIndex.memory_to_keywords[memoryId];

      // 保存更新后的索引
      await this._saveSearchIndex(searchIndex);

      console.log(`记忆 ${memoryId} 已从搜索索引中移除`);
    } catch (error) {
      console.error(`从搜索索引中移除记忆失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 搜索记忆
   * @param {string} query - 搜索查询
   * @param {Object} options - 搜索选项
   * @param {number} options.limit - 返回结果数量限制
   * @param {boolean} options.fuzzy - 是否启用模糊搜索
   * @returns {Promise<Array<{id: string, score: number}>>} 搜索结果，按相关性排序
   */
  async search(query, options = { limit: 100, fuzzy: true }) {
    this._checkInitialized();

    try {
      console.log(`执行搜索: "${query}"`);

      // 如果查询为空，返回空结果
      if (!query || query.trim() === '') {
        return [];
      }

      // 提取查询中的关键词
      const queryKeywords = this._tokenize(query);

      // 如果没有有效关键词，返回空结果
      if (queryKeywords.length === 0) {
        return [];
      }

      // 获取搜索索引
      const searchIndex = await this._getSearchIndex();

      // 计算每个记忆的相关性得分
      const scores = {};

      for (const keyword of queryKeywords) {
        // 精确匹配
        if (searchIndex.keywords[keyword]) {
          for (const memoryId of searchIndex.keywords[keyword].memories) {
            scores[memoryId] = (scores[memoryId] || 0) + 1;
          }
        }

        // 模糊匹配
        if (options.fuzzy && keyword.length > 3) {
          for (const indexedKeyword in searchIndex.keywords) {
            // 如果关键词包含查询关键词，或查询关键词包含关键词
            if (indexedKeyword !== keyword &&
                (indexedKeyword.includes(keyword) || keyword.includes(indexedKeyword))) {
              for (const memoryId of searchIndex.keywords[indexedKeyword].memories) {
                // 模糊匹配的得分较低
                scores[memoryId] = (scores[memoryId] || 0) + 0.5;
              }
            }
          }
        }
      }

      // 将得分转换为数组并排序
      const results = Object.entries(scores)
        .map(([id, score]) => ({ id, score }))
        .sort((a, b) => b.score - a.score)
        .slice(0, options.limit);

      console.log(`搜索完成，找到 ${results.length} 条结果`);

      return results;
    } catch (error) {
      console.error(`搜索失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 重建所有记忆的搜索索引
   * @returns {Promise<void>}
   */
  async rebuildIndex() {
    this._checkInitialized();

    try {
      console.log('开始重建搜索索引...');

      // 创建新的空索引
      const newIndex = {
        version: 1,
        last_updated: new Date().toISOString(),
        keywords: {},
        memory_to_keywords: {}
      };

      // 保存空索引
      await this._saveSearchIndex(newIndex);

      // 获取所有记忆块
      const metadata = await this.ossStorageService.getUserMetadata();
      const chunks = metadata.memory_chunks || [];

      let processedCount = 0;

      // 处理每个块
      for (const chunk of chunks) {
        try {
          console.log(`处理记忆块 ${chunk.id}...`);

          // 获取块数据
          const chunkData = await this.ossStorageService._getMemoryChunk(chunk.id);

          // 处理块中的每个记忆
          for (const memoryId in chunkData.memories) {
            try {
              // 获取完整记忆
              const memory = await this.ossStorageService.getMemory(memoryId);

              // 为记忆创建索引
              await this.indexMemory(memory);

              processedCount++;

              // 每处理100个记忆输出一次日志
              if (processedCount % 100 === 0) {
                console.log(`已处理 ${processedCount} 条记忆`);
              }
            } catch (memoryError) {
              console.error(`处理记忆 ${memoryId} 失败: ${memoryError.message}`);
              // 继续处理下一个记忆
            }
          }
        } catch (chunkError) {
          console.error(`处理块 ${chunk.id} 失败: ${chunkError.message}`);
          // 继续处理下一个块
        }
      }

      console.log(`搜索索引重建完成，共处理 ${processedCount} 条记忆`);
    } catch (error) {
      console.error(`重建搜索索引失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取搜索索引
   * @returns {Promise<Object>} 搜索索引
   * @private
   */
  async _getSearchIndex() {
    try {
      // 先从缓存获取
      const cacheKey = 'search_index';
      const cachedIndex = await this.storageService.getCacheItem(cacheKey, 'search_index');

      if (cachedIndex) {
        console.log('从缓存中获取搜索索引');
        return cachedIndex;
      }

      // 从OSS获取
      try {
        console.log('从 OSS 获取搜索索引');
        const indexPath = `users/${this.ossStorageService.userId}/indexes/search_index.json`;
        const searchIndex = await this.ossStorageService.getObject(indexPath);

        // 缓存索引
        await this.storageService.setCacheItem(cacheKey, searchIndex, 'search_index');
        console.log('搜索索引已缓存');

        return searchIndex;
      } catch (error) {
        // 如果索引不存在，创建新的空索引
        console.log('搜索索引不存在，创建新索引');

        const newIndex = {
          version: 1,
          last_updated: new Date().toISOString(),
          keywords: {},
          memory_to_keywords: {}
        };

        // 保存新索引
        await this._saveSearchIndex(newIndex);

        return newIndex;
      }
    } catch (error) {
      console.error(`获取搜索索引失败: ${error.message}`);

      // 返回空索引
      return {
        version: 1,
        last_updated: new Date().toISOString(),
        keywords: {},
        memory_to_keywords: {}
      };
    }
  }

  /**
   * 保存搜索索引
   * @param {Object} searchIndex - 搜索索引
   * @returns {Promise<void>}
   * @private
   */
  async _saveSearchIndex(searchIndex) {
    try {
      // 更新最后修改时间
      searchIndex.last_updated = new Date().toISOString();

      // 保存到OSS
      const indexPath = `users/${this.ossStorageService.userId}/indexes/search_index.json`;
      await this.ossStorageService.putObject(indexPath, JSON.stringify(searchIndex));

      // 更新缓存
      const cacheKey = 'search_index';
      await this.storageService.setCacheItem(cacheKey, searchIndex, 'search_index');
      console.log('搜索索引已保存到缓存');
    } catch (error) {
      console.error(`保存搜索索引失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 从记忆中提取关键词
   * @param {Object} memory - 记忆对象
   * @returns {Array<string>} 关键词数组
   * @private
   */
  _extractKeywords(memory) {
    const keywords = new Set();

    // 从标题中提取关键词
    if (memory.title) {
      this._tokenize(memory.title).forEach(keyword => keywords.add(keyword));
    }

    // 从内容中提取关键词
    if (memory.content) {
      this._tokenize(memory.content).forEach(keyword => keywords.add(keyword));
    }

    // 添加标签作为关键词
    if (memory.tags && Array.isArray(memory.tags)) {
      memory.tags.forEach(tag => {
        // 将标签分词
        this._tokenize(tag).forEach(keyword => keywords.add(keyword));
        // 同时将整个标签作为一个关键词
        if (tag.length > 1) {
          keywords.add(tag);
        }
      });
    }

    // 添加分类作为关键词
    if (memory.category) {
      keywords.add(memory.category);
    }

    return Array.from(keywords);
  }

  /**
   * 将文本分词为关键词
   * @param {string} text - 输入文本
   * @returns {Array<string>} 关键词数组
   * @private
   */
  _tokenize(text) {
    if (!text || typeof text !== 'string') {
      return [];
    }

    // 转换为小写
    const lowerText = text.toLowerCase();

    // 简单的分词：按非字母数字字符分割
    const tokens = lowerText.split(/[^\p{L}\p{N}]+/u)
      .filter(token =>
        // 过滤空字符串
        token.length > 0 &&
        // 过滤停用词
        !this.stopWords.has(token) &&
        // 过滤单个字符（除非是数字）
        (token.length > 1 || /\d/.test(token))
      );

    // 对于中文，还需要进行字符级别的分词
    const chineseChars = lowerText.match(/[\u4e00-\u9fa5]{2,}/g) || [];

    // 对于每个中文词组，提取2-3个字符的滑动窗口
    for (const chars of chineseChars) {
      if (chars.length >= 2) {
        // 2字符窗口
        for (let i = 0; i < chars.length - 1; i++) {
          const twoChars = chars.substring(i, i + 2);
          if (!this.stopWords.has(twoChars)) {
            tokens.push(twoChars);
          }
        }

        // 3字符窗口
        if (chars.length >= 3) {
          for (let i = 0; i < chars.length - 2; i++) {
            const threeChars = chars.substring(i, i + 3);
            if (!this.stopWords.has(threeChars)) {
              tokens.push(threeChars);
            }
          }
        }
      }
    }

    return tokens;
  }
}

// 创建单例实例
const searchIndexService = new SearchIndexService();

export default searchIndexService;
