/**
 * HuaweiObsProvider - 华为云OBS存储提供者
 *
 * 实现了IStorageProvider接口，提供与华为云OBS的交互功能
 */
import ObsClient from 'esdk-obs-browserjs';
import IStorageProvider from './IStorageProvider';

class HuaweiObsProvider extends IStorageProvider {
  constructor() {
    super();
    this.obsClient = null;
    this.isInitialized = false;
    this.bucketName = null;
    this.baseUrl = null;
  }

  /**
   * 初始化OBS存储提供者
   * @param {Object} config - 配置信息
   * @param {string} config.accessKeyId - 访问密钥ID
   * @param {string} config.secretAccessKey - 秘密访问密钥
   * @param {string} config.endpoint - 终端节点
   * @param {string} config.bucketName - 存储桶名称
   * @param {string} [config.region] - 区域
   * @returns {Promise<boolean>} 初始化是否成功
   */
  async initialize(config) {
    try {
      // 检查是否有必要的设置
      if (!config.accessKeyId || !config.secretAccessKey || !config.endpoint || !config.bucketName) {
        console.log('OBS存储设置不完整，跳过初始化');
        return false;
      }

      // 创建 ObsClient 实例
      this.obsClient = new ObsClient({
        access_key_id: config.accessKeyId,
        secret_access_key: config.secretAccessKey,
        server: `https://${config.endpoint}`,
        timeout: 60 * 1000,  // 60秒超时
        max_retry_count: 3,  // 最大重试次数
      });

      this.bucketName = config.bucketName;
      this.baseUrl = `https://${this.bucketName}.${config.endpoint}`;
      this.isInitialized = true;

      console.log('华为云OBS存储提供者初始化成功');
      return true;
    } catch (error) {
      console.error('初始化华为云OBS存储提供者失败:', error);
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * 检查连接是否有效
   * @returns {Promise<boolean>} 连接是否有效
   */
  async testConnection() {
    console.log("HuaweiObsProvider.testConnection");
    if (!this.isInitialized) {
      throw new Error('OBS存储提供者未初始化');
    }

    return new Promise((resolve, reject) => {
      this.obsClient.headBucket({
        Bucket: this.bucketName
      }, (err, result) => {
        if (err) {
          reject(err);
        } else if (result.CommonMsg.Status < 300) {
          resolve(true);
        } else {
          reject(new Error(`存储桶不存在或无权限访问。状态码：${result.CommonMsg.Status}`));
        }
      });
    });
  }

  /**
   * 获取对象
   * @param {string} key - 对象键
   * @returns {Promise<any>} 对象内容
   */
  async getObject(key) {
    if (!this.isInitialized) {
      throw new Error('OBS存储提供者未初始化');
    }

    return new Promise((resolve, reject) => {
      this.obsClient.getObject({
        Bucket: this.bucketName,
        Key: key
      }, (err, result) => {
        if (err) {
          console.error(`获取对象${key}失败:`, err);
          reject(err);
          return;
        }

        if (!result) {
          const noResultError = new Error(`获取对象${key}失败: 没有返回结果`);
          console.error(noResultError);
          reject(noResultError);
          return;
        }

        console.log(`获取对象${key}响应状态码:`, result.CommonMsg.Status);

        if (result.CommonMsg.Status < 300) {
          if (!result.InterfaceResult || !result.InterfaceResult.Content) {
            const noContentError = new Error(`获取对象${key}失败: 没有内容`);
            console.error(noContentError);
            reject(noContentError);
            return;
          }

          try {
            // 尝试解析JSON
            const data = result.InterfaceResult.Content;
            console.log(`成功解析对象${key}的JSON内容`);
            resolve(data);
          } catch (parseError) {
            console.error(`解析对象${key}的JSON内容失败:`, parseError);
            // 如果不是JSON，返回原始内容
            resolve(result.InterfaceResult.Content);
          }
        } else {
          const statusError = new Error(`获取对象${key}失败，状态码: ${result.CommonMsg.Status}`);
          console.error(statusError);
          reject(statusError);
        }
      });
    });
  }

  /**
   * 上传对象
   * @param {string} key - 对象键
   * @param {string|Buffer} body - 对象内容
   * @param {Object} [options] - 上传选项
   * @param {string} [options.contentType] - 内容类型
   * @param {Object} [options.metadata] - 元数据
   * @returns {Promise<Object>} 上传结果
   */
  async putObject(key, body, options = {}) {
    if (!this.isInitialized) {
      throw new Error('OBS存储提供者未初始化');
    }

    const params = {
      Bucket: this.bucketName,
      Key: key,
      Body: body
    };

    // 添加可选参数
    if (options.contentType) {
      params.ContentType = options.contentType;
    }

    if (options.metadata) {
      params.Metadata = options.metadata;
    }

    return new Promise((resolve, reject) => {
      this.obsClient.putObject(params, (err, result) => {
        if (err) {
          console.error(`上传对象${key}失败:`, err);
          reject(err);
        } else if (result.CommonMsg.Status < 300) {
          resolve({
            success: true,
            key,
            etag: result.InterfaceResult.ETag
          });
        } else {
          const error = new Error(`上传对象${key}失败，状态码: ${result.CommonMsg.Status}`);
          console.error(error);
          reject(error);
        }
      });
    });
  }

  /**
   * 删除对象
   * @param {string} key - 对象键
   * @returns {Promise<Object>} 删除结果
   */
  async deleteObject(key) {
    if (!this.isInitialized) {
      throw new Error('OBS存储提供者未初始化');
    }

    return new Promise((resolve, reject) => {
      this.obsClient.deleteObject({
        Bucket: this.bucketName,
        Key: key
      }, (err, result) => {
        if (err) {
          console.error(`删除对象${key}失败:`, err);
          reject(err);
        } else if (result.CommonMsg.Status < 300) {
          resolve({
            success: true,
            key
          });
        } else {
          const error = new Error(`删除对象${key}失败，状态码: ${result.CommonMsg.Status}`);
          console.error(error);
          reject(error);
        }
      });
    });
  }

  /**
   * 获取存储桶名称
   * @returns {string} 存储桶名称
   */
  getBucketName() {
    return this.bucketName;
  }

  /**
   * 列出对象
   * @param {Object} options - 列出选项
   * @param {string} [options.prefix] - 前缀
   * @param {string} [options.marker] - 标记
   * @param {number} [options.maxKeys] - 最大键数
   * @param {string} [options.delimiter] - 分隔符
   * @returns {Promise<Object>} 对象列表
   */
  async listObjects(options = {}) {
    if (!this.isInitialized) {
      throw new Error('OBS存储提供者未初始化');
    }

    const params = {
      Bucket: this.bucketName
    };

    // 添加可选参数
    if (options.prefix) {
      params.Prefix = options.prefix;
    }

    if (options.marker) {
      params.Marker = options.marker;
    }

    if (options.maxKeys) {
      params.MaxKeys = options.maxKeys;
    }

    if (options.delimiter) {
      params.Delimiter = options.delimiter;
    }

    return new Promise((resolve, reject) => {
      console.log('调用obsClient.listObjects:', params);
      this.obsClient.listObjects(params, (err, result) => {
        if (err) {
          console.error('列出对象失败:', err);
          reject(err);
        } else if (result.CommonMsg.Status < 300) {
          console.log('列出对象成功:', result);
          const objects = (result.InterfaceResult.Contents || []).map(item => ({
            key: item.Key,
            size: item.Size,
            lastModified: item.LastModified,
            etag: item.ETag
          }));

          resolve({
            success: true,
            objects,
            isTruncated: result.InterfaceResult.IsTruncated,
            nextMarker: result.InterfaceResult.NextMarker,
            commonPrefixes: result.InterfaceResult.CommonPrefixes || [],
            result: result // 保留原始结果以便兼容
          });
        } else {
          const error = new Error(`列出对象失败，状态码: ${result.CommonMsg.Status}`);
          console.error(error);
          reject(error);
        }
      });
    });
  }

  /**
   * 获取对象URL
   * @param {string} key - 对象键
   * @param {number} [expires=3600] - 过期时间（秒）
   * @returns {Promise<string>} 对象URL
   */
  async getObjectUrl(key, expires = 3600) {
    if (!this.isInitialized) {
      throw new Error('OBS存储提供者未初始化');
    }

    return new Promise((resolve, reject) => {
      this.obsClient.createSignedUrl({
        Method: 'GET',
        Bucket: this.bucketName,
        Key: key,
        Expires: expires
      }, (err, result) => {
        if (err) {
          console.error(`获取对象URL失败:`, err);
          reject(err);
        } else if (result.CommonMsg.Status < 300) {
          resolve(result.InterfaceResult.SignedUrl);
        } else {
          const error = new Error(`获取对象URL失败，状态码: ${result.CommonMsg.Status}`);
          console.error(error);
          reject(error);
        }
      });
    });
  }

  createSignedUrlSync(options) {
    if (!this.isInitialized) {
      throw new Error('OBS存储提供者未初始化');
    }

    console.log("createSignedUrlSync options:", options)

    const resp = this.obsClient.createSignedUrlSync(options);
    console.log("createSignedUrlSync:", resp);

    return resp;
  }

  /**
   * 获取对象元数据
   * @param {string} key - 对象键
   * @returns {Promise<Object>} 对象元数据
   */
  async getObjectMetadata(key) {
    if (!this.isInitialized) {
      throw new Error('OBS存储提供者未初始化');
    }

    return new Promise((resolve, reject) => {
      this.obsClient.getObjectMetadata({
        Bucket: this.bucketName,
        Key: key
      }, (err, result) => {
        if (err) {
          console.error(`获取对象元数据失败: ${key}`, err);
          reject(err);
          return;
        }

        if (result.CommonMsg.Status < 300) {
          const metadata = {
            etag: result.InterfaceResult.ETag,
            size: result.InterfaceResult.ContentLength,
            lastModified: result.InterfaceResult.LastModified,
            contentType: result.InterfaceResult.ContentType
          };
          resolve(metadata);
        } else {
          const error = new Error(`获取对象元数据失败，状态码: ${result.CommonMsg.Status}`);
          console.error(error);
          reject(error);
        }
      });
    });
  }

  /**
   * 列出备份
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 备份列表
   */
  async listBackups(options = {}) {
    if (!this.isInitialized) {
      throw new Error('OBS存储提供者未初始化');
    }

    // 准备参数
    const params = {
      Bucket: this.bucketName,
      ...options
    };

    return new Promise((resolve, reject) => {
      this.obsClient.listObjects(params, (err, result) => {
        if (err) {
          reject(err);
        } else if (result.CommonMsg.Status < 300) {
          const backups = (result.InterfaceResult.Contents || []).map(item => ({
            key: item.Key,
            size: item.Size,
            lastModified: item.LastModified,
            etag: item.ETag,
          }));

          resolve({
            success: true,
            backups,
          });
        } else {
          reject(new Error(`列出备份失败。状态码：${result.CommonMsg.Status}`));
        }
      });
    });
  }

}

export default HuaweiObsProvider;