/**
 * MinioProvider - MinIO存储提供者
 *
 * 实现了IStorageProvider接口，提供与MinIO的交互功能
 * 使用浏览器兼容的方式实现，不依赖于Node.js特定模块
 */
import IStorageProvider from './IStorageProvider';
import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { generateAuthorizationHeader, generatePresignedUrl, sha256, toHex, getISODate } from '../../utils/AwsSignatureV4';

class MinioProvider extends IStorageProvider {
  constructor() {
    super();
    this.isInitialized = false;
    this.bucketName = null;
    this.baseUrl = null;
    this.config = null;
    this.s3Client = null;
  }

  /**
   * 初始化MinIO存储提供者
   * @param {Object} config - 配置信息
   * @param {string} config.accessKey - 访问密钥
   * @param {string} config.secretKey - 秘密访问密钥
   * @param {string} config.endPoint - 终端节点
   * @param {string} config.bucketName - 存储桶名称
   * @param {number} [config.port] - 端口号，默认为9000
   * @param {boolean} [config.useSSL] - 是否使用SSL，默认为true
   * @returns {Promise<boolean>} 初始化是否成功
   */
  async initialize(config) {
    try {
      // 检查是否有必要的设置
      if (!config.accessKey || !config.secretKey || !config.endPoint || !config.bucketName) {
        console.log('MinIO存储设置不完整，跳过初始化');
        return false;
      }

      this.config = config;
      this.bucketName = config.bucketName;

      // 构建基础URL
      const protocol = config.useSSL !== undefined ? (config.useSSL ? 'https' : 'http') : 'https';
      const port = config.port ? `:${config.port}` : '';
      this.baseUrl = `${protocol}://${config.endPoint}${port}`;

      // 设置区域
      this.region = config.region || 'us-east-1'; // 默认使用 us-east-1

      // 创建S3客户端
      this.s3Client = new S3Client({
        region: this.region,
        endpoint: this.baseUrl,
        credentials: {
          accessKeyId: config.accessKey,
          secretAccessKey: config.secretKey
        },
        forcePathStyle: true // MinIO需要使用路径样式的URL
      });

      // 在浏览器环境中，我们无法直接检查存储桶是否存在
      // 所以我们假设它存在，或者在第一次操作时会创建
      this.isInitialized = true;

      console.log('MinIO存储提供者初始化成功');
      return true;
    } catch (error) {
      console.error('初始化MinIO存储提供者失败:', error);
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * 检查连接是否有效
   * @returns {Promise<boolean>} 连接是否有效
   */
  async testConnection() {
    if (!this.isInitialized) {
      throw new Error('MinIO存储提供者未初始化');
    }

    try {
      // 尝试列出对象来测试连接
      await this._fetchWithAuth(`${this.baseUrl}/${this.bucketName}?max-keys=1`, {
        method: 'GET'
      });
      return true;
    } catch (error) {
      console.error('测试MinIO连接失败:', error);
      return false;
    }
  }

  /**
   * 获取存储桶名称
   * @returns {string} 存储桶名称
   */
  getBucketName() {
    return this.bucketName;
  }

  /**
   * 获取对象
   * @param {string} key - 对象键
   * @param {Object} [options] - 获取选项
   * @returns {Promise<Buffer|Object>} 对象内容
   */
  async getObject(key, options = {}) {
    if (!this.isInitialized) {
      throw new Error('MinIO存储提供者未初始化');
    }

    try {
      const response = await this._fetchWithAuth(`${this.baseUrl}/${this.bucketName}/${key}`, {
        method: 'GET'
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`获取对象失败: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`获取对象失败: ${response.status} ${response.statusText}`);
      }

      // 尝试解析JSON响应
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        try {
          return await response.json();
        } catch (e) {
          // 如果解析失败，返回原始数据
          return await response.arrayBuffer();
        }
      }

      // 返回原始数据
      return await response.arrayBuffer();
    } catch (error) {
      console.error(`获取对象失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 上传对象
   * @param {string} key - 对象键
   * @param {string|Buffer|Blob|File|ArrayBuffer|Uint8Array|Object} body - 对象内容
   * @param {string|Object} [contentType] - 内容类型或选项对象
   * @param {Object} [metadata] - 元数据
   * @returns {Promise<Object>} 上传结果
   */
  async putObject(key, body, contentType = 'application/octet-stream', metadata = {}) {
    if (!this.isInitialized) {
      throw new Error('MinIO存储提供者未初始化');
    }

    try {
      // 兼容华为云OBS的调用方式
      let actualContentType = contentType;
      let actualMetadata = metadata;

      // 如果contentType是一个对象，则它可能是华为云OBS的选项对象
      if (typeof contentType === 'object' && contentType !== null) {
        console.log('检测到华为云OBS格式的选项对象');
        if (contentType.contentType) {
          actualContentType = contentType.contentType;
        }
        if (contentType.metadata) {
          actualMetadata = { ...actualMetadata, ...contentType.metadata };
        }
      }

      console.log(`上传对象: ${key}, 内容类型: ${actualContentType}, 数据类型: ${typeof body}`);

      // 准备请求头
      const headers = {
        'Content-Type': actualContentType
      };

      // 添加元数据
      Object.keys(actualMetadata).forEach(k => {
        headers[`x-amz-meta-${k}`] = actualMetadata[k];
      });

      // 处理请求体
      let processedBody = body;

      // 处理不同类型的输入数据
      if (body instanceof ArrayBuffer) {
        // ArrayBuffer转换为Uint8Array
        processedBody = new Uint8Array(body);
        console.log(`将ArrayBuffer转换为Uint8Array, 长度: ${processedBody.length}`);
      } else if (body instanceof Uint8Array) {
        // Uint8Array保持不变
        console.log(`使用Uint8Array, 长度: ${body.length}`);
      } else if (typeof body === 'object' && body !== null && !(body instanceof Blob) && !(body instanceof File)) {
        // 如果是JSON对象，转换为字符串
        processedBody = JSON.stringify(body);
        console.log('将JSON对象转换为字符串');
      } else if (typeof body === 'string') {
        // 如果是字符串但内容类型不是JSON或文本，可能是二进制数据
        if (actualContentType.startsWith('image/') || actualContentType.startsWith('video/') || actualContentType === 'application/octet-stream') {
          // 检查是否是二进制字符串
          if (body.startsWith('\x89PNG') || body.startsWith('\xff\xd8\xff') || body.startsWith('GIF')) {
            // 这是二进制数据被错误地解释为字符串
            console.log('检测到二进制图片数据被解释为字符串，进行转换');
            try {
              // 尝试将字符串转换为字节数组
              const bytes = new Uint8Array(body.length);
              for (let i = 0; i < body.length; i++) {
                bytes[i] = body.charCodeAt(i) & 0xff;
              }
              processedBody = bytes;
              console.log(`成功将二进制字符串转换为Uint8Array，长度: ${bytes.length}`);
            } catch (error) {
              console.error('转换二进制字符串失败:', error);
              // 如果转换失败，尝试创建Blob
              processedBody = new Blob([body], { type: actualContentType });
              console.log('创建Blob作为后备方案');
            }
          } else {
            // 如果不是二进制字符串，但内容类型是二进制，创建Blob
            processedBody = new Blob([body], { type: actualContentType });
            console.log('将字符串转换为Blob，内容类型:', actualContentType);
          }
        }
      }

      // 发送请求
      const response = await this._fetchWithAuth(`${this.baseUrl}/${this.bucketName}/${key}`, {
        method: 'PUT',
        headers,
        body: processedBody
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`上传对象失败: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`上传对象失败: ${response.status} ${response.statusText}`);
      }

      // 获取ETag
      const etag = response.headers.get('ETag');

      return {
        ETag: etag,
        Key: key,
        Bucket: this.bucketName
      };
    } catch (error) {
      console.error(`上传对象失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 删除对象
   * @param {string} key - 对象键
   * @returns {Promise<Object>} 删除结果
   */
  async deleteObject(key) {
    if (!this.isInitialized) {
      throw new Error('MinIO存储提供者未初始化');
    }

    try {
      const response = await this._fetchWithAuth(`${this.baseUrl}/${this.bucketName}/${key}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`删除对象失败: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`删除对象失败: ${response.status} ${response.statusText}`);
      }

      return {
        success: true,
        key
      };
    } catch (error) {
      console.error(`删除对象失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 列出对象
   * @param {Object} options - 列出选项
   * @param {string} [options.prefix] - 前缀
   * @param {string} [options.marker] - 标记
   * @param {number} [options.maxKeys] - 最大键数
   * @param {string} [options.delimiter] - 分隔符
   * @returns {Promise<Object>} 对象列表
   */
  async listObjects(options = {}) {
    if (!this.isInitialized) {
      throw new Error('MinIO存储提供者未初始化');
    }

    try {
      // 构建查询参数
      const queryParams = new URLSearchParams();
      if (options.prefix) queryParams.set('prefix', options.prefix);
      if (options.marker) queryParams.set('marker', options.marker);
      if (options.maxKeys) queryParams.set('max-keys', options.maxKeys.toString());
      if (options.delimiter) queryParams.set('delimiter', options.delimiter);

      // 发送请求
      const url = `${this.baseUrl}/${this.bucketName}?${queryParams.toString()}`;
      const response = await this._fetchWithAuth(url, {
        method: 'GET'
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`列出对象失败: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`列出对象失败: ${response.status} ${response.statusText}`);
      }

      // 解析XML响应
      const text = await response.text();
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(text, 'text/xml');

      // 检查是否有错误
      const errorNode = xmlDoc.querySelector('Error');
      if (errorNode) {
        const code = errorNode.querySelector('Code')?.textContent || 'UnknownError';
        const message = errorNode.querySelector('Message')?.textContent || 'Unknown error';
        throw new Error(`MinIO错误: ${code} - ${message}`);
      }

      // 提取对象信息
      const contents = Array.from(xmlDoc.querySelectorAll('Contents'));
      const objects = contents.map(content => {
        const key = content.querySelector('Key')?.textContent || '';
        const size = parseInt(content.querySelector('Size')?.textContent || '0', 10);
        const lastModified = new Date(content.querySelector('LastModified')?.textContent || '');
        const etag = content.querySelector('ETag')?.textContent || '';

        return {
          Key: key,
          Size: size,
          LastModified: lastModified,
          ETag: etag
        };
      });

      // 提取公共前缀
      const commonPrefixes = Array.from(xmlDoc.querySelectorAll('CommonPrefixes')).map(prefix => {
        return {
          Prefix: prefix.querySelector('Prefix')?.textContent || ''
        };
      });

      // 提取其他信息
      const isTruncated = xmlDoc.querySelector('IsTruncated')?.textContent === 'true';
      const nextMarker = xmlDoc.querySelector('NextMarker')?.textContent || null;

      return {
        CommonMsg: { Status: 200 },
        InterfaceResult: {
          Contents: objects,
          CommonPrefixes: commonPrefixes,
          IsTruncated: isTruncated,
          NextMarker: nextMarker
        }
      };
    } catch (error) {
      console.error('列出对象失败', error);
      throw error;
    }
  }

  /**
   * 获取对象元数据
   * @param {string} key - 对象键
   * @returns {Promise<Object>} 对象元数据
   */
  async getObjectMetadata(key) {
    if (!this.isInitialized) {
      throw new Error('MinIO存储提供者未初始化');
    }

    try {
      // 使用HEAD请求获取对象元数据
      const response = await this._fetchWithAuth(`${this.baseUrl}/${this.bucketName}/${key}`, {
        method: 'HEAD'
      });

      if (!response.ok) {
        throw new Error(`获取对象元数据失败: ${response.status} ${response.statusText}`);
      }

      // 从响应头中提取元数据
      const metadata = {
        etag: response.headers.get('etag') || response.headers.get('ETag'),
        size: parseInt(response.headers.get('content-length') || '0', 10),
        lastModified: response.headers.get('last-modified'),
        contentType: response.headers.get('content-type')
      };

      // 清理ETag（移除引号）
      if (metadata.etag) {
        metadata.etag = metadata.etag.replace(/"/g, '');
      }

      return metadata;
    } catch (error) {
      console.error(`获取对象元数据失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 获取对象URL
   * @param {string} key - 对象键
   * @param {number} [expires=3600] - 过期时间（秒）
   * @returns {Promise<string>} 对象URL
   */
  async getObjectUrl(key, expires = 3600) {
    if (!this.isInitialized) {
      throw new Error('MinIO存储提供者未初始化');
    }

    try {
      // 使用AWS SDK生成预签名URL
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key
      });

      const signedUrl = await getSignedUrl(this.s3Client, command, { expiresIn: expires });
      console.log(`生成签名URL成功: ${signedUrl}`);
      return signedUrl;
    } catch (error) {
      console.error(`获取对象URL失败: ${key}`, error);
      // 如果生成预签名URL失败，返回直接URL（可能需要身份验证）
      return `${this.baseUrl}/${this.bucketName}/${key}`;
    }
  }

  /**
   * 同步创建签名URL
   * @param {Object} options - 选项
   * @returns {Promise<string>} 签名URL的Promise
   */
  createSignedUrlSync(options) {
    if (!this.isInitialized) {
      throw new Error('MinIO存储提供者未初始化');
    }

    try {
      const key = options.Key || '';
      const expires = options.Expires || 3600;

      // 创建命令
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key
      });

      // 返回一个Promise，将在OssStorageService中处理
      console.log(`创建签名URL（异步）: ${key}`);
      return getSignedUrl(this.s3Client, command, { expiresIn: expires });
    } catch (error) {
      console.error('创建签名URL失败:', error);
      return Promise.resolve(`${this.baseUrl}/${this.bucketName}/${options.Key || ''}`);
    }
  }

  /**
   * 列出备份
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 备份列表
   */
  async listBackups(options = {}) {
    if (!this.isInitialized) {
      throw new Error('MinIO存储提供者未初始化');
    }

    try {
      const result = await this.listObjects(options);

      const backups = (result.InterfaceResult.Contents || []).map(item => ({
        key: item.Key,
        size: item.Size,
        lastModified: item.LastModified,
        etag: item.ETag,
      }));

      return {
        success: true,
        backups,
      };
    } catch (error) {
      console.error('列出备份失败', error);
      throw error;
    }
  }

  /**
   * 使用AWS签名版本4对请求进行身份验证
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @returns {Promise<Response>} 响应对象
   * @private
   */
  async _fetchWithAuth(url, options = {}) {
    try {
      const urlObj = new URL(url);
      const path = urlObj.pathname + urlObj.search;
      const method = options.method || 'GET';
      const headers = options.headers || {};

      // 添加必要的头部
      headers['host'] = urlObj.host;

      // 使用未签名的负载以简化实现
      headers['x-amz-content-sha256'] = 'UNSIGNED-PAYLOAD';

      // 生成签名
      const { authorizationHeader, datetime } = await generateAuthorizationHeader({
        method,
        path,
        headers,
        payload: options.body || '',
        accessKey: this.config.accessKey,
        secretKey: this.config.secretKey,
        region: this.region,
        service: 's3'
      });

      // 添加授权头部
      headers['Authorization'] = authorizationHeader;
      headers['x-amz-date'] = datetime;

      // 记录请求详情以便调试
      console.log(`MinIO 请求: ${method} ${url}`, {
        headers,
        method,
        path
      });

      // 发送请求
      const response = await fetch(url, {
        ...options,
        headers
      });

      // 如果请求失败，记录详细错误信息
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`MinIO 请求失败: ${response.status} ${response.statusText}`, errorText);
      }

      return response;
    } catch (error) {
      console.error('生成AWS签名失败:', error);
      throw error;
    }
  }
}

export default MinioProvider;
