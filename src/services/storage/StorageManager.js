/**
 * StorageManager - 存储管理器
 * 
 * 用于管理不同的存储提供者
 */
import StorageFactory from './StorageFactory';
import StorageTypes from './StorageTypes';

class StorageManager {
  constructor() {
    this.providers = new Map();
    this.defaultProvider = null;
  }

  /**
   * 注册存储提供者
   * @param {string} name - 提供者名称
   * @param {string} type - 存储类型
   * @param {Object} config - 配置信息
   * @param {boolean} [isDefault=false] - 是否为默认提供者
   * @returns {Promise<void>}
   */
  async registerProvider(name, type, config, isDefault = false) {
    try {
      const provider = await StorageFactory.createProvider(type, config);
      this.providers.set(name, provider);
      
      if (isDefault || !this.defaultProvider) {
        this.defaultProvider = name;
      }
      
      console.log(`存储提供者 ${name} 注册成功${isDefault ? '，并设为默认' : ''}`);
    } catch (error) {
      console.error(`注册存储提供者 ${name} 失败:`, error);
      throw error;
    }
  }

  /**
   * 获取存储提供者
   * @param {string} [name] - 提供者名称，如果不提供则返回默认提供者
   * @returns {IStorageProvider} 存储提供者实例
   */
  getProvider(name) {
    const providerName = name || this.defaultProvider;
    
    if (!providerName) {
      throw new Error('没有可用的存储提供者');
    }
    
    const provider = this.providers.get(providerName);
    
    if (!provider) {
      throw new Error(`找不到存储提供者: ${providerName}`);
    }
    
    return provider;
  }

  /**
   * 设置默认存储提供者
   * @param {string} name - 提供者名称
   */
  setDefaultProvider(name) {
    if (!this.providers.has(name)) {
      throw new Error(`找不到存储提供者: ${name}`);
    }
    
    this.defaultProvider = name;
    console.log(`默认存储提供者已设置为: ${name}`);
  }

  /**
   * 获取所有注册的提供者名称
   * @returns {Array<string>} 提供者名称数组
   */
  getProviderNames() {
    return Array.from(this.providers.keys());
  }

  /**
   * 移除存储提供者
   * @param {string} name - 提供者名称
   */
  removeProvider(name) {
    if (!this.providers.has(name)) {
      return;
    }
    
    this.providers.delete(name);
    
    // 如果移除的是默认提供者，重新设置默认提供者
    if (this.defaultProvider === name) {
      const providerNames = this.getProviderNames();
      this.defaultProvider = providerNames.length > 0 ? providerNames[0] : null;
    }
    
    console.log(`存储提供者 ${name} 已移除`);
  }
}

// 创建单例实例
const storageManager = new StorageManager();

export default storageManager;