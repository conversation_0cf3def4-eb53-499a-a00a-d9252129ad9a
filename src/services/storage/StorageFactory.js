/**
 * StorageFactory - 存储工厂类
 *
 * 用于创建不同类型的存储提供者
 */
import HuaweiObsProvider from './HuaweiObsProvider';
import MinioProvider from './MinioProvider';
import StorageTypes from './StorageTypes';

class StorageFactory {
  /**
   * 创建存储提供者
   * @param {string} type - 存储类型
   * @param {Object} config - 配置信息
   * @returns {Promise<IStorageProvider>} 存储提供者实例
   */
  static async createProvider(type, config) {
    if (!StorageTypes.isValidType(type)) {
      throw new Error(`不支持的存储类型: ${type}`);
    }

    let provider;

    switch (type) {
      case StorageTypes.HUAWEI_OBS:
        provider = new HuaweiObsProvider();
        break;
      case StorageTypes.MINIO:
        provider = new MinioProvider();
        break;
      case StorageTypes.ALIYUN_OSS:
        // 未来实现
        throw new Error(`存储类型 ${type} 尚未实现`);
      case StorageTypes.AMAZON_S3:
        // 未来实现
        throw new Error(`存储类型 ${type} 尚未实现`);
      case StorageTypes.TENCENT_COS:
        // 未来实现
        throw new Error(`存储类型 ${type} 尚未实现`);
      case StorageTypes.LOCAL_FILE:
        // 未来实现
        throw new Error(`存储类型 ${type} 尚未实现`);
      default:
        throw new Error(`不支持的存储类型: ${type}`);
    }

    // 初始化提供者
    await provider.initialize(config);
    return provider;
  }
}

export default StorageFactory;