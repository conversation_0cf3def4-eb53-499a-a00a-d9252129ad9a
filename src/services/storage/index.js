/**
 * 存储模块索引文件
 */
import IStorageProvider from './IStorageProvider';
import HuaweiObsProvider from './HuaweiObsProvider';
import MinioProvider from './MinioProvider';
import StorageFactory from './StorageFactory';
import storageManager from './StorageManager';
import StorageTypes from './StorageTypes';

export {
  IStorageProvider,
  HuaweiObsProvider,
  MinioProvider,
  StorageFactory,
  storageManager,
  StorageTypes
};

export default storageManager;