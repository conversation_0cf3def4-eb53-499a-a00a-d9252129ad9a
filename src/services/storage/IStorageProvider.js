/**
 * IStorageProvider - 存储提供者接口
 *
 * 定义了存储服务应该实现的基本方法
 */

class IStorageProvider {
  /**
   * 初始化存储提供者
   * @param {Object} config - 配置信息
   * @returns {Promise<boolean>} 初始化是否成功
   */
  async initialize(config) {
    throw new Error('必须由子类实现');
  }

  /**
   * 测试连接是否有效
   * @returns {Promise<boolean>} 连接是否有效
   */
  async testConnection() {
    throw new Error('必须由子类实现');
  }

  /**
   * 获取存储桶名称
   * @returns {string} 存储桶名称
   */
  getBucketName() {
    throw new Error('必须由子类实现');
  }

  /**
   * 获取对象
   * @param {string} key - 对象键
   * @param {Object} [options] - 获取选项
   * @returns {Promise<Buffer|Object>} 对象内容
   */
  async getObject(key, options = {}) {
    throw new Error('必须由子类实现');
  }

  /**
   * 上传对象
   * @param {string} key - 对象键
   * @param {string|Buffer|Blob|File} body - 对象内容
   * @param {string} [contentType] - 内容类型
   * @param {Object} [metadata] - 元数据
   * @returns {Promise<Object>} 上传结果
   */
  async putObject(key, body, contentType = 'application/octet-stream', metadata = {}) {
    throw new Error('必须由子类实现');
  }

  /**
   * 删除对象
   * @param {string} key - 对象键
   * @returns {Promise<Object>} 删除结果
   */
  async deleteObject(key) {
    throw new Error('必须由子类实现');
  }

  /**
   * 列出对象
   * @param {Object} options - 列出选项
   * @param {string} [options.prefix] - 前缀
   * @param {string} [options.marker] - 标记
   * @param {number} [options.maxKeys] - 最大键数
   * @param {string} [options.delimiter] - 分隔符
   * @returns {Promise<Object>} 对象列表
   */
  async listObjects(options = {}) {
    throw new Error('必须由子类实现');
  }

  /**
   * 获取对象URL
   * @param {string} key - 对象键
   * @param {number} [expires=3600] - 过期时间（秒）
   * @returns {Promise<string>} 对象URL
   */
  async getObjectUrl(key, expires = 3600) {
    throw new Error('必须由子类实现');
  }

  /**
   * 同步创建签名URL
   * @param {Object} options - 选项
   * @returns {string} 签名URL
   */
  createSignedUrlSync(options) {
    throw new Error('必须由子类实现');
  }

  /**
   * 获取对象元数据
   * @param {string} key - 对象键
   * @returns {Promise<Object>} 对象元数据（包含etag、size、lastModified等）
   */
  async getObjectMetadata(key) {
    throw new Error('必须由子类实现');
  }

  /**
   * 列出备份
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 备份列表
   */
  async listBackups(options = {}) {
    throw new Error('必须由子类实现');
  }
}

export default IStorageProvider;