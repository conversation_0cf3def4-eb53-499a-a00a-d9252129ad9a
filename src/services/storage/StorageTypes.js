/**
 * StorageTypes - 存储类型枚举
 * 
 * 定义了系统支持的所有存储类型
 */

const StorageTypes = {
  // 华为云对象存储服务
  HUAWEI_OBS: 'huaweiObs',
  
  // MinIO对象存储
  MINIO: 'minio',
  
  // 阿里云对象存储服务（预留）
  ALIYUN_OSS: 'aliyunOss',
  
  // 亚马逊S3对象存储（预留）
  AMAZON_S3: 'amazonS3',
  
  // 腾讯云对象存储（预留）
  TENCENT_COS: 'tencentCos',
  
  
  /**
   * 获取所有支持的存储类型
   * @returns {Array<string>} 存储类型数组
   */
  getAllTypes() {
    return Object.keys(this)
      .filter(key => typeof this[key] === 'string')
      .map(key => this[key]);
  },
  
  /**
   * 检查存储类型是否有效
   * @param {string} type - 存储类型
   * @returns {boolean} 是否有效
   */
  isValidType(type) {
    return this.getAllTypes().includes(type);
  }
};

// 冻结对象，防止修改
Object.freeze(StorageTypes);

export default StorageTypes;