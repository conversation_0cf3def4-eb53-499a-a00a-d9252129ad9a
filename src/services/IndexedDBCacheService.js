/**
 * IndexedDBCacheService - IndexedDB缓存服务类
 * 
 * 用于管理记忆数据的本地缓存，使用IndexedDB提供更大的存储容量和更好的性能
 */
class IndexedDBCacheService {
  constructor() {
    this.dbName = 'memory_keeper_cache';
    this.dbVersion = 1;
    this.storeName = 'cache_store';
    this.db = null;
    this.memoryCache = new Map(); // 内存缓存，用于最频繁访问的数据
    this.isInitialized = false;
    this.initPromise = null;
    
    // 缓存配置
    this.config = {
      // 不同类型数据的缓存过期时间（毫秒）
      expirationTimes: {
        default: 24 * 60 * 60 * 1000, // 默认24小时
        memory: 7 * 24 * 60 * 60 * 1000, // 记忆缓存7天
        chunk: 3 * 24 * 60 * 60 * 1000, // 记忆块缓存3天
        metadata: 1 * 60 * 60 * 1000, // 元数据缓存1小时
        index: 12 * 60 * 60 * 1000, // 索引缓存12小时
        image: 30 * 24 * 60 * 60 * 1000, // 图片缓存30天
        search_index: 7 * 24 * 60 * 60 * 1000, // 搜索索引缓存7天
      },
      
      // 内存缓存大小限制
      memoryCacheSizes: {
        default: 50, // 默认最多缓存50项
        memory: 100, // 最多缓存100条记忆
        chunk: 20, // 最多缓存20个记忆块
        metadata: 1, // 只缓存1个元数据
        index: 30, // 最多缓存30个索引
        image: 200, // 最多缓存200张图片
        search_index: 1, // 只缓存1个搜索索引
      },
      
      // 缓存优先级（1-10，数字越大优先级越高）
      priorities: {
        default: 5,
        memory: 8,
        chunk: 7,
        metadata: 10,
        index: 6,
        image: 4,
        search_index: 9,
      }
    };
    
    // 初始化数据库
    this.initialize();
  }
  
  /**
   * 初始化IndexedDB
   * @returns {Promise<void>}
   */
  initialize() {
    if (this.initPromise) {
      return this.initPromise;
    }
    
    this.initPromise = new Promise((resolve, reject) => {
      if (!window.indexedDB) {
        console.error('您的浏览器不支持IndexedDB');
        this.isInitialized = false;
        reject(new Error('浏览器不支持IndexedDB'));
        return;
      }
      
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = (event) => {
        console.error('打开IndexedDB失败:', event.target.error);
        this.isInitialized = false;
        reject(event.target.error);
      };
      
      request.onsuccess = (event) => {
        this.db = event.target.result;
        this.isInitialized = true;
        console.log('IndexedDB缓存服务初始化成功');
        
        // 设置自动清理过期缓存的定时任务
        this._setupCleanupTask();
        
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // 创建缓存对象存储
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'key' });
          store.createIndex('timestamp', 'timestamp', { unique: false });
          store.createIndex('type', 'type', { unique: false });
          store.createIndex('priority', 'priority', { unique: false });
          console.log('创建缓存对象存储成功');
        }
      };
    });
    
    return this.initPromise;
  }
  
  /**
   * 设置自动清理过期缓存的定时任务
   * @private
   */
  _setupCleanupTask() {
    // 每小时清理一次过期缓存
    setInterval(() => {
      this._cleanupExpiredCache();
    }, 60 * 60 * 1000);
    
    // 立即执行一次清理
    this._cleanupExpiredCache();
  }
  
  /**
   * 清理过期缓存
   * @private
   */
  async _cleanupExpiredCache() {
    try {
      await this.initialize();
      
      const now = Date.now();
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const index = store.index('timestamp');
      
      // 获取所有缓存项
      const request = index.openCursor();
      
      request.onsuccess = (event) => {
        const cursor = event.target.result;
        if (cursor) {
          const cacheItem = cursor.value;
          const type = cacheItem.type || 'default';
          const expirationTime = this.config.expirationTimes[type] || this.config.expirationTimes.default;
          
          // 如果缓存项已过期，删除它
          if (now - cacheItem.timestamp > expirationTime) {
            store.delete(cursor.primaryKey);
            console.log(`删除过期缓存项: ${cacheItem.key}`);
          }
          
          cursor.continue();
        }
      };
      
      transaction.oncomplete = () => {
        console.log('过期缓存清理完成');
      };
      
      transaction.onerror = (event) => {
        console.error('清理过期缓存失败:', event.target.error);
      };
    } catch (error) {
      console.error('清理过期缓存时出错:', error);
    }
  }
  
  /**
   * 获取缓存项
   * @param {string} key - 缓存键
   * @param {string} [type='default'] - 缓存类型
   * @returns {Promise<any>} 缓存值，如果不存在则返回null
   */
  async getItem(key, type = 'default') {
    // 先从内存缓存获取
    const cacheKey = `${type}_${key}`;
    if (this.memoryCache.has(cacheKey)) {
      const cacheItem = this.memoryCache.get(cacheKey);
      
      // 检查是否过期
      const expirationTime = this.config.expirationTimes[type] || this.config.expirationTimes.default;
      if (Date.now() - cacheItem.timestamp < expirationTime) {
        // 更新访问时间
        cacheItem.lastAccessed = Date.now();
        this.memoryCache.set(cacheKey, cacheItem);
        
        console.log(`从内存缓存获取: ${key} (${type})`);
        return cacheItem.value;
      }
      
      // 如果过期，从内存缓存中移除
      this.memoryCache.delete(cacheKey);
    }
    
    try {
      await this.initialize();
      
      // 从IndexedDB获取
      const transaction = this.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.get(cacheKey);
        
        request.onsuccess = (event) => {
          const cacheItem = event.target.result;
          
          if (!cacheItem) {
            resolve(null);
            return;
          }
          
          // 检查是否过期
          const expirationTime = this.config.expirationTimes[type] || this.config.expirationTimes.default;
          if (Date.now() - cacheItem.timestamp > expirationTime) {
            // 如果过期，删除缓存项
            this._removeItem(cacheKey);
            resolve(null);
            return;
          }
          
          // 添加到内存缓存
          this._addToMemoryCache(cacheKey, cacheItem.value, type);
          
          // 更新访问时间
          this._updateAccessTime(cacheKey);
          
          console.log(`从IndexedDB获取: ${key} (${type})`);
          resolve(cacheItem.value);
        };
        
        request.onerror = (event) => {
          console.error(`获取缓存项失败: ${key}`, event.target.error);
          reject(event.target.error);
        };
      });
    } catch (error) {
      console.error(`获取缓存项失败: ${key}`, error);
      return null;
    }
  }
  
  /**
   * 设置缓存项
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {string} [type='default'] - 缓存类型
   * @returns {Promise<void>}
   */
  async setItem(key, value, type = 'default') {
    try {
      await this.initialize();
      
      const cacheKey = `${type}_${key}`;
      const timestamp = Date.now();
      const priority = this.config.priorities[type] || this.config.priorities.default;
      
      // 添加到内存缓存
      this._addToMemoryCache(cacheKey, value, type);
      
      // 保存到IndexedDB
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      const cacheItem = {
        key: cacheKey,
        value,
        timestamp,
        lastAccessed: timestamp,
        type,
        priority
      };
      
      return new Promise((resolve, reject) => {
        const request = store.put(cacheItem);
        
        request.onsuccess = () => {
          console.log(`缓存项保存成功: ${key} (${type})`);
          resolve();
        };
        
        request.onerror = (event) => {
          console.error(`保存缓存项失败: ${key}`, event.target.error);
          
          // 如果是存储空间不足错误，清理部分缓存
          if (event.target.error.name === 'QuotaExceededError') {
            this._cleanupCache().then(() => {
              // 重试保存
              this.setItem(key, value, type).then(resolve).catch(reject);
            }).catch(reject);
          } else {
            reject(event.target.error);
          }
        };
      });
    } catch (error) {
      console.error(`设置缓存项失败: ${key}`, error);
      throw error;
    }
  }
  
  /**
   * 移除缓存项
   * @param {string} key - 缓存键
   * @param {string} [type='default'] - 缓存类型
   * @returns {Promise<void>}
   */
  async removeItem(key, type = 'default') {
    try {
      await this.initialize();
      
      const cacheKey = `${type}_${key}`;
      
      // 从内存缓存移除
      this.memoryCache.delete(cacheKey);
      
      // 从IndexedDB移除
      return this._removeItem(cacheKey);
    } catch (error) {
      console.error(`移除缓存项失败: ${key}`, error);
      throw error;
    }
  }
  
  /**
   * 从IndexedDB移除缓存项
   * @param {string} cacheKey - 完整缓存键
   * @returns {Promise<void>}
   * @private
   */
  async _removeItem(cacheKey) {
    const transaction = this.db.transaction([this.storeName], 'readwrite');
    const store = transaction.objectStore(this.storeName);
    
    return new Promise((resolve, reject) => {
      const request = store.delete(cacheKey);
      
      request.onsuccess = () => {
        console.log(`缓存项删除成功: ${cacheKey}`);
        resolve();
      };
      
      request.onerror = (event) => {
        console.error(`删除缓存项失败: ${cacheKey}`, event.target.error);
        reject(event.target.error);
      };
    });
  }
  
  /**
   * 更新缓存项的访问时间
   * @param {string} cacheKey - 完整缓存键
   * @private
   */
  async _updateAccessTime(cacheKey) {
    try {
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      const request = store.get(cacheKey);
      
      request.onsuccess = (event) => {
        const cacheItem = event.target.result;
        if (cacheItem) {
          cacheItem.lastAccessed = Date.now();
          store.put(cacheItem);
        }
      };
    } catch (error) {
      console.error(`更新缓存项访问时间失败: ${cacheKey}`, error);
    }
  }
  
  /**
   * 添加到内存缓存
   * @param {string} cacheKey - 完整缓存键
   * @param {any} value - 缓存值
   * @param {string} type - 缓存类型
   * @private
   */
  _addToMemoryCache(cacheKey, value, type) {
    const timestamp = Date.now();
    const cacheItem = { value, timestamp, lastAccessed: timestamp };
    
    // 添加到内存缓存
    this.memoryCache.set(cacheKey, cacheItem);
    
    // 如果内存缓存超过最大数量，移除最旧的项
    const maxSize = this.config.memoryCacheSizes[type] || this.config.memoryCacheSizes.default;
    
    // 计算当前类型的缓存项数量
    let typeCount = 0;
    for (const key of this.memoryCache.keys()) {
      if (key.startsWith(`${type}_`)) {
        typeCount++;
      }
    }
    
    if (typeCount > maxSize) {
      this._cleanupMemoryCache(type);
    }
  }
  
  /**
   * 清理内存缓存
   * @param {string} type - 缓存类型
   * @private
   */
  _cleanupMemoryCache(type) {
    // 获取指定类型的所有缓存项
    const cacheItems = [];
    
    for (const [key, value] of this.memoryCache.entries()) {
      if (key.startsWith(`${type}_`)) {
        cacheItems.push({
          key,
          lastAccessed: value.lastAccessed
        });
      }
    }
    
    // 按最后访问时间排序
    cacheItems.sort((a, b) => a.lastAccessed - b.lastAccessed);
    
    // 移除最旧的项，直到数量符合限制
    const maxSize = this.config.memoryCacheSizes[type] || this.config.memoryCacheSizes.default;
    const removeCount = cacheItems.length - maxSize;
    
    for (let i = 0; i < removeCount; i++) {
      this.memoryCache.delete(cacheItems[i].key);
    }
    
    console.log(`清理内存缓存: 移除了 ${removeCount} 个 ${type} 类型的缓存项`);
  }
  
  /**
   * 清理缓存
   * 根据优先级和最后访问时间，移除低优先级的缓存项
   * @returns {Promise<void>}
   * @private
   */
  async _cleanupCache() {
    try {
      await this.initialize();
      
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const index = store.index('priority');
      
      // 获取所有缓存项，按优先级排序
      const request = index.openCursor(null, 'next');
      
      // 收集所有缓存项
      const cacheItems = [];
      
      await new Promise((resolve) => {
        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (cursor) {
            cacheItems.push(cursor.value);
            cursor.continue();
          } else {
            resolve();
          }
        };
      });
      
      // 按优先级（升序）和最后访问时间（升序）排序
      cacheItems.sort((a, b) => {
        if (a.priority !== b.priority) {
          return a.priority - b.priority;
        }
        return a.lastAccessed - b.lastAccessed;
      });
      
      // 移除前30%的缓存项
      const removeCount = Math.ceil(cacheItems.length * 0.3);
      const itemsToRemove = cacheItems.slice(0, removeCount);
      
      for (const item of itemsToRemove) {
        await this._removeItem(item.key);
      }
      
      console.log(`清理缓存: 移除了 ${itemsToRemove.length} 个缓存项`);
    } catch (error) {
      console.error('清理缓存失败:', error);
      throw error;
    }
  }
  
  /**
   * 清空缓存
   * @returns {Promise<void>}
   */
  async clear() {
    try {
      await this.initialize();
      
      // 清空内存缓存
      this.memoryCache.clear();
      
      // 清空IndexedDB
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.clear();
        
        request.onsuccess = () => {
          console.log('缓存已清空');
          resolve();
        };
        
        request.onerror = (event) => {
          console.error('清空缓存失败:', event.target.error);
          reject(event.target.error);
        };
      });
    } catch (error) {
      console.error('清空缓存失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取缓存统计信息
   * @returns {Promise<Object>} 缓存统计信息
   */
  async getStats() {
    try {
      await this.initialize();
      
      const transaction = this.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      
      // 获取所有缓存项
      const request = store.getAll();
      
      return new Promise((resolve, reject) => {
        request.onsuccess = (event) => {
          const cacheItems = event.target.result;
          
          // 按类型分组统计
          const stats = {
            total: cacheItems.length,
            byType: {},
            memoryCache: {
              total: this.memoryCache.size,
              byType: {}
            }
          };
          
          // 统计IndexedDB缓存
          for (const item of cacheItems) {
            const type = item.type || 'default';
            
            if (!stats.byType[type]) {
              stats.byType[type] = {
                count: 0,
                size: 0
              };
            }
            
            stats.byType[type].count++;
            
            // 估算大小（字节）
            try {
              const size = JSON.stringify(item.value).length;
              stats.byType[type].size += size;
            } catch (e) {
              // 忽略无法序列化的对象
            }
          }
          
          // 统计内存缓存
          for (const [key, value] of this.memoryCache.entries()) {
            const type = key.split('_')[0] || 'default';
            
            if (!stats.memoryCache.byType[type]) {
              stats.memoryCache.byType[type] = {
                count: 0
              };
            }
            
            stats.memoryCache.byType[type].count++;
          }
          
          resolve(stats);
        };
        
        request.onerror = (event) => {
          console.error('获取缓存统计信息失败:', event.target.error);
          reject(event.target.error);
        };
      });
    } catch (error) {
      console.error('获取缓存统计信息失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const indexedDBCacheService = new IndexedDBCacheService();
export default indexedDBCacheService;
