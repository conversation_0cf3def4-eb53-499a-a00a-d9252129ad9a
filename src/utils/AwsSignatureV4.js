/**
 * AWS Signature V4 implementation for browser
 *
 * This utility provides functions to generate AWS Signature V4 for authentication
 * with AWS compatible services like MinIO
 */

/**
 * Generate an ISO8601 formatted timestamp
 * @returns {string} ISO8601 timestamp
 */
export function getISODate() {
  return new Date().toISOString().replace(/[:-]|\.\d{3}/g, '');
}

/**
 * Convert string to Uint8Array
 * @param {string} str - Input string
 * @returns {Uint8Array} Uint8Array representation
 */
export function toUint8Array(str) {
  return new TextEncoder().encode(str);
}

/**
 * Convert ArrayBuffer to hex string
 * @param {ArrayBuffer} buffer - Input buffer
 * @returns {string} Hex string
 */
export function toHex(buffer) {
  return Array.from(new Uint8Array(buffer))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * Create a SHA-256 hash
 * @param {string|Uint8Array} message - Message to hash
 * @returns {Promise<ArrayBuffer>} Hash result
 */
export async function sha256(message) {
  const msgUint8 = typeof message === 'string' ? toUint8Array(message) : message;
  const hashBuffer = await crypto.subtle.digest('SHA-256', msgUint8);
  return hashBuffer;
}

/**
 * Create a HMAC-SHA256 signature
 * @param {string|ArrayBuffer} key - Key for HMAC
 * @param {string} message - Message to sign
 * @returns {Promise<ArrayBuffer>} Signature
 */
export async function hmacSha256(key, message) {
  const keyBuffer = typeof key === 'string' ? toUint8Array(key) : key;
  const messageBuffer = toUint8Array(message);

  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyBuffer,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  return crypto.subtle.sign('HMAC', cryptoKey, messageBuffer);
}

/**
 * Create a canonical request for AWS Signature V4
 * @param {string} method - HTTP method
 * @param {string} path - Request path
 * @param {Object} headers - Request headers
 * @param {ArrayBuffer} payloadHash - SHA-256 hash of the payload
 * @returns {string} Canonical request
 */
export function createCanonicalRequest(method, path, headers, payloadHash) {
  // Parse the URL to separate path and query string
  let canonicalUri = path;
  let canonicalQueryString = '';

  // Split path and query string if present
  const queryIndex = path.indexOf('?');
  if (queryIndex >= 0) {
    canonicalUri = path.substring(0, queryIndex);
    const queryString = path.substring(queryIndex + 1);

    // Parse and sort query parameters
    const searchParams = new URLSearchParams(queryString);
    const sortedParams = Array.from(searchParams.entries()).sort();

    canonicalQueryString = sortedParams
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');
  }

  // Ensure the path is properly encoded
  canonicalUri = canonicalUri.split('/')
    .map(segment => encodeURIComponent(decodeURIComponent(segment)))
    .join('/');

  // Create canonical headers
  const canonicalHeaders = Object.keys(headers)
    .sort()
    .map(key => `${key.toLowerCase()}:${headers[key].trim()}`)
    .join('\n') + '\n';

  // Create signed headers
  const signedHeaders = Object.keys(headers)
    .sort()
    .map(key => key.toLowerCase())
    .join(';');

  // Convert payload hash to hex if it's an ArrayBuffer
  const payloadHashHex = payloadHash instanceof ArrayBuffer
    ? toHex(new Uint8Array(payloadHash))
    : payloadHash;

  // Combine components to create canonical request
  const canonicalRequest = [
    method,
    canonicalUri,
    canonicalQueryString,
    canonicalHeaders,
    signedHeaders,
    payloadHashHex
  ].join('\n');

  return canonicalRequest;
}

/**
 * Create a string to sign for AWS Signature V4
 * @param {string} datetime - ISO8601 timestamp
 * @param {string} region - AWS region
 * @param {string} service - AWS service
 * @param {string} canonicalRequest - Canonical request
 * @returns {Promise<string>} String to sign
 */
export async function createStringToSign(datetime, region, service, canonicalRequest) {
  const hash = await sha256(canonicalRequest);
  const hashedCanonicalRequest = toHex(new Uint8Array(hash));

  const scope = `${datetime.slice(0, 8)}/${region}/${service}/aws4_request`;

  return [
    'AWS4-HMAC-SHA256',
    datetime,
    scope,
    hashedCanonicalRequest
  ].join('\n');
}

/**
 * Calculate the signing key for AWS Signature V4
 * @param {string} secretKey - AWS secret key
 * @param {string} date - Date in format YYYYMMDD
 * @param {string} region - AWS region
 * @param {string} service - AWS service
 * @returns {Promise<ArrayBuffer>} Signing key
 */
export async function getSigningKey(secretKey, date, region, service) {
  const kDate = await hmacSha256(toUint8Array(`AWS4${secretKey}`), date);
  const kRegion = await hmacSha256(kDate, region);
  const kService = await hmacSha256(kRegion, service);
  const kSigning = await hmacSha256(kService, 'aws4_request');
  return kSigning;
}

/**
 * Calculate the signature for AWS Signature V4
 * @param {ArrayBuffer} signingKey - Signing key
 * @param {string} stringToSign - String to sign
 * @returns {Promise<string>} Signature
 */
export async function calculateSignature(signingKey, stringToSign) {
  const signature = await hmacSha256(signingKey, stringToSign);
  return toHex(new Uint8Array(signature));
}

/**
 * Generate AWS Signature V4 authorization header
 * @param {Object} options - Options
 * @param {string} options.method - HTTP method
 * @param {string} options.path - Request path
 * @param {Object} options.headers - Request headers
 * @param {string|ArrayBuffer|Blob|File} options.payload - Request payload
 * @param {string} options.accessKey - AWS access key
 * @param {string} options.secretKey - AWS secret key
 * @param {string} options.region - AWS region
 * @param {string} options.service - AWS service
 * @returns {Promise<Object>} Authorization header and datetime
 */
export async function generateAuthorizationHeader(options) {
  const {
    method,
    path,
    headers,
    payload,
    accessKey,
    secretKey,
    region,
    service
  } = options;

  // Create a date for headers and the credential string
  const datetime = getISODate();
  const date = datetime.slice(0, 8);

  // Add required headers
  const signedHeaders = {
    ...headers,
    'x-amz-date': datetime,
    'x-amz-content-sha256': 'UNSIGNED-PAYLOAD' // Use unsigned payload for simplicity
  };

  // Create payload hash
  let payloadHash = 'UNSIGNED-PAYLOAD'; // Default to unsigned payload

  // Create canonical request
  const canonicalRequest = createCanonicalRequest(
    method,
    path,
    signedHeaders,
    payloadHash
  );

  // Create string to sign
  const stringToSign = await createStringToSign(
    datetime,
    region,
    service,
    canonicalRequest
  );

  // Calculate signing key
  const signingKey = await getSigningKey(
    secretKey,
    date,
    region,
    service
  );

  // Calculate signature
  const signature = await calculateSignature(
    signingKey,
    stringToSign
  );

  // Create signed headers string
  const signedHeadersStr = Object.keys(signedHeaders)
    .sort()
    .map(key => key.toLowerCase())
    .join(';');

  // Create credential scope
  const credentialScope = `${date}/${region}/${service}/aws4_request`;

  // Create authorization header
  const authorizationHeader = [
    `AWS4-HMAC-SHA256 Credential=${accessKey}/${credentialScope}`,
    `SignedHeaders=${signedHeadersStr}`,
    `Signature=${signature}`
  ].join(', ');

  return {
    authorizationHeader,
    datetime,
    signature,
    signedHeadersStr,
    credentialScope
  };
}

/**
 * Generate a presigned URL for S3 compatible storage
 * @param {Object} options - Options
 * @param {string} options.method - HTTP method (GET, PUT, etc.)
 * @param {string} options.host - Host name (e.g., s3.amazonaws.com)
 * @param {string} options.path - Path including bucket and key
 * @param {string} options.accessKey - AWS access key
 * @param {string} options.secretKey - AWS secret key
 * @param {string} options.region - AWS region
 * @param {number} options.expires - Expiration time in seconds (default: 900)
 * @returns {Promise<string>} Presigned URL
 */
export async function generatePresignedUrl(options) {
  const {
    method = 'GET',
    host,
    path,
    accessKey,
    secretKey,
    region,
    expires = 900
  } = options;

  // Create a date for headers and the credential string
  const datetime = getISODate();
  const date = datetime.slice(0, 8);

  // Create the canonical query string
  const credential = `${accessKey}/${date}/${region}/s3/aws4_request`;

  // Create query parameters
  const queryParams = new URLSearchParams({
    'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
    'X-Amz-Credential': credential,
    'X-Amz-Date': datetime,
    'X-Amz-Expires': expires.toString(),
    'X-Amz-SignedHeaders': 'host'
  });

  // Create the canonical request
  const canonicalUri = path.split('?')[0]; // Remove any existing query string
  const canonicalQueryString = queryParams.toString();
  const canonicalHeaders = `host:${host}\n`;
  const signedHeaders = 'host';
  const payloadHash = 'UNSIGNED-PAYLOAD';

  const canonicalRequest = [
    method,
    canonicalUri,
    canonicalQueryString,
    canonicalHeaders,
    signedHeaders,
    payloadHash
  ].join('\n');

  // Create the string to sign
  const credentialScope = `${date}/${region}/s3/aws4_request`;
  const stringToSign = [
    'AWS4-HMAC-SHA256',
    datetime,
    credentialScope,
    toHex(await sha256(canonicalRequest))
  ].join('\n');

  // Calculate the signature
  const signingKey = await getSigningKey(secretKey, date, region, 's3');
  const signature = await calculateSignature(signingKey, stringToSign);

  // Add the signature to the query parameters
  queryParams.append('X-Amz-Signature', signature);

  // Construct the presigned URL
  const protocol = options.useSSL === false ? 'http' : 'https';
  const port = options.port ? `:${options.port}` : '';
  return `${protocol}://${host}${port}${canonicalUri}?${queryParams.toString()}`;
}

/**
 * 同步创建SHA-256哈希
 * 注意：这个函数使用了一个简单的SHA-256实现，不如Web Crypto API安全
 * 仅用于在同步环境中生成签名URL
 * @param {string} message - 要哈希的消息
 * @returns {string} 十六进制哈希
 */
function sha256Sync(message) {
  // 这里使用一个简化的哈希函数，实际生产环境应使用更安全的实现
  // 这个实现仅用于演示，不建议在生产环境中使用
  let hash = 0;
  for (let i = 0; i < message.length; i++) {
    const char = message.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  // 转换为16进制字符串并填充到64个字符
  const hashHex = Math.abs(hash).toString(16).padStart(64, '0');
  return hashHex;
}

/**
 * 同步生成HMAC-SHA256签名
 * 注意：这个函数使用了一个简化的HMAC实现，不如Web Crypto API安全
 * 仅用于在同步环境中生成签名URL
 * @param {string} key - 密钥
 * @param {string} message - 要签名的消息
 * @returns {string} 十六进制签名
 */
function hmacSha256Sync(key, message) {
  // 这里使用一个简化的HMAC函数，实际生产环境应使用更安全的实现
  // 这个实现仅用于演示，不建议在生产环境中使用
  const combinedStr = key + message;
  return sha256Sync(combinedStr);
}

/**
 * 同步获取签名密钥
 * @param {string} secretKey - AWS密钥
 * @param {string} date - 日期（YYYYMMDD格式）
 * @param {string} region - AWS区域
 * @param {string} service - AWS服务
 * @returns {string} 签名密钥
 */
function getSigningKeySync(secretKey, date, region, service) {
  const kDate = hmacSha256Sync('AWS4' + secretKey, date);
  const kRegion = hmacSha256Sync(kDate, region);
  const kService = hmacSha256Sync(kRegion, service);
  const kSigning = hmacSha256Sync(kService, 'aws4_request');
  return kSigning;
}

/**
 * 同步计算签名
 * @param {string} signingKey - 签名密钥
 * @param {string} stringToSign - 要签名的字符串
 * @returns {string} 签名
 */
function calculateSignatureSync(signingKey, stringToSign) {
  return hmacSha256Sync(signingKey, stringToSign);
}

/**
 * 同步生成预签名URL
 * 注意：这个函数使用了简化的哈希和HMAC实现，不如异步版本安全
 * 仅用于在同步环境中生成签名URL
 * @param {Object} options - 选项
 * @param {string} options.method - HTTP方法（GET, PUT等）
 * @param {string} options.host - 主机名（例如，s3.amazonaws.com）
 * @param {string} options.path - 包含存储桶和键的路径
 * @param {string} options.accessKey - AWS访问密钥
 * @param {string} options.secretKey - AWS秘密密钥
 * @param {string} options.region - AWS区域
 * @param {number} options.expires - 过期时间（秒）
 * @returns {string} 预签名URL
 */
export function generatePresignedUrlSync(options) {
  const {
    method = 'GET',
    host,
    path,
    accessKey,
    secretKey,
    region,
    expires = 900
  } = options;

  // 创建日期和凭证字符串
  const datetime = getISODate();
  const date = datetime.slice(0, 8);

  // 创建规范查询字符串
  const credential = `${accessKey}/${date}/${region}/s3/aws4_request`;

  // 创建查询参数
  const queryParams = new URLSearchParams({
    'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
    'X-Amz-Credential': credential,
    'X-Amz-Date': datetime,
    'X-Amz-Expires': expires.toString(),
    'X-Amz-SignedHeaders': 'host'
  });

  // 创建规范请求
  const canonicalUri = path.split('?')[0]; // 移除任何现有的查询字符串
  const canonicalQueryString = queryParams.toString();
  const canonicalHeaders = `host:${host}\n`;
  const signedHeaders = 'host';
  const payloadHash = 'UNSIGNED-PAYLOAD';

  const canonicalRequest = [
    method,
    canonicalUri,
    canonicalQueryString,
    canonicalHeaders,
    signedHeaders,
    payloadHash
  ].join('\n');

  // 创建要签名的字符串
  const credentialScope = `${date}/${region}/s3/aws4_request`;
  const stringToSign = [
    'AWS4-HMAC-SHA256',
    datetime,
    credentialScope,
    sha256Sync(canonicalRequest)
  ].join('\n');

  // 计算签名
  const signingKey = getSigningKeySync(secretKey, date, region, 's3');
  const signature = calculateSignatureSync(signingKey, stringToSign);

  // 将签名添加到查询参数
  queryParams.append('X-Amz-Signature', signature);

  // 构造预签名URL
  const protocol = options.useSSL === false ? 'http' : 'https';
  const port = options.port ? `:${options.port}` : '';
  return `${protocol}://${host}${port}${canonicalUri}?${queryParams.toString()}`;
}
