/**
 * 配置加密工具
 * 用于加密和解密配置文件中的敏感信息
 */

/**
 * 使用密码加密敏感信息
 * @param {Object} settings - 包含敏感信息的设置对象
 * @param {string} password - 加密密码
 * @returns {Object} 加密后的设置对象
 */
export const encryptSensitiveInfo = async (settings, password) => {
  if (!password || password.trim() === '') {
    throw new Error('加密密码不能为空');
  }

  // 创建一个新的设置对象，避免修改原始对象
  const encryptedSettings = { ...settings };
  
  // 提取需要加密的敏感信息
  const sensitiveInfo = {};
  
  // 华为云OBS密钥
  if (settings.huaweiObs && settings.huaweiObs.secretAccessKey) {
    sensitiveInfo.huaweiObsSecretKey = settings.huaweiObs.secretAccessKey;
    // 在原始设置中标记为已加密
    encryptedSettings.huaweiObs = {
      ...settings.huaweiObs,
      secretAccessKey: null,
      secretAccessKeyEncrypted: true
    };
  }
  
  // MinIO密钥
  if (settings.minio && settings.minio.secretKey) {
    sensitiveInfo.minioSecretKey = settings.minio.secretKey;
    encryptedSettings.minio = {
      ...settings.minio,
      secretKey: null,
      secretKeyEncrypted: true
    };
  }
  
  // Amazon S3密钥
  if (settings.amazonS3 && settings.amazonS3.secretAccessKey) {
    sensitiveInfo.amazonS3SecretKey = settings.amazonS3.secretAccessKey;
    encryptedSettings.amazonS3 = {
      ...settings.amazonS3,
      secretAccessKey: null,
      secretAccessKeyEncrypted: true
    };
  }
  
  // 腾讯云COS密钥
  if (settings.tencentCos && settings.tencentCos.secretKey) {
    sensitiveInfo.tencentCosSecretKey = settings.tencentCos.secretKey;
    encryptedSettings.tencentCos = {
      ...settings.tencentCos,
      secretKey: null,
      secretKeyEncrypted: true
    };
  }
  
  // 加密密钥
  if (settings.encryptionKey) {
    sensitiveInfo.encryptionKey = settings.encryptionKey;
    encryptedSettings.encryptionKey = null;
    encryptedSettings.encryptionKeyEncrypted = true;
  }
  
  // 如果没有敏感信息需要加密，直接返回原始设置
  if (Object.keys(sensitiveInfo).length === 0) {
    return encryptedSettings;
  }
  
  try {
    // 将敏感信息转换为JSON字符串
    const sensitiveInfoStr = JSON.stringify(sensitiveInfo);
    
    // 使用Web Crypto API进行加密
    // 1. 从密码生成密钥
    const encoder = new TextEncoder();
    const passwordData = encoder.encode(password);
    const salt = crypto.getRandomValues(new Uint8Array(16));
    
    // 使用PBKDF2派生密钥
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      passwordData,
      { name: 'PBKDF2' },
      false,
      ['deriveBits', 'deriveKey']
    );
    
    const key = await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt,
        iterations: 100000,
        hash: 'SHA-256'
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt']
    );
    
    // 2. 加密数据
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const dataToEncrypt = encoder.encode(sensitiveInfoStr);
    
    const encryptedData = await crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv
      },
      key,
      dataToEncrypt
    );
    
    // 3. 将加密结果转换为Base64字符串
    const encryptedArray = new Uint8Array(encryptedData);
    const encryptedBase64 = btoa(String.fromCharCode.apply(null, encryptedArray));
    const saltBase64 = btoa(String.fromCharCode.apply(null, salt));
    const ivBase64 = btoa(String.fromCharCode.apply(null, iv));
    
    // 4. 将加密信息添加到设置中
    encryptedSettings._sensitiveInfoEncrypted = {
      data: encryptedBase64,
      salt: saltBase64,
      iv: ivBase64,
      version: '1.0'
    };
    
    return encryptedSettings;
  } catch (error) {
    console.error('加密敏感信息失败:', error);
    throw new Error(`加密敏感信息失败: ${error.message}`);
  }
};

/**
 * 使用密码解密敏感信息
 * @param {Object} encryptedSettings - 包含加密敏感信息的设置对象
 * @param {string} password - 解密密码
 * @returns {Object} 解密后的设置对象
 */
export const decryptSensitiveInfo = async (encryptedSettings, password) => {
  if (!password || password.trim() === '') {
    throw new Error('解密密码不能为空');
  }
  
  // 检查是否有加密的敏感信息
  if (!encryptedSettings._sensitiveInfoEncrypted) {
    // 没有加密信息，直接返回原始设置
    return { ...encryptedSettings };
  }
  
  try {
    const { data, salt, iv, version } = encryptedSettings._sensitiveInfoEncrypted;
    
    // 检查版本兼容性
    if (version !== '1.0') {
      throw new Error(`不支持的加密版本: ${version}`);
    }
    
    // 解码Base64字符串
    const encryptedArray = Uint8Array.from(atob(data), c => c.charCodeAt(0));
    const saltArray = Uint8Array.from(atob(salt), c => c.charCodeAt(0));
    const ivArray = Uint8Array.from(atob(iv), c => c.charCodeAt(0));
    
    // 从密码生成密钥
    const encoder = new TextEncoder();
    const passwordData = encoder.encode(password);
    
    // 使用PBKDF2派生密钥
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      passwordData,
      { name: 'PBKDF2' },
      false,
      ['deriveBits', 'deriveKey']
    );
    
    const key = await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: saltArray,
        iterations: 100000,
        hash: 'SHA-256'
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false,
      ['decrypt']
    );
    
    // 解密数据
    const decryptedData = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: ivArray
      },
      key,
      encryptedArray
    );
    
    // 解析解密后的JSON
    const decoder = new TextDecoder();
    const decryptedStr = decoder.decode(decryptedData);
    const sensitiveInfo = JSON.parse(decryptedStr);
    
    // 创建解密后的设置对象
    const decryptedSettings = { ...encryptedSettings };
    
    // 删除加密标记
    delete decryptedSettings._sensitiveInfoEncrypted;
    
    // 恢复敏感信息
    if (sensitiveInfo.huaweiObsSecretKey && decryptedSettings.huaweiObs) {
      decryptedSettings.huaweiObs = {
        ...decryptedSettings.huaweiObs,
        secretAccessKey: sensitiveInfo.huaweiObsSecretKey,
        secretAccessKeyEncrypted: false
      };
    }
    
    if (sensitiveInfo.minioSecretKey && decryptedSettings.minio) {
      decryptedSettings.minio = {
        ...decryptedSettings.minio,
        secretKey: sensitiveInfo.minioSecretKey,
        secretKeyEncrypted: false
      };
    }
    
    if (sensitiveInfo.amazonS3SecretKey && decryptedSettings.amazonS3) {
      decryptedSettings.amazonS3 = {
        ...decryptedSettings.amazonS3,
        secretAccessKey: sensitiveInfo.amazonS3SecretKey,
        secretAccessKeyEncrypted: false
      };
    }
    
    if (sensitiveInfo.tencentCosSecretKey && decryptedSettings.tencentCos) {
      decryptedSettings.tencentCos = {
        ...decryptedSettings.tencentCos,
        secretKey: sensitiveInfo.tencentCosSecretKey,
        secretKeyEncrypted: false
      };
    }
    
    if (sensitiveInfo.encryptionKey) {
      decryptedSettings.encryptionKey = sensitiveInfo.encryptionKey;
      decryptedSettings.encryptionKeyEncrypted = false;
    }
    
    return decryptedSettings;
  } catch (error) {
    console.error('解密敏感信息失败:', error);
    throw new Error(`解密敏感信息失败，请检查密码是否正确: ${error.message}`);
  }
};

/**
 * 检查设置是否包含加密的敏感信息
 * @param {Object} settings - 设置对象
 * @returns {boolean} 是否包含加密的敏感信息
 */
export const hasEncryptedSensitiveInfo = (settings) => {
  return !!settings._sensitiveInfoEncrypted;
};

export default {
  encryptSensitiveInfo,
  decryptSensitiveInfo,
  hasEncryptedSensitiveInfo
};
