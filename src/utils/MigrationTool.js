/**
 * MigrationTool - 数据迁移工具
 *
 * 用于将旧的存储结构迁移到新的分块存储结构，
 * 支持大规模记忆数据的存储和管理。
 * 增加了从 OSS 中的数据恢复 metadata.json 的功能。
 */
import ossStorageService from '../services/OssStorageService';
import storageService from '../services/StorageService';

class MigrationTool {
  /**
   * 执行迁移
   * @returns {Promise<Object>} 迁移结果
   */
  async migrate() {
    try {
      console.log('开始数据迁移...');

      // 1. 备份当前数据
      console.log('备份当前数据...');
      const backup = await this._backupCurrentData();
      console.log('数据备份完成');

      // 2. 迁移到分块存储结构
      console.log('迁移到分块存储结构...');
      await ossStorageService.migrateToChunkedStructure();
      console.log('迁移到分块存储结构完成');

      return {
        success: true,
        message: '数据迁移成功',
        backup
      };
    } catch (error) {
      console.error('数据迁移失败:', error);
      return {
        success: false,
        message: `数据迁移失败: ${error.message}`,
        error
      };
    }
  }

  /**
   * 备份当前数据
   * @returns {Promise<Object>} 备份数据
   * @private
   */
  async _backupCurrentData() {
    try {
      // 导出当前数据
      const data = await storageService.exportData();

      // 保存备份到本地存储
      const backupKey = `migration_backup_${new Date().toISOString()}`;
      await storageService.localCache.setItem(backupKey, data);

      return {
        key: backupKey,
        timestamp: new Date().toISOString(),
        data
      };
    } catch (error) {
      console.error('备份数据失败:', error);
      throw error;
    }
  }

  /**
   * 从备份恢复数据
   * @param {string} backupKey - 备份键
   * @returns {Promise<Object>} 恢复结果
   */
  async restoreFromBackup(backupKey) {
    try {
      console.log(`从备份 ${backupKey} 恢复数据...`);

      // 从本地存储获取备份
      const backup = await storageService.localCache.getItem(backupKey);
      if (!backup) {
        throw new Error(`找不到备份: ${backupKey}`);
      }

      // 导入备份数据
      await storageService.importData(backup);

      return {
        success: true,
        message: '数据恢复成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('从备份恢复数据失败:', error);
      return {
        success: false,
        message: `数据恢复失败: ${error.message}`,
        error
      };
    }
  }

  /**
   * 获取所有备份
   * @returns {Promise<Array>} 备份列表
   */
  async getBackups() {
    try {
      const backups = [];

      // 遍历localStorage查找备份
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key.startsWith('memory_keeper_migration_backup_')) {
          try {
            const backupData = await storageService.localCache.getItem(key.replace('memory_keeper_', ''));
            backups.push({
              key: key.replace('memory_keeper_', ''),
              timestamp: key.split('_').pop(),
              memoryCount: backupData.memories ? backupData.memories.length : 0
            });
          } catch (error) {
            console.error(`获取备份 ${key} 失败:`, error);
          }
        }
      }

      // 按时间排序
      return backups.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    } catch (error) {
      console.error('获取备份列表失败:', error);
      return [];
    }
  }

  /**
   * 删除备份
   * @param {string} backupKey - 备份键
   * @returns {Promise<Object>} 删除结果
   */
  async deleteBackup(backupKey) {
    try {
      await storageService.localCache.removeItem(backupKey);

      return {
        success: true,
        message: `备份 ${backupKey} 已删除`
      };
    } catch (error) {
      console.error(`删除备份 ${backupKey} 失败:`, error);
      return {
        success: false,
        message: `删除备份失败: ${error.message}`,
        error
      };
    }
  }

  /**
   * 检查是否需要迁移
   * @returns {Promise<boolean>} 是否需要迁移
   */
  async checkNeedsMigration() {
    try {
      // 获取元数据
      const metadata = await ossStorageService.getUserMetadata();

      // 检查是否已经使用分块结构
      if (metadata.memory_chunks && metadata.memory_chunks.length > 0 &&
          metadata.memory_id_to_chunk && Object.keys(metadata.memory_id_to_chunk).length > 0) {
        return false;
      }

      // 检查是否有旧的记忆数据
      if (metadata.memory_metadata && Object.keys(metadata.memory_metadata).length > 0) {
        return true;
      }

      return false;
    } catch (error) {
      console.error('检查是否需要迁移失败:', error);
      return false;
    }
  }

  /**
   * 探索OSS中的目录结构
   * @param {string} prefix - 起始前缀
   * @param {number} depth - 探索深度
   * @returns {Promise<Object>} 探索结果
   */
  async exploreOssStructure(prefix = '', depth = 2) {
    try {
      console.log(`开始探索OSS目录结构，前缀: ${prefix}, 深度: ${depth}`);

      // 初始化 OSS 存储服务
      if (!ossStorageService.isInitialized) {
        await ossStorageService.initialize();
      }

      // 获取当前前缀下的所有文件
      const files = await ossStorageService.listObjects(prefix);
      console.log(`在前缀 ${prefix} 下找到 ${files.length} 个文件`);

      // 输出前10个文件的路径
      if (files.length > 0) {
        console.log('文件列表示例（前10个）:');
        files.slice(0, 10).forEach((file, index) => {
          console.log(`${index + 1}. ${file.Key || JSON.stringify(file)}`);
        });
      }

      // 如果深度大于0，继续探索子目录
      if (depth > 0 && files.length > 0) {
        // 提取可能的子目录
        const possibleDirs = new Set();

        for (const file of files) {
          if (file && file.Key && typeof file.Key === 'string') {
            const key = file.Key;
            // 如果当前前缀不为空，则去除前缀部分
            const relativePath = prefix ? key.substring(prefix.length) : key;
            // 提取第一级目录
            const parts = relativePath.split('/');
            if (parts.length > 1 && parts[0]) {
              possibleDirs.add(parts[0]);
            }
          }
        }

        console.log(`找到 ${possibleDirs.size} 个可能的子目录:`, Array.from(possibleDirs));

        // 递归探索子目录
        for (const dir of possibleDirs) {
          const newPrefix = prefix ? `${prefix}${dir}/` : `${dir}/`;
          await this.exploreOssStructure(newPrefix, depth - 1);
        }
      }

      // 过滤出 JSON 文件
      const jsonFiles = files.filter(file =>
        file && file.Key && typeof file.Key === 'string' &&
        file.Key.endsWith('.json')
      );

      console.log(`在前缀 ${prefix} 下找到 ${jsonFiles.length} 个 JSON 文件`);

      return {
        prefix,
        totalFiles: files.length,
        jsonFiles: jsonFiles.length,
        files: files.slice(0, 10).map(file => file.Key || JSON.stringify(file))
      };
    } catch (error) {
      console.error(`探索OSS目录结构失败，前缀: ${prefix}`, error);
      return {
        prefix,
        error: error.message
      };
    }
  }

  /**
   * 从 OSS 中的数据恢复 metadata.json
   * @returns {Promise<Object>} 恢复结果
   */
  async recoverMetadataFromOSS() {
    try {
      console.log('开始从 OSS 恢复 metadata.json...');

      // 1. 备份当前元数据（如果有的话）
      let currentMetadata = null;
      try {
        currentMetadata = await ossStorageService.getUserMetadata();
        if (currentMetadata) {
          console.log('备份当前元数据...');
          const backupKey = `metadata_backup_${new Date().toISOString()}`;
          await storageService.localCache.setItem(backupKey, currentMetadata);
          console.log(`元数据已备份到 ${backupKey}`);
        }
      } catch (error) {
        console.warn('无法获取当前元数据，可能已损坏:', error);
      }

      // 2. 扫描 OSS 中的记忆文件
      console.log('扫描 OSS 中的记忆文件...');
      // 从 OssStorageService 中获取用户ID
      if (!ossStorageService.isInitialized) {
        console.log('初始化 OssStorageService...');
        await ossStorageService.initialize();
      }

      // 输出 OssStorageService 的状态
      console.log('OssStorageService 状态:', {
        isInitialized: ossStorageService.isInitialized,
        userId: ossStorageService.userId,
        bucketName: ossStorageService.bucketName,
        baseUrl: ossStorageService.baseUrl,
        providerName: ossStorageService.providerName
      });

      const userId = ossStorageService.userId;
      if (!userId) {
        throw new Error('无法获取用户ID，请先登录或初始化存储服务');
      }
      console.log('获取到用户ID:', userId);

      // 探索OSS目录结构，找出可能的记忆文件
      console.log('开始探索OSS目录结构...');

      // 先探索用户目录
      const userDirResult = await this.exploreOssStructure(`users/${userId}/`, 3);
      console.log('用户目录探索结果:', userDirResult);

      // 收集所有可能的记忆文件路径
      let memoryFiles = [];

      // 尝试不同的前缀格式
      const prefixes = [
        `users/${userId}/memories/`,
      ];

      // 尝试每个前缀
      for (const prefix of prefixes) {
        console.log(`尝试使用前缀: ${prefix}`);
        try {
          // 列出所有记忆文件
          const files = await ossStorageService.listObjects(prefix);
          console.log(`使用前缀 ${prefix} 找到 ${files.length} 个文件`);

          if (files.length > 0) {
            // 输出前5个文件的路径供调试
            console.log('文件列表示例（前5个）:');
            files.slice(0, 5).forEach((file, index) => {
              console.log(`${index + 1}. ${file.key || JSON.stringify(file)}`);
            });

            // 过滤出.json文件
            const jsonFiles = files.filter(file =>
              file && file.key && typeof file.key === 'string' &&
              file.key.endsWith('.json') &&
              !file.key.endsWith('metadata.json')
            );

            console.log(jsonFiles);

            console.log(`在 ${files.length} 个文件中找到 ${jsonFiles.length} 个 JSON 文件`);

            if (jsonFiles.length > 0) {
              // 将找到的JSON文件添加到列表中
              memoryFiles.push(...jsonFiles);
            }
          }
        } catch (error) {
          console.warn(`使用前缀 ${prefix} 列出对象失败:`, error);
        }
      }

      console.log(`总共找到 ${memoryFiles.length} 个可能的记忆文件`);

      // 去除重复的文件
      const uniqueFiles = [];
      const seenKeys = new Set();

      for (const file of memoryFiles) {
        if (file && file.key && typeof file.key === 'string' && !seenKeys.has(file.key)) {
          seenKeys.add(file.key);
          uniqueFiles.push(file);
        }
      }

      console.log(`去除重复后还有 ${uniqueFiles.length} 个文件`);
      memoryFiles = uniqueFiles;

      // 输出前10个文件的路径供调试
      if (memoryFiles.length > 0) {
        console.log('文件列表示例（前10个）:');
        memoryFiles.slice(0, 10).forEach((file, index) => {
          console.log(`${index + 1}. ${file.key || JSON.stringify(file)}`);
        });
      }

      if (memoryFiles.length === 0) {
        // 尝试直接从存储中获取记忆文件
        console.log('尝试直接从存储中获取记忆文件...');
        try {
          // 尝试获取元数据文件
          const metadataKey = `users/${userId}/metadata.json`;
          console.log(`尝试获取元数据文件: ${metadataKey}`);
          const metadata = await ossStorageService.getObject(metadataKey);

          if (metadata && metadata.memory_metadata) {
            console.log(`从元数据文件中找到 ${Object.keys(metadata.memory_metadata).length} 条记忆`);

            // 从元数据中提取记忆ID并构建文件列表
            const memoryIds = Object.keys(metadata.memory_metadata);
            for (const memoryId of memoryIds) {
              const memoryKey = `users/${userId}/memories/${memoryId}.json`;
              memoryFiles.push({ key: memoryKey });
            }

            console.log(`从元数据构建了 ${memoryFiles.length} 个文件路径`);
          } else {
            console.log('元数据文件不存在或无效');
          }
        } catch (error) {
          console.warn('直接获取记忆文件失败:', error);
        }
      }

      if (memoryFiles.length === 0) {
        return {
          success: false,
          message: '未找到记忆文件，无法恢复元数据'
        };
      }

      // 3. 创建新的元数据结构
      const newMetadata = {
        version: '2.0',
        memory_count: 0,
        memory_metadata: {},
        memory_chunks: [],
        memory_id_to_chunk: {},
        tags: {},
        categories: {},
        last_updated: new Date().toISOString(),
        devices: currentMetadata?.devices || {}
      };

      // 4. 处理每个记忆文件
      let processedCount = 0;
      let errorCount = 0;
      let errorDetails = [];

      // 只处理.json文件，并跳过metadata.json
      const jsonFiles = memoryFiles.filter(file =>
        file && file.key && typeof file.key === 'string' &&
        file.key.endsWith('.json') &&
        !file.key.endsWith('metadata.json')
      );
      console.log(`找到 ${jsonFiles.length} 个 JSON 文件（排除metadata.json）`);

      // 输出原始文件列表的结构信息，便于调试
      console.log('原始文件列表结构:', memoryFiles.length > 0 ? JSON.stringify(memoryFiles[0]) : '空');

      // 如果没有找到JSON文件，尝试直接从存储中扫描所有文件
      if (jsonFiles.length === 0) {
        console.log('尝试直接从存储中扫描所有文件...');

        try {
          // 尝试获取存储根目录下的所有文件
          const allFiles = await ossStorageService.listObjects('');
          console.log(`在存储根目录下找到 ${allFiles.length} 个文件`);

          if (allFiles.length > 0) {
            // 输出前10个文件的路径供调试
            console.log('存储根目录文件列表示例（前10个）:');
            allFiles.slice(0, 10).forEach((file, index) => {
              console.log(`${index + 1}. ${file.key || JSON.stringify(file)}`);
            });

            // 过滤出可能的记忆文件
            const possibleMemoryFiles = allFiles.filter(file =>
              file && file.key && typeof file.key === 'string' &&
              file.key.endsWith('.json') &&
              !file.key.endsWith('metadata.json') &&
              (file.key.includes('memory') || file.key.includes('memories'))
            );

            console.log(`找到 ${possibleMemoryFiles.length} 个可能的记忆文件`);

            if (possibleMemoryFiles.length > 0) {
              // 将这些文件添加到jsonFiles中
              jsonFiles.push(...possibleMemoryFiles);
            }
          }
        } catch (error) {
          console.warn('扫描存储根目录失败:', error);
        }
      }

      for (const file of jsonFiles) {
        try {
          if (!file || !file.key || typeof file.key !== 'string') {
            const errorMsg = `跳过无效文件: ${JSON.stringify(file)}`;
            console.warn(errorMsg);
            errorDetails.push(errorMsg);
            errorCount++;
            continue;
          }

          // 提取记忆ID
          const keyParts = file.key.split('/');
          const fileName = keyParts.pop();

          if (!fileName) {
            const errorMsg = `跳过无效文件名: ${file.key}`;
            console.warn(errorMsg);
            errorDetails.push(errorMsg);
            errorCount++;
            continue;
          }

          // 跳过metadata.json文件
          if (fileName === 'metadata.json') {
            console.log(`跳过元数据文件: ${file.key}`);
            continue;
          }

          // 跳过非.json文件
          if (!fileName.endsWith('.json')) {
            console.log(`跳过非 JSON 文件: ${file.key}`);
            continue;
          }

          const memoryId = fileName.replace('.json', '');
          console.log(`处理记忆文件: ${file.key}, 提取的ID: ${memoryId}`);

          // 获取记忆文件内容
          let memory;
          try {
            memory = await ossStorageService.getObject(file.key);
            console.log(`获取到记忆文件内容:`, memory ? '成功' : '失败');
          } catch (getError) {
            const errorMsg = `获取记忆文件 ${file.key} 失败: ${getError.message}`;
            console.warn(errorMsg);
            errorDetails.push(errorMsg);
            errorCount++;
            continue;
          }

          if (!memory) {
            const errorMsg = `记忆文件 ${file.key} 内容为空`;
            console.warn(errorMsg);
            errorDetails.push(errorMsg);
            errorCount++;
            continue;
          }

          // 如果返回的是字符串，尝试解析为JSON
          if (typeof memory === 'string') {
            try {
              memory = JSON.parse(memory);
              console.log('成功将字符串解析为JSON');
            } catch (parseError) {
              const errorMsg = `解析记忆文件 ${file.key} 内容失败: ${parseError.message}`;
              console.warn(errorMsg);
              errorDetails.push(errorMsg);
              errorCount++;
              continue;
            }
          }

          if (!memory.id) {
            // 如果没有ID，使用文件名作为ID
            const errorMsg = `记忆文件 ${file.key} 缺少ID字段，使用文件名作为ID`;
            console.warn(errorMsg);
            errorDetails.push(errorMsg);
            memory.id = memoryId;
          } else if (memory.id !== memoryId) {
            const errorMsg = `记忆文件 ${file.key} 的ID(${memory.id})与文件名提取的ID(${memoryId})不匹配`;
            console.warn(errorMsg);
            errorDetails.push(errorMsg);
            // 使用文件名中的ID
            memory.id = memoryId;
          }

          // 提取元数据信息
          const memoryMeta = {
            id: memory.id,
            title: memory.title || '无标题记忆',
            content: memory.content || '',
            created_at: memory.created_at || new Date().toISOString(),
            category: memory.category || '',
            tags: Array.isArray(memory.tags) ? memory.tags : [],
            has_images: memory.images && Array.isArray(memory.images) && memory.images.length > 0,
            has_videos: memory.videos && Array.isArray(memory.videos) && memory.videos.length > 0,
            path: `memories/${memory.id}.json` // 保存相对路径
          };

          console.log(`处理记忆元数据: ID=${memoryMeta.id}, 标题=${memoryMeta.title}`);

          // 添加到元数据
          newMetadata.memory_metadata[memory.id] = memoryMeta;

          // 更新标签和分类索引
          if (memory.tags && Array.isArray(memory.tags)) {
            memory.tags.forEach(tag => {
              if (!newMetadata.tags[tag]) {
                newMetadata.tags[tag] = [];
              }
              if (!newMetadata.tags[tag].includes(memory.id)) {
                newMetadata.tags[tag].push(memory.id);
              }
            });
          }

          if (memory.category) {
            if (!newMetadata.categories[memory.category]) {
              newMetadata.categories[memory.category] = [];
            }
            if (!newMetadata.categories[memory.category].includes(memory.id)) {
              newMetadata.categories[memory.category].push(memory.id);
            }
          }

          processedCount++;

          // 每处理10个记忆输出一次进度
          if (processedCount % 10 === 0 || processedCount === jsonFiles.length) {
            console.log(`已处理 ${processedCount}/${jsonFiles.length} 个记忆文件`);
          }
        } catch (error) {
          const errorMsg = `处理记忆文件 ${file.key} 失败: ${error.message}`;
          console.error(errorMsg, error);
          errorDetails.push(errorMsg);
          errorCount++;
        }
      }

      // 输出错误详情
      if (errorCount > 0) {
        console.error(`处理过程中出现 ${errorCount} 个错误:`);
        errorDetails.slice(0, 10).forEach((error, index) => {
          console.error(`${index + 1}. ${error}`);
        });
        if (errorDetails.length > 10) {
          console.error(`... 及其他 ${errorDetails.length - 10} 个错误`);
        }
      }

      // 5. 更新记忆计数
      newMetadata.memory_count = processedCount;

      // 6. 如果需要，创建分块结构
      if (processedCount > 0) {
        console.log('创建分块结构...');
        await ossStorageService.migrateToChunkedStructure(newMetadata);
        console.log('分块结构创建完成');
      }

      // 根据处理结果决定成功或失败
      const success = processedCount > 0;

      return {
        success,
        message: success
          ? `元数据恢复成功，共处理 ${processedCount} 条记忆，${errorCount} 条错误`
          : `元数据恢复失败，未能处理任何记忆，${errorCount} 条错误`,
        processedCount,
        errorCount,
        errorDetails: errorDetails.slice(0, 20) // 返回前20个错误详情
      };
    } catch (error) {
      console.error('从 OSS 恢复 metadata.json 失败:', error);
      return {
        success: false,
        message: `恢复失败: ${error.message}`,
        error
      };
    }
  }
}

// 导出单例实例
const migrationTool = new MigrationTool();
export default migrationTool;
