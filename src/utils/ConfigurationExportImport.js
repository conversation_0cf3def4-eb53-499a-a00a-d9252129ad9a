/**
 * 配置导入导出工具
 * 用于处理用户配置的导入和导出
 */

/**
 * 处理敏感信息
 * 根据配置决定是否保留敏感信息，如密钥、密码等
 * @param {Object} settings - 设置对象
 * @param {boolean} keepSensitiveInfo - 是否保留敏感信息
 * @returns {Object} 处理后的设置对象
 */
export const processSensitiveInfo = (settings, keepSensitiveInfo = false) => {
  // 如果需要保留敏感信息，直接返回原始设置
  if (keepSensitiveInfo) {
    return { ...settings };
  }

  // 否则移除敏感信息
  const filteredSettings = { ...settings };

  // 移除云存储密钥
  if (filteredSettings.huaweiObs) {
    filteredSettings.huaweiObs = {
      ...filteredSettings.huaweiObs,
      secretAccessKey: ''
    };
  }

  if (filteredSettings.minio) {
    filteredSettings.minio = {
      ...filteredSettings.minio,
      secretKey: ''
    };
  }

  if (filteredSettings.amazonS3) {
    filteredSettings.amazonS3 = {
      ...filteredSettings.amazonS3,
      secretAccessKey: ''
    };
  }

  if (filteredSettings.tencentCos) {
    filteredSettings.tencentCos = {
      ...filteredSettings.tencentCos,
      secretKey: ''
    };
  }

  // 移除加密密钥
  if (filteredSettings.encryptionKey) {
    filteredSettings.encryptionKey = '';
  }

  return filteredSettings;
};

// 为了兼容性保留原来的函数名
export const filterSensitiveInfo = (settings) => {
  return processSensitiveInfo(settings, false);
};

/**
 * 验证配置文件
 * 检查导入的配置文件是否有效
 * @param {Object} data - 导入的数据
 * @returns {Object} 验证结果 { valid: boolean, message: string }
 */
export const validateConfigFile = (data) => {
  // 检查基本结构
  if (!data || typeof data !== 'object') {
    return { valid: false, message: '无效的配置文件格式' };
  }

  // 检查版本和导出日期
  if (!data.version || !data.exportDate) {
    return { valid: false, message: '配置文件缺少版本或导出日期信息' };
  }

  // 检查设置对象
  if (!data.settings || typeof data.settings !== 'object') {
    return { valid: false, message: '配置文件缺少设置信息' };
  }

  // 检查是否包含任何有效配置
  const hasValidConfig = (
    data.settings.userInfo ||
    data.settings.storageProvider ||
    data.settings.syncSettings ||
    data.settings.darkMode !== undefined ||
    data.settings.categories ||
    data.settings.cacheSettings ||
    data.settings.encryption !== undefined
  );

  if (!hasValidConfig) {
    return { valid: false, message: '配置文件不包含任何有效配置' };
  }

  return { valid: true, message: '配置文件有效' };
};

/**
 * 合并配置
 * 将导入的配置与当前配置合并
 * @param {Object} currentSettings - 当前设置
 * @param {Object} importedSettings - 导入的设置
 * @returns {Object} 合并后的设置
 */
export const mergeConfigurations = (currentSettings, importedSettings) => {
  // 创建合并后的设置对象
  const mergedSettings = {
    ...currentSettings,
    ...importedSettings,
    // 保留一些不应被覆盖的设置
    dataVersion: currentSettings.dataVersion,
    lastModified: new Date().toISOString(),
    lastModifiedBy: currentSettings.lastModifiedBy || 'import_operation',
    importedAt: new Date().toISOString()
  };

  return mergedSettings;
};

/**
 * 获取需要重新输入的敏感信息列表
 * @param {Object} importedSettings - 导入的设置
 * @returns {Array<string>} 敏感信息列表
 */
export const getSensitiveInfoList = (importedSettings) => {
  const sensitiveInfoList = [];

  if (importedSettings.storageProvider === 'huaweiObs') {
    sensitiveInfoList.push('华为云 OBS 的 Secret Access Key');
  }

  if (importedSettings.storageProvider === 'minio') {
    sensitiveInfoList.push('MinIO 的 Secret Key');
  }

  if (importedSettings.storageProvider === 'amazonS3') {
    sensitiveInfoList.push('Amazon S3 的 Secret Access Key');
  }

  if (importedSettings.storageProvider === 'tencentCos') {
    sensitiveInfoList.push('腾讯云 COS 的 Secret Key');
  }

  if (importedSettings.encryption) {
    sensitiveInfoList.push('加密密钥');
  }

  return sensitiveInfoList;
};

export default {
  filterSensitiveInfo,
  validateConfigFile,
  mergeConfigurations,
  getSensitiveInfoList
};
