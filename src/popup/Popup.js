import React, { useState, useEffect } from 'react';
import { Layout, Typography, Button, Space, Divider, Spin, Alert } from 'antd';
import { PlusOutlined, BookOutlined, SettingOutlined, UserOutlined, QuestionCircleOutlined, ReadOutlined } from '@ant-design/icons';

const { Header, Content, Footer } = Layout;
const { Title, Paragraph } = Typography;

const Popup = () => {
  const [loading, setLoading] = useState(true);
  const [userInfoSet, setUserInfoSet] = useState(false);

  useEffect(() => {
    // 检查用户信息是否已设置
    chrome.runtime.sendMessage({ type: 'CHECK_USER_INFO' }, (response) => {
      setUserInfoSet(response.isUserInfoSet);
      setLoading(false);
    });
  }, []);

  const navigateTo = (page) => {
    // 在这里实现页面导航逻辑（使用插件内部页面）
    const url = chrome.runtime.getURL(`main.html?page=${page}`);
    console.log(`导航到: ${url}`);
    chrome.tabs.create({ url });
  };

  const openDocumentation = () => {
    // 打开使用文档（外部链接）
    chrome.tabs.create({ url: 'https://eversnip.github.io/EverSnip/' });
  };

  if (loading) {
    return (
      <Layout style={{ width: 300, height: 400 }}>
        <Header style={{ background: '#fff', padding: '0 16px' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <img src="../icons/icon48.png" alt="Logo" style={{ width: 24, height: 24, marginRight: 8 }} />
            <Title level={4} style={{ margin: '16px 0' }}>拾光忆栈</Title>
          </div>
        </Header>
        <Content style={{ padding: '16px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Spin size="large" tip="加载中..." />
        </Content>
      </Layout>
    );
  }

  if (!userInfoSet) {
    return (
      <Layout style={{ width: 300, height: 430 }}>
        <Header style={{ background: '#fff', padding: '0 16px' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <img src="../icons/icon48.png" alt="Logo" style={{ width: 24, height: 24, marginRight: 8 }} />
            <Title level={4} style={{ margin: '16px 0' }}>拾光忆栈</Title>
          </div>
        </Header>
        <Content style={{ padding: '16px', overflowY: 'auto', display: 'flex', flexDirection: 'column' }}>
          <Alert
            message="用户信息设置"
            description="欢迎使用拾光忆栈，第一次使用，您需要设置用户信息才能使用，用户信息作为您的唯一标识，用于同步您的记忆和设置。用户信息只会存储在本地和您私有的云存储里面，不会上传到任何第三方服务器。"
            type="info"
            showIcon
            style={{ marginBottom: '10px' }}
          />

          <Button
              icon={<ReadOutlined />}
              onClick={openDocumentation}
              size="large"
              style={{ width: '100%', height: '30px' }}
          >
            使用文档
          </Button>
          <Button
            type="primary"
            icon={<UserOutlined />}
            onClick={() => navigateTo('options')}
            size="large"
            style={{ width: '100%', height: '60px', marginTop: '16px' }}
          >
            设置用户信息
          </Button>
        </Content>
      </Layout>
    );
  }

  return (
    <Layout style={{ width: 300, height: 430 }}>
      <Header style={{ background: '#fff', padding: '0 16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <img src="../icons/icon48.png" alt="Logo" style={{ width: 24, height: 24, marginRight: 8 }} />
          <Title level={4} style={{ margin: '16px 0' }}>拾光忆栈</Title>
        </div>
      </Header>
      <Content style={{ padding: '16px', overflowY: 'auto', display: 'flex', flexDirection: 'column' }}>
        <Paragraph style={{ marginBottom: '16px' }}>
          欢迎使用拾光忆栈，您的个人记忆管理助手。请选择以下操作：
        </Paragraph>

        <Space direction="vertical" style={{ width: '100%', flex: 1 }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigateTo('add-memory')}
            size="large"
            style={{ width: '100%', height: '30px', marginBottom: '14px' }}
          >
            添加记忆
          </Button>

          <Button
            icon={<BookOutlined />}
            onClick={() => navigateTo('browse-memories')}
            size="large"
            style={{ width: '100%', height: '30px', marginBottom: '14px' }}
          >
            浏览记忆
          </Button>

          <Button
            icon={<SettingOutlined />}
            onClick={() => navigateTo('options')}
            size="large"
            style={{ width: '100%', height: '30px', marginBottom: '14px' }}
          >
            设置
          </Button>

          <Button
            icon={<QuestionCircleOutlined />}
            onClick={() => navigateTo('feedback')}
            size="large"
            style={{ width: '100%', height: '30px', marginBottom: '14px' }}
          >
            问题反馈
          </Button>

          <Button
            icon={<ReadOutlined />}
            onClick={openDocumentation}
            size="large"
            style={{ width: '100%', height: '30px' }}
          >
            使用文档
          </Button>
        </Space>

        <Divider style={{ margin: '5px 0' }} />
      </Content>
      <Footer style={{ textAlign: 'center', padding: '8px 14px' }}>
          拾光忆栈 ©{new Date().getFullYear()}
      </Footer>
    </Layout>
  );
};

export default Popup;
