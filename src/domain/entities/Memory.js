/**
 * Memory实体类型定义
 * 定义了记忆对象的结构和相关类型
 */

/**
 * 记忆实体
 * @typedef {Object} Memory
 * @property {string} id - 记忆的唯一标识符
 * @property {string} title - 记忆标题
 * @property {string} content - 记忆内容
 * @property {string} category - 分类ID
 * @property {string} createdAt - 创建时间（ISO字符串）
 * @property {string} url - 相关URL（可选）
 * @property {string[]} tags - 标签数组
 * @property {number} version - 版本号
 * @property {string} lastModified - 最后修改时间（ISO字符串）
 * @property {string} lastModifiedBy - 最后修改者设备ID
 * @property {boolean} [isDeleted] - 是否已删除（软删除标记）
 * @property {string} [deletedAt] - 删除时间（ISO字符串）
 * @property {string} [deletedBy] - 删除者设备ID
 */

/**
 * 创建记忆请求
 * @typedef {Object} CreateMemoryRequest
 * @property {string} title - 记忆标题
 * @property {string} content - 记忆内容
 * @property {string} [category] - 分类ID（可选）
 * @property {string} [url] - 相关URL（可选）
 * @property {string[]} [tags] - 标签数组（可选）
 */

/**
 * 更新记忆请求
 * @typedef {Object} UpdateMemoryRequest
 * @property {string} [title] - 记忆标题
 * @property {string} [content] - 记忆内容
 * @property {string} [category] - 分类ID
 * @property {string} [url] - 相关URL
 * @property {string[]} [tags] - 标签数组
 */

/**
 * 记忆搜索查询
 * @typedef {Object} MemorySearchQuery
 * @property {string} [keyword] - 关键词搜索
 * @property {string} [category] - 分类筛选
 * @property {string[]} [tags] - 标签筛选
 * @property {string} [dateFrom] - 开始日期（ISO字符串）
 * @property {string} [dateTo] - 结束日期（ISO字符串）
 * @property {number} [limit] - 结果数量限制
 * @property {number} [offset] - 结果偏移量
 * @property {string} [sortBy] - 排序字段（createdAt, lastModified, title）
 * @property {string} [sortOrder] - 排序顺序（asc, desc）
 */

/**
 * 记忆版本信息
 * @typedef {Object} MemoryVersion
 * @property {number} version - 版本号
 * @property {string} modifiedAt - 修改时间（ISO字符串）
 * @property {string} modifiedBy - 修改者设备ID
 * @property {Object} changes - 变更内容
 */

/**
 * 批量操作结果
 * @typedef {Object} BatchOperationResult
 * @property {number} total - 总数
 * @property {number} success - 成功数
 * @property {number} failed - 失败数
 * @property {Array<{id: string, error: string}>} errors - 错误详情
 */

/**
 * Memory实体类
 * 提供记忆对象的创建和验证方法
 */
export class Memory {
  /**
   * 创建新的记忆实例
   * @param {CreateMemoryRequest} data - 创建数据
   * @param {string} deviceId - 设备ID
   * @returns {Memory} 记忆实例
   */
  static create(data, deviceId) {
    const now = new Date().toISOString();
    const id = Date.now().toString() + '_' + Math.random().toString(36).substr(2, 9);
    
    return {
      id,
      title: data.title || '无标题记忆',
      content: data.content || '',
      category: data.category || '',
      createdAt: now,
      url: data.url || '',
      tags: data.tags || [],
      version: 1,
      lastModified: now,
      lastModifiedBy: deviceId,
      isDeleted: false
    };
  }

  /**
   * 更新记忆实例
   * @param {Memory} memory - 原记忆对象
   * @param {UpdateMemoryRequest} updates - 更新数据
   * @param {string} deviceId - 设备ID
   * @returns {Memory} 更新后的记忆实例
   */
  static update(memory, updates, deviceId) {
    const now = new Date().toISOString();
    
    return {
      ...memory,
      ...updates,
      version: (memory.version || 0) + 1,
      lastModified: now,
      lastModifiedBy: deviceId
    };
  }

  /**
   * 软删除记忆
   * @param {Memory} memory - 记忆对象
   * @param {string} deviceId - 设备ID
   * @returns {Memory} 标记删除后的记忆实例
   */
  static softDelete(memory, deviceId) {
    const now = new Date().toISOString();
    
    return {
      ...memory,
      isDeleted: true,
      deletedAt: now,
      deletedBy: deviceId,
      version: (memory.version || 0) + 1,
      lastModified: now,
      lastModifiedBy: deviceId
    };
  }

  /**
   * 验证记忆数据
   * @param {Object} data - 要验证的数据
   * @returns {Object} 验证结果 {isValid: boolean, errors: string[]}
   */
  static validate(data) {
    const errors = [];

    if (!data.title || typeof data.title !== 'string' || data.title.trim().length === 0) {
      errors.push('标题不能为空');
    }

    if (data.title && data.title.length > 200) {
      errors.push('标题长度不能超过200个字符');
    }

    if (!data.content || typeof data.content !== 'string') {
      errors.push('内容不能为空');
    }

    if (data.content && data.content.length > 50000) {
      errors.push('内容长度不能超过50000个字符');
    }

    if (data.tags && !Array.isArray(data.tags)) {
      errors.push('标签必须是数组');
    }

    if (data.tags && data.tags.length > 20) {
      errors.push('标签数量不能超过20个');
    }

    if (data.url && typeof data.url !== 'string') {
      errors.push('URL必须是字符串');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 检查记忆是否匹配搜索条件
   * @param {Memory} memory - 记忆对象
   * @param {MemorySearchQuery} query - 搜索条件
   * @returns {boolean} 是否匹配
   */
  static matchesQuery(memory, query) {
    // 跳过已删除的记忆
    if (memory.isDeleted) {
      return false;
    }

    // 关键词搜索
    if (query.keyword) {
      const keyword = query.keyword.toLowerCase();
      const searchText = `${memory.title} ${memory.content}`.toLowerCase();
      if (!searchText.includes(keyword)) {
        return false;
      }
    }

    // 分类筛选
    if (query.category && memory.category !== query.category) {
      return false;
    }

    // 标签筛选
    if (query.tags && query.tags.length > 0) {
      const hasMatchingTag = query.tags.some(tag => memory.tags.includes(tag));
      if (!hasMatchingTag) {
        return false;
      }
    }

    // 日期范围筛选
    if (query.dateFrom) {
      if (new Date(memory.createdAt) < new Date(query.dateFrom)) {
        return false;
      }
    }

    if (query.dateTo) {
      if (new Date(memory.createdAt) > new Date(query.dateTo)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 排序记忆数组
   * @param {Memory[]} memories - 记忆数组
   * @param {string} sortBy - 排序字段
   * @param {string} sortOrder - 排序顺序
   * @returns {Memory[]} 排序后的记忆数组
   */
  static sort(memories, sortBy = 'lastModified', sortOrder = 'desc') {
    return memories.sort((a, b) => {
      let valueA, valueB;

      switch (sortBy) {
        case 'title':
          valueA = a.title.toLowerCase();
          valueB = b.title.toLowerCase();
          break;
        case 'createdAt':
          valueA = new Date(a.createdAt);
          valueB = new Date(b.createdAt);
          break;
        case 'lastModified':
        default:
          valueA = new Date(a.lastModified);
          valueB = new Date(b.lastModified);
          break;
      }

      if (sortOrder === 'asc') {
        return valueA < valueB ? -1 : valueA > valueB ? 1 : 0;
      } else {
        return valueA > valueB ? -1 : valueA < valueB ? 1 : 0;
      }
    });
  }
}

export default Memory;
