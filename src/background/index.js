// Background script for EverSnip extension
// Service worker environment - no window object available

// 初始化默认设置
function initializeDefaultSettings() {
  chrome.storage.local.get(['settings'], (result) => {
    const existingSettings = result.settings || {};

    // 默认分类
    const defaultCategories = [
      { id: '1', name: '工作', color: '#1890ff', icon: 'BulbOutlined' },
      { id: '2', name: '生活', color: '#52c41a', icon: 'HeartOutlined' },
      { id: '3', name: '学习', color: '#722ed1', icon: 'BookOutlined' },
      { id: '4', name: '其他', color: '#faad14', icon: 'StarOutlined' },
    ];

    // 合并现有设置和默认设置
    const updatedSettings = {
      ...existingSettings,
      categories: existingSettings.categories || defaultCategories,
      defaultCategory: existingSettings.defaultCategory || '4',
      darkMode: existingSettings.darkMode !== undefined ? existingSettings.darkMode : false,
      theme: existingSettings.theme || 'default',
      fontSize: existingSettings.fontSize || 14,
      primaryColor: existingSettings.primaryColor || '#1890ff',
      compactMode: existingSettings.compactMode !== undefined ? existingSettings.compactMode : false,
    };

    // 保存更新后的设置
    chrome.storage.local.set({
      settings: updatedSettings,
      memories: result.memories || []
    });
  });
}

// 监听安装事件
chrome.runtime.onInstalled.addListener((details) => {

  if (details.reason === 'install') {
    // 初始化存储与默认值
    initializeDefaultSettings();

    // 初始化同步设置
    initializeSyncSettings();

    // 安装后打开选项页面
    chrome.runtime.openOptionsPage();
  } else if (details.reason === 'update' || details.reason === 'chrome_update' || details.reason === 'browser_update') {
    // 在插件更新或浏览器更新后触发记忆加载
    setTimeout(() => {
      // 延迟发送消息，确保其他组件已加载
      chrome.runtime.sendMessage({ type: 'LOAD_MEMORIES_FROM_CLOUD_REQUESTED' });
    }, 5000); // 5秒后触发
  }
});

// 检查用户信息并在需要时打开设置页面
function checkUserInfoAndRedirect() {
  chrome.storage.local.get(['settings', 'memories'], (result) => {
    try {
      const settings = result.settings || {};
      const memories = result.memories || [];
      const isUserInfoSet = !!(settings.userId && settings.userInfo && settings.userInfo.username);

      if (!isUserInfoSet) {
        // 如果用户信息未设置，打开选项页面
        chrome.runtime.openOptionsPage();
      } else if (isUserInfoSet && memories.length === 0) {
        // 如果用户信息已设置但没有记忆，尝试从云端加载
        console.log('检测到用户信息已设置但没有记忆，尝试从云端加载...');
        chrome.runtime.sendMessage({ type: 'LOAD_MEMORIES_FROM_CLOUD_REQUESTED' });
      }
    } catch (error) {
      console.error('检查用户信息失败:', error);
    }
  });
}

// 监听来自内容脚本或弹出窗口的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'ADD_MEMORY') {
    // 获取当前记忆
    chrome.storage.local.get(['memories', 'settings'], (result) => {
      const memories = result.memories || [];
      const settings = result.settings || {};

      // 创建新记忆
      const newMemory = {
        id: Date.now().toString(),
        title: message.title || '无标题记忆',
        content: message.content,
        category: message.category || settings.defaultCategory || '',
        url: message.url,
        createdAt: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        lastModifiedBy: 'device_' + (Math.random().toString(36).substring(2, 10)),
        version: 1,
        tags: message.tags || [],
      };

      const updatedMemories = [...memories, newMemory];

      // 保存更新后的记忆
      chrome.storage.local.set({ memories: updatedMemories }, () => {
        sendResponse({ success: true, memory: newMemory });

        // 如果启用了数据变更时同步，触发同步
        const syncSettings = settings.syncSettings || {};
        if (syncSettings.autoSync && syncSettings.syncOnChange) {
          chrome.runtime.sendMessage({ type: 'PERFORM_SYNC' });
        }
      });
    });

    // 返回 true 表示我们将异步响应
    return true;
  } else if (message.type === 'CHECK_USER_INFO') {
    // 检查用户信息是否已设置
    chrome.storage.local.get(['settings'], (result) => {
      try {
        const settings = result.settings || {};
        const isUserInfoSet = !!(settings.userId && settings.userInfo && settings.userInfo.username);
        sendResponse({ isUserInfoSet: isUserInfoSet });
      } catch (error) {
        console.error('检查用户信息失败:', error);
        sendResponse({ isUserInfoSet: false, error: error.message });
      }
    });
    return true;
  } else if (message.type === 'OPEN_BROWSE_MEMORIES') {
    // 打开浏览记忆页面
    chrome.tabs.create({ url: 'browse-memories.html' });
    return true;
  } else if (message.type === 'PERFORM_SYNC') {
    // 执行同步操作
    // 在后台脚本中无法直接调用同步服务，需要在前台页面中处理
    // 这里只是将消息转发给所有活动页面
    chrome.runtime.sendMessage({ type: 'SYNC_REQUESTED' });
    return true;
  }
});

// 初始化同步设置
function initializeSyncSettings() {
  chrome.storage.local.get(['settings'], (result) => {
    const existingSettings = result.settings || {};

    // 如果没有同步设置，初始化默认设置
    if (!existingSettings.syncSettings) {
      const defaultSyncSettings = {
        autoSync: false,
        syncInterval: 30, // 分钟
        syncOnStartup: true,
        syncOnChange: false,
        conflictStrategy: 'auto_newest',
        compressionEnabled: true
      };

      // 更新设置
      const updatedSettings = {
        ...existingSettings,
        syncSettings: defaultSyncSettings
      };

      // 保存更新后的设置
      chrome.storage.local.set({ settings: updatedSettings });
    }
  });
}

// 执行自动同步
function performAutoSync() {
  chrome.storage.local.get(['settings'], (result) => {
    const settings = result.settings || {};
    const syncSettings = settings.syncSettings || {};

    // 如果启用了自动同步和启动时同步
    if (syncSettings.autoSync && syncSettings.syncOnStartup) {
      // 发送消息到弹出窗口或内容脚本，触发同步
      chrome.runtime.sendMessage({ type: 'PERFORM_SYNC' });
    }
  });
}

// 监听浏览器启动事件
chrome.runtime.onStartup.addListener(() => {
  // 检查用户信息是否已设置
  checkUserInfoAndRedirect();

  // 初始化同步设置
  initializeSyncSettings();

  // 执行自动同步
  performAutoSync();

  // 延迟发送加载记忆请求，确保其他组件已加载
  setTimeout(() => {
    chrome.storage.local.get(['settings', 'memories'], (result) => {
      const settings = result.settings || {};
      const memories = result.memories || [];
      const isUserInfoSet = !!(settings.userId && settings.userInfo && settings.userInfo.username);

      if (isUserInfoSet && memories.length === 0) {
        console.log('浏览器启动时检测到用户信息已设置但没有记忆，尝试从云端加载...');
        chrome.runtime.sendMessage({ type: 'LOAD_MEMORIES_FROM_CLOUD_REQUESTED' });
      }
    });
  }, 3000); // 3秒后触发
});

// 监听扩展图标点击
chrome.action.onClicked.addListener(() => {
  // 检查用户信息是否已设置
  checkUserInfoAndRedirect();
});

// 添加对LOAD_MEMORIES_FROM_CLOUD_REQUESTED消息的处理
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'LOAD_MEMORIES_FROM_CLOUD_REQUESTED') {
    // 将消息转发给所有活动页面
    chrome.runtime.sendMessage({ type: 'LOAD_MEMORIES_FROM_CLOUD_REQUESTED' });
    return true;
  }
});
