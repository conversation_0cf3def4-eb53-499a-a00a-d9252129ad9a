// Jest DOM 扩展
import '@testing-library/jest-dom';

// 模拟Chrome扩展API
global.chrome = {
  storage: {
    local: {
      get: jest.fn((keys, callback) => {
        // 默认返回空对象
        const result = {};
        if (Array.isArray(keys)) {
          keys.forEach(key => {
            result[key] = null;
          });
        } else if (typeof keys === 'string') {
          result[keys] = null;
        }
        callback(result);
      }),
      set: jest.fn((items, callback) => {
        if (callback) callback();
      }),
      remove: jest.fn((keys, callback) => {
        if (callback) callback();
      }),
      clear: jest.fn((callback) => {
        if (callback) callback();
      }),
      getBytesInUse: jest.fn((keys, callback) => {
        callback(0);
      })
    },
    sync: {
      get: jest.fn((keys, callback) => {
        const result = {};
        if (Array.isArray(keys)) {
          keys.forEach(key => {
            result[key] = null;
          });
        } else if (typeof keys === 'string') {
          result[keys] = null;
        }
        callback(result);
      }),
      set: jest.fn((items, callback) => {
        if (callback) callback();
      })
    },
    onChanged: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    }
  },
  runtime: {
    sendMessage: jest.fn((message, callback) => {
      if (callback) callback({ success: true });
    }),
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    getURL: jest.fn((path) => `chrome-extension://test-id/${path}`),
    lastError: null,
    id: 'test-extension-id',
    getManifest: jest.fn(() => ({ name: 'Test Extension', version: '1.0.0' })),
    reload: jest.fn(),
    onStartup: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onInstalled: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onSuspend: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onConnect: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    connect: jest.fn(() => ({ name: 'test-port' })),
    getBackgroundPage: jest.fn((callback) => {
      callback({ document: {} });
    }),
    openOptionsPage: jest.fn((callback) => {
      if (callback) callback();
    }),
    setUninstallURL: jest.fn((url, callback) => {
      if (callback) callback();
    }),
    getPlatformInfo: jest.fn((callback) => {
      callback({ os: 'mac', arch: 'x86-64' });
    }),
    getPackageDirectoryEntry: jest.fn((callback) => {
      callback({});
    })
  },
  tabs: {
    query: jest.fn((queryInfo, callback) => {
      callback([{ id: 1, url: 'https://example.com', title: 'Test Tab' }]);
    }),
    create: jest.fn((createProperties, callback) => {
      if (callback) callback({ id: 2, url: createProperties.url });
    }),
    update: jest.fn((tabId, updateProperties, callback) => {
      if (callback) callback({ id: tabId, ...updateProperties });
    }),
    sendMessage: jest.fn((tabId, message, callback) => {
      if (callback) callback({ success: true });
    }),
    get: jest.fn((tabId, callback) => {
      callback({ id: tabId, url: 'https://example.com', title: 'Test Tab' });
    }),
    remove: jest.fn((tabIds, callback) => {
      if (callback) callback();
    }),
    executeScript: jest.fn((tabId, injectDetails, callback) => {
      if (callback) callback([]);
    }),
    insertCSS: jest.fn((tabId, injectDetails, callback) => {
      if (callback) callback();
    }),
    removeCSS: jest.fn((tabId, removeDetails, callback) => {
      if (callback) callback();
    }),
    reload: jest.fn((tabId, reloadProperties, callback) => {
      if (callback) callback();
    }),
    duplicate: jest.fn((tabId, callback) => {
      if (callback) callback({ id: tabId + 1000 });
    }),
    onCreated: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onUpdated: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onRemoved: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onActivated: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    }
  },
  windows: {
    getCurrent: jest.fn((callback) => {
      callback({ id: 1, focused: true });
    })
  }
};

// 模拟全局对象
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve('')
  })
);

// 模拟 ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}));

// 模拟 IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}));

// 模拟 matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn()
  }))
});

// 模拟 localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// 模拟 sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
});

// 模拟 URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mocked-url');
global.URL.revokeObjectURL = jest.fn();

// 抑制 console 警告（在测试中）
const originalError = console.error;
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is deprecated')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

// 每个测试前重置所有模拟
beforeEach(() => {
  jest.clearAllMocks();
  
  // 重置Chrome API模拟
  chrome.runtime.lastError = null;
  
  // 重置localStorage模拟
  localStorageMock.getItem.mockClear();
  localStorageMock.setItem.mockClear();
  localStorageMock.removeItem.mockClear();
  localStorageMock.clear.mockClear();
  
  // 重置sessionStorage模拟
  sessionStorageMock.getItem.mockClear();
  sessionStorageMock.setItem.mockClear();
  sessionStorageMock.removeItem.mockClear();
  sessionStorageMock.clear.mockClear();
});

// 测试工具函数
global.testUtils = {
  // 等待异步操作完成
  waitFor: (callback, timeout = 1000) => {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const check = () => {
        try {
          const result = callback();
          if (result) {
            resolve(result);
          } else if (Date.now() - startTime > timeout) {
            reject(new Error('Timeout waiting for condition'));
          } else {
            setTimeout(check, 10);
          }
        } catch (error) {
          if (Date.now() - startTime > timeout) {
            reject(error);
          } else {
            setTimeout(check, 10);
          }
        }
      };
      check();
    });
  },
  
  // 模拟Chrome存储数据
  mockChromeStorage: (data) => {
    chrome.storage.local.get.mockImplementation((keys, callback) => {
      const result = {};
      if (Array.isArray(keys)) {
        keys.forEach(key => {
          result[key] = data[key] || null;
        });
      } else if (typeof keys === 'string') {
        result[keys] = data[keys] || null;
      } else {
        Object.assign(result, data);
      }
      callback(result);
    });
  },
  
  // 创建测试用的记忆数据
  createTestMemory: (overrides = {}) => ({
    id: 'test-memory-1',
    title: 'Test Memory',
    content: 'This is a test memory content',
    category: 'personal',
    tags: ['test', 'example'],
    createdAt: new Date('2023-01-01').toISOString(),
    lastModified: new Date('2023-01-01').toISOString(),
    ...overrides
  }),
  
  // 创建测试用的分类数据
  createTestCategory: (overrides = {}) => ({
    id: 'test-category-1',
    name: 'Test Category',
    color: '#1890ff',
    description: 'This is a test category',
    ...overrides
  })
};
