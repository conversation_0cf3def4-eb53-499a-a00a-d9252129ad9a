/**
 * 示例测试文件 - 验证测试框架配置是否正确
 */

describe('测试框架验证', () => {
  test('Jest 基本功能测试', () => {
    expect(1 + 1).toBe(2);
    expect('hello').toBe('hello');
    expect([1, 2, 3]).toEqual([1, 2, 3]);
  });

  test('异步测试支持', async () => {
    const asyncFunction = () => Promise.resolve('success');
    const result = await asyncFunction();
    expect(result).toBe('success');
  });

  test('模拟函数测试', () => {
    const mockFn = jest.fn();
    mockFn('test');
    
    expect(mockFn).toHaveBeenCalled();
    expect(mockFn).toHaveBeenCalledWith('test');
  });

  test('Chrome API 模拟测试', (done) => {
    chrome.storage.local.get(['test'], (result) => {
      expect(result).toEqual({ test: null });
      done();
    });
  });

  test('Chrome API Promise 包装测试', async () => {
    const getStorageData = (keys) => {
      return new Promise((resolve) => {
        chrome.storage.local.get(keys, resolve);
      });
    };

    const result = await getStorageData(['testKey']);
    expect(result).toEqual({ testKey: null });
  });

  test('测试工具函数验证', () => {
    const testMemory = global.testUtils.createTestMemory();
    
    expect(testMemory).toHaveProperty('id');
    expect(testMemory).toHaveProperty('title');
    expect(testMemory).toHaveProperty('content');
    expect(testMemory.title).toBe('Test Memory');
  });

  test('Chrome存储模拟工具测试', (done) => {
    const testData = { key1: 'value1', key2: 'value2' };
    global.testUtils.mockChromeStorage(testData);

    chrome.storage.local.get(['key1', 'key2'], (result) => {
      expect(result).toEqual(testData);
      done();
    });
  });
});

describe('环境配置验证', () => {
  test('jsdom 环境测试', () => {
    expect(typeof window).toBe('object');
    expect(typeof document).toBe('object');
    expect(typeof navigator).toBe('object');
  });

  test('localStorage 模拟测试', () => {
    localStorage.setItem('test', 'value');
    expect(localStorage.setItem).toHaveBeenCalledWith('test', 'value');
  });

  test('fetch 模拟测试', async () => {
    const response = await fetch('/test');
    expect(response.ok).toBe(true);
  });

  test('ResizeObserver 模拟测试', () => {
    const observer = new ResizeObserver(() => {});
    expect(observer.observe).toBeDefined();
    expect(observer.disconnect).toBeDefined();
  });
});

describe('错误处理测试', () => {
  test('同步错误捕获', () => {
    const throwError = () => {
      throw new Error('Test error');
    };

    expect(throwError).toThrow('Test error');
  });

  test('异步错误捕获', async () => {
    const asyncThrowError = async () => {
      throw new Error('Async test error');
    };

    await expect(asyncThrowError()).rejects.toThrow('Async test error');
  });

  test('Chrome API 错误模拟', (done) => {
    // 模拟Chrome API错误
    chrome.runtime.lastError = { message: 'Test error' };

    chrome.storage.local.get(['test'], (result) => {
      expect(chrome.runtime.lastError).toBeTruthy();
      expect(chrome.runtime.lastError.message).toBe('Test error');
      
      // 清理错误状态
      chrome.runtime.lastError = null;
      done();
    });
  });
});
