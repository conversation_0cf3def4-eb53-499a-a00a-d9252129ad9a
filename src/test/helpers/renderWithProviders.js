import React from 'react';
import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';

// 创建测试用的Redux store
export function createTestStore(preloadedState = {}) {
  return configureStore({
    reducer: {
      // 这里将在后续任务中添加实际的reducers
      memories: (state = { items: [], loading: false, error: null }, action) => {
        switch (action.type) {
          case 'memories/setMemories':
            return { ...state, items: action.payload };
          case 'memories/setLoading':
            return { ...state, loading: action.payload };
          case 'memories/setError':
            return { ...state, error: action.payload };
          default:
            return state;
        }
      },
      categories: (state = { items: [], loading: false, error: null }, action) => {
        switch (action.type) {
          case 'categories/setCategories':
            return { ...state, items: action.payload };
          default:
            return state;
        }
      },
      settings: (state = { user: null, cloudStorage: {}, sync: {} }, action) => {
        switch (action.type) {
          case 'settings/updateUser':
            return { ...state, user: action.payload };
          default:
            return state;
        }
      }
    },
    preloadedState,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false
      })
  });
}

// 带有Redux Provider的渲染函数
export function renderWithProviders(
  ui,
  {
    preloadedState = {},
    store = createTestStore(preloadedState),
    ...renderOptions
  } = {}
) {
  function Wrapper({ children }) {
    return <Provider store={store}>{children}</Provider>;
  }

  return {
    store,
    ...render(ui, { wrapper: Wrapper, ...renderOptions })
  };
}

// 创建模拟的应用服务
export function createMockApplicationServices() {
  return {
    memoryService: {
      createMemory: jest.fn(),
      updateMemory: jest.fn(),
      deleteMemory: jest.fn(),
      getMemory: jest.fn(),
      getMemories: jest.fn(),
      searchMemories: jest.fn()
    },
    categoryService: {
      createCategory: jest.fn(),
      updateCategory: jest.fn(),
      deleteCategory: jest.fn(),
      getCategories: jest.fn()
    },
    syncService: {
      performSync: jest.fn(),
      getSyncStatus: jest.fn(),
      configureSyncSettings: jest.fn()
    }
  };
}

// 创建模拟的存储适配器
export function createMockStorageAdapter() {
  const storage = new Map();
  
  return {
    get: jest.fn((keys) => {
      const result = {};
      if (Array.isArray(keys)) {
        keys.forEach(key => {
          result[key] = storage.get(key) || null;
        });
      } else if (typeof keys === 'string') {
        result[keys] = storage.get(keys) || null;
      }
      return Promise.resolve(result);
    }),
    
    set: jest.fn((items) => {
      Object.entries(items).forEach(([key, value]) => {
        storage.set(key, value);
      });
      return Promise.resolve();
    }),
    
    remove: jest.fn((keys) => {
      if (Array.isArray(keys)) {
        keys.forEach(key => storage.delete(key));
      } else {
        storage.delete(keys);
      }
      return Promise.resolve();
    }),
    
    clear: jest.fn(() => {
      storage.clear();
      return Promise.resolve();
    }),
    
    // 测试辅助方法
    _getStorage: () => storage,
    _setStorage: (key, value) => storage.set(key, value)
  };
}

// 创建模拟的依赖注入容器
export function createMockDIContainer() {
  const services = new Map();
  
  return {
    register: jest.fn((token, factory, options) => {
      services.set(token, { factory, options });
    }),
    
    resolve: jest.fn((token) => {
      const service = services.get(token);
      if (!service) {
        throw new Error(`Service ${String(token)} not registered`);
      }
      return service.factory();
    }),
    
    isRegistered: jest.fn((token) => {
      return services.has(token);
    }),
    
    // 测试辅助方法
    _getServices: () => services,
    _clear: () => services.clear()
  };
}

// 等待异步操作的工具函数
export async function waitForAsync() {
  await new Promise(resolve => setTimeout(resolve, 0));
}

// 模拟用户事件的工具函数
export function createMockUserEvent() {
  return {
    click: jest.fn(),
    type: jest.fn(),
    clear: jest.fn(),
    selectOptions: jest.fn(),
    upload: jest.fn()
  };
}

// 创建测试数据的工厂函数
export const testDataFactory = {
  memory: (overrides = {}) => ({
    id: `memory-${Date.now()}`,
    title: 'Test Memory',
    content: 'This is test content',
    category: 'personal',
    tags: ['test'],
    createdAt: new Date().toISOString(),
    lastModified: new Date().toISOString(),
    ...overrides
  }),
  
  category: (overrides = {}) => ({
    id: `category-${Date.now()}`,
    name: 'Test Category',
    color: '#1890ff',
    description: 'Test category description',
    ...overrides
  }),
  
  user: (overrides = {}) => ({
    id: `user-${Date.now()}`,
    name: 'Test User',
    email: '<EMAIL>',
    settings: {
      theme: 'light',
      language: 'zh-CN'
    },
    ...overrides
  })
};

// 断言辅助函数
export const testAssertions = {
  // 检查元素是否可见
  toBeVisible: (element) => {
    expect(element).toBeInTheDocument();
    expect(element).toBeVisible();
  },
  
  // 检查元素是否包含文本
  toHaveText: (element, text) => {
    expect(element).toHaveTextContent(text);
  },
  
  // 检查函数是否被调用
  toHaveBeenCalledWith: (mockFn, ...args) => {
    expect(mockFn).toHaveBeenCalledWith(...args);
  },
  
  // 检查异步操作是否完成
  toResolve: async (promise) => {
    await expect(promise).resolves.toBeDefined();
  },
  
  // 检查异步操作是否失败
  toReject: async (promise) => {
    await expect(promise).rejects.toBeDefined();
  }
};
