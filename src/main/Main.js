import React, { useState, useEffect } from 'react';
import { Layout, Menu, Typography, theme } from 'antd';
import { EditOutlined, BookOutlined, SettingOutlined, QuestionCircleOutlined, ReadOutlined } from '@ant-design/icons';

/**
 * 主页面逻辑
 * 处理导航菜单和内容页面的加载
 */

// 页面映射配置
const PAGES = {
  'add-memory': 'add-memory.html',
  'browse-memories': 'browse-memories.html',
  'options': 'options.html',
  'sync-settings': 'sync-settings.html',
  'cache-settings': 'cache-settings.html',
  'feedback': 'feedback.html'
};

// 默认页面
const DEFAULT_PAGE = 'browse-memories';

// 页面间通信事件
const EVENTS = {
  MEMORY_ADDED: 'memory_added',
  SETTINGS_CHANGED: 'settings_changed',
  THEME_CHANGED: 'theme_changed',
  NAVIGATE: 'navigate'
};

const Main = () => {
  // 当前活动页面
  const [activePage, setActivePage] = useState(null);

  /**
   * 初始化页面
   */
  useEffect(() => {
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const pageParam = urlParams.get('page');

    console.log(`URL参数页面: ${pageParam}`);

    // 加载指定页面或默认页面
    loadPage(pageParam || DEFAULT_PAGE);

    // 监听来自iframe的消息
    window.addEventListener('message', handleIframeMessage);

    // 监听存储变化
    chrome.storage.onChanged.addListener(handleStorageChange);

    return () => {
      // 清理事件监听器
      window.removeEventListener('message', handleIframeMessage);
      chrome.storage.onChanged.removeListener(handleStorageChange);
    };
  }, []);

  /**
   * 加载指定页面
   * @param {string} page - 页面标识符
   */
  const loadPage = (page) => {
    if (!PAGES[page]) {
      console.error(`未知页面: ${page}`);
      page = DEFAULT_PAGE;
    }

    console.log(`加载页面: ${page}`);

    // 更新活动页面
    setActivePage(page);

    // 加载iframe内容
    const iframe = document.getElementById('content-frame');
    if (iframe) {
      iframe.src = chrome.runtime.getURL(PAGES[page]);
    }

    // 更新URL参数，但不刷新页面
    const url = new URL(window.location.href);
    url.searchParams.set('page', page);
    window.history.pushState({}, '', url);
  };

  /**
   * 处理iframe消息
   * @param {MessageEvent} event - 消息事件
   */
  const handleIframeMessage = (event) => {
    // 确保消息来源是我们的iframe
    if (event.source !== document.getElementById('content-frame').contentWindow) {
      return;
    }

    const { type, data } = event.data;

    switch (type) {
      case EVENTS.MEMORY_ADDED:
        // 记忆添加成功后，跳转到浏览记忆页面
        loadPage('browse-memories');
        break;

      case EVENTS.SETTINGS_CHANGED:
        // 设置变更后，可能需要刷新当前页面
        // TODO: 实现设置变更后的刷新逻辑
        break;

      case EVENTS.THEME_CHANGED:
        // 主题变更后，更新UI
        // TODO: 实现主题变更逻辑
        break;

      case EVENTS.NAVIGATE:
        // 导航到指定页面
        if (data && data.page) {
          loadPage(data.page);
        }
        break;

      default:
        console.log('未处理的iframe消息类型:', type);
    }
  };

  /**
   * 处理存储变化
   * @param {object} changes - 变化对象
   * @param {string} areaName - 存储区域名称
   */
  const handleStorageChange = (changes, areaName) => {
    // 处理存储变化，例如设置更新
    if (areaName === 'local' && changes.settings) {
      // TODO: 实现设置变更后的处理逻辑
    }
  };

  /**
   * 处理菜单项点击
   * @param {string} page - 页面标识符
   */
  const handleMenuItemClick = (page) => {
    console.log(`菜单点击: ${page}`);
    if (page === 'documentation') {
      window.open('https://eversnip.github.io/EverSnip/', '_blank');
    } else {
      loadPage(page);
    }
  };

  const { token } = theme.useToken();

  // 菜单项配置
  const menuItems = [
    {
      key: 'add-memory',
      icon: <EditOutlined />,
      label: '添加记忆',
    },
    {
      key: 'browse-memories',
      icon: <BookOutlined />,
      label: '浏览记忆',
    },
    {
      key: 'options',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      key: 'feedback',
      icon: <QuestionCircleOutlined />,
      label: '问题反馈',
    },
    {
      key: 'documentation',
      icon: <ReadOutlined />,
      label: '使用文档',
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Layout.Sider
        width={200}
        style={{
          background: token.colorBgContainer,
          borderRight: `1px solid ${token.colorBorderSecondary}`,
          overflow: 'auto',
          height: '100vh',
        }}
      >
        <div style={{
          padding: '16px',
          display: 'flex',
          alignItems: 'center',
          borderBottom: `1px solid ${token.colorBorderSecondary}`,
        }}>
          <img src="../icons/icon48.png" alt="拾光忆栈" style={{ width: 32, height: 32, marginRight: 8 }} />
          <Typography.Title level={4} style={{ margin: 0 }}>拾光忆栈</Typography.Title>
        </div>
        <Menu
          mode="inline"
          selectedKeys={activePage ? [activePage] : []}
          style={{ height: 'calc(100% - 120px)', borderRight: 0 }}
          items={menuItems}
          onClick={({ key }) => handleMenuItemClick(key)}
        />
        <div style={{
          padding: '16px',
          borderTop: `1px solid ${token.colorBorderSecondary}`,
          textAlign: 'center',
          fontSize: '12px',
          color: token.colorTextSecondary,
          position: 'absolute',
          bottom: 0,
          width: '100%',
        }}>
          版本: {chrome.runtime.getManifest().version}
        </div>
      </Layout.Sider>

      <Layout.Content style={{ height: '100vh', position: 'relative' }}>
        <iframe
          id="content-frame"
          src=""
          frameBorder="0"
          style={{ width: '100%', height: '100%', border: 'none' }}
        />
      </Layout.Content>
    </Layout>
  );
};

export default Main;
