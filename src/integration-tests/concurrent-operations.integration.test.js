/**
 * 并发操作集成测试
 * 测试在并发环境下服务的数据一致性和锁机制
 */

import {
  createTestContainer,
  cleanupTestEnvironment,
  createTestMemories,
  delay,
  SERVICE_TYPES
} from './test-helpers.js';

describe('并发操作集成测试', () => {
  let container;
  let memoryService;
  let settingsService;
  let lockService;
  let cacheService;

  beforeEach(async () => {
    container = createTestContainer();
    memoryService = container.resolve(SERVICE_TYPES.MEMORY_STORAGE_SERVICE);
    settingsService = container.resolve(SERVICE_TYPES.SETTINGS_SERVICE);
    lockService = container.resolve(SERVICE_TYPES.LOCK_SERVICE);
    cacheService = container.resolve(SERVICE_TYPES.CACHE_SERVICE);
  });

  afterEach(async () => {
    await cleanupTestEnvironment(container);
  });

  describe('并发记忆操作', () => {
    test('应该正确处理并发创建记忆', async () => {
      const testData = createTestMemories(10);
      const results = [];

      // 并发创建记忆
      const promises = testData.map(async (data, index) => {
        await delay(Math.random() * 20); // 随机延迟模拟真实并发
        const memory = await memoryService.addMemory(data);
        results.push(memory);
        return memory;
      });

      const memories = await Promise.all(promises);

      // 验证所有记忆都被正确创建
      expect(memories).toHaveLength(10);
      expect(results).toHaveLength(10);

      // 验证ID的唯一性
      const ids = memories.map(m => m.id);
      expect(new Set(ids).size).toBe(ids.length);

      // 验证所有记忆都在存储中
      const allMemories = await memoryService.getMemories();
      expect(allMemories).toHaveLength(10);
    });

    test('应该正确处理并发更新同一记忆', async () => {
      const testData = createTestMemories(1)[0];
      const memory = await memoryService.addMemory(testData);

      // 并发更新同一记忆
      const updatePromises = Array.from({ length: 5 }, (_, index) => 
        memoryService.updateMemory(memory.id, {
          title: `更新标题 ${index}`,
          content: `更新内容 ${index}`
        })
      );

      const updatedMemories = await Promise.all(updatePromises);

      // 验证所有更新都成功
      expect(updatedMemories).toHaveLength(5);

      // 验证最终版本号是正确的
      const finalMemory = await memoryService.getMemory(memory.id);
      expect(finalMemory.version).toBe(memory.version + 5);

      // 验证最后一次更新的内容被保存
      const lastUpdate = updatedMemories[updatedMemories.length - 1];
      expect(finalMemory.title).toBe(lastUpdate.title);
      expect(finalMemory.content).toBe(lastUpdate.content);
    });

    test('应该正确处理并发删除和恢复操作', async () => {
      const testData = createTestMemories(3);
      const memories = [];

      // 创建测试记忆
      for (const data of testData) {
        const memory = await memoryService.addMemory(data);
        memories.push(memory);
      }

      // 并发删除和恢复操作
      const operations = [
        () => memoryService.deleteMemory(memories[0].id),
        () => memoryService.deleteMemory(memories[1].id),
        () => memoryService.restoreMemory(memories[0].id),
        () => memoryService.deleteMemory(memories[2].id),
        () => memoryService.restoreMemory(memories[1].id)
      ];

      await Promise.all(operations.map(op => op()));

      // 验证最终状态
      const finalMemories = await memoryService.getMemories(true);
      const memory0 = finalMemories.find(m => m.id === memories[0].id);
      const memory1 = finalMemories.find(m => m.id === memories[1].id);
      const memory2 = finalMemories.find(m => m.id === memories[2].id);

      expect(memory0.isDeleted).toBe(false); // 删除后恢复
      expect(memory1.isDeleted).toBe(false); // 删除后恢复
      expect(memory2.isDeleted).toBe(true);  // 只删除
    });

    test('应该正确处理并发搜索操作', async () => {
      const testData = [
        { title: 'JavaScript学习', content: 'JS基础', category: 'study', tags: ['JS'] },
        { title: 'Python项目', content: 'Python开发', category: 'work', tags: ['Python'] },
        { title: 'React组件', content: 'React开发', category: 'study', tags: ['React'] },
        { title: 'Node.js服务', content: 'Node后端', category: 'work', tags: ['Node'] }
      ];

      // 创建测试数据
      for (const data of testData) {
        await memoryService.addMemory(data);
      }

      // 并发搜索操作
      const searchPromises = [
        memoryService.searchMemories({ keyword: 'JavaScript' }),
        memoryService.searchMemories({ category: 'study' }),
        memoryService.searchMemories({ tags: ['Python'] }),
        memoryService.searchMemories({ keyword: 'React' }),
        memoryService.searchMemories({ category: 'work' })
      ];

      const searchResults = await Promise.all(searchPromises);

      // 验证搜索结果
      expect(searchResults[0].memories).toHaveLength(1); // JavaScript
      expect(searchResults[1].memories).toHaveLength(2); // study category
      expect(searchResults[2].memories).toHaveLength(1); // Python tag
      expect(searchResults[3].memories).toHaveLength(1); // React
      expect(searchResults[4].memories).toHaveLength(2); // work category
    });
  });

  describe('并发设置操作', () => {
    test('应该正确处理并发设置更新', async () => {
      // 并发更新不同的设置项
      const updatePromises = [
        settingsService.updateSetting('darkMode', true),
        settingsService.updateSetting('fontSize', 16),
        settingsService.updateSetting('language', 'en-US'),
        settingsService.updateSetting('autoSave', false),
        settingsService.updateSetting('enableNotifications', false)
      ];

      await Promise.all(updatePromises);

      // 验证所有设置都被正确更新
      const settings = await settingsService.getSettings();
      expect(settings.darkMode).toBe(true);
      expect(settings.fontSize).toBe(16);
      expect(settings.language).toBe('en-US');
      expect(settings.autoSave).toBe(false);
      expect(settings.enableNotifications).toBe(false);
      expect(settings.dataVersion).toBe(5); // 5次更新
    });

    test('应该正确处理并发分类操作', async () => {
      const categories = [
        { id: 'cat1', name: '分类1', color: '#ff0000' },
        { id: 'cat2', name: '分类2', color: '#00ff00' },
        { id: 'cat3', name: '分类3', color: '#0000ff' }
      ];

      // 并发添加分类
      const addPromises = categories.map(cat => 
        settingsService.addCategory(cat)
      );

      await Promise.all(addPromises);

      // 验证所有分类都被添加
      const allCategories = await settingsService.getCategories();
      expect(allCategories).toHaveLength(7); // 4个默认 + 3个新增

      // 并发更新分类
      const updatePromises = [
        settingsService.updateCategory('cat1', { name: '更新分类1' }),
        settingsService.updateCategory('cat2', { color: '#ffff00' }),
        settingsService.updateCategory('cat3', { name: '更新分类3', color: '#ff00ff' })
      ];

      await Promise.all(updatePromises);

      // 验证更新结果
      const cat1 = await settingsService.getCategory('cat1');
      const cat2 = await settingsService.getCategory('cat2');
      const cat3 = await settingsService.getCategory('cat3');

      expect(cat1.name).toBe('更新分类1');
      expect(cat2.color).toBe('#ffff00');
      expect(cat3.name).toBe('更新分类3');
      expect(cat3.color).toBe('#ff00ff');
    });
  });

  describe('锁机制测试', () => {
    test('应该正确处理锁的获取和释放', async () => {
      const lockKey = 'test-lock';
      const results = [];

      // 并发获取同一个锁
      const lockPromises = Array.from({ length: 5 }, async (_, index) => {
        const token = await lockService.acquire(lockKey);
        results.push(`acquired-${index}`);
        
        // 模拟一些工作
        await delay(10);
        
        await lockService.release(lockKey, token);
        results.push(`released-${index}`);
      });

      await Promise.all(lockPromises);

      // 验证所有锁都被正确获取和释放
      expect(results).toHaveLength(10); // 5次获取 + 5次释放
      
      // 验证获取和释放是成对出现的
      const acquiredCount = results.filter(r => r.startsWith('acquired')).length;
      const releasedCount = results.filter(r => r.startsWith('released')).length;
      expect(acquiredCount).toBe(5);
      expect(releasedCount).toBe(5);
    });

    test('应该正确处理锁超时', async () => {
      const lockKey = 'timeout-test-lock';
      
      // 获取锁但不释放
      const token1 = await lockService.acquire(lockKey, { timeout: 100 });
      
      // 尝试获取同一个锁（应该超时）
      const startTime = Date.now();
      
      try {
        await lockService.acquire(lockKey, { timeout: 50 });
        fail('应该抛出超时错误');
      } catch (error) {
        const elapsed = Date.now() - startTime;
        expect(error.message).toContain('获取锁超时');
        expect(elapsed).toBeGreaterThanOrEqual(50);
        expect(elapsed).toBeLessThan(100);
      }
      
      // 释放第一个锁
      await lockService.release(lockKey, token1);
      
      // 现在应该能够获取锁
      const token2 = await lockService.acquire(lockKey);
      expect(token2).toBeDefined();
      await lockService.release(lockKey, token2);
    });

    test('应该正确处理锁的强制释放', async () => {
      const lockKey = 'force-release-test';
      
      // 获取锁
      const token = await lockService.acquire(lockKey);
      
      // 验证锁被持有
      expect(await lockService.isLocked(lockKey)).toBe(true);
      
      // 强制释放锁
      await lockService.forceRelease(lockKey);
      
      // 验证锁被释放
      expect(await lockService.isLocked(lockKey)).toBe(false);
      
      // 应该能够重新获取锁
      const newToken = await lockService.acquire(lockKey);
      expect(newToken).toBeDefined();
      await lockService.release(lockKey, newToken);
    });
  });

  describe('数据一致性保证', () => {
    test('应该在高并发下保持数据一致性', async () => {
      const testData = createTestMemories(20);
      
      // 高并发创建、更新、删除操作
      const operations = [];
      
      // 创建操作
      testData.forEach((data, index) => {
        operations.push(async () => {
          await delay(Math.random() * 10);
          return memoryService.addMemory(data);
        });
      });
      
      // 执行所有操作
      const results = await Promise.all(operations.map(op => op()));
      
      // 验证数据一致性
      const allMemories = await memoryService.getMemories();
      expect(allMemories).toHaveLength(20);
      
      // 验证缓存和存储的一致性
      const cachedMemories = await cacheService.get('memories');
      expect(cachedMemories).toHaveLength(20);
      
      // 验证每个记忆都存在且数据正确
      results.forEach(memory => {
        const storedMemory = allMemories.find(m => m.id === memory.id);
        const cachedMemory = cachedMemories.find(m => m.id === memory.id);
        
        expect(storedMemory).toEqual(memory);
        expect(cachedMemory).toEqual(memory);
      });
    });

    test('应该正确处理并发操作中的错误恢复', async () => {
      const testData = createTestMemories(5);
      let errorCount = 0;
      
      // 模拟部分操作失败
      const originalAddMemory = memoryService.addMemory.bind(memoryService);
      memoryService.addMemory = async (data) => {
        if (data.title.includes('3') || data.title.includes('4')) {
          errorCount++;
          throw new Error('模拟错误');
        }
        return originalAddMemory(data);
      };
      
      // 并发执行操作（部分会失败）
      const promises = testData.map(async (data) => {
        try {
          return await memoryService.addMemory(data);
        } catch (error) {
          return null; // 失败的操作返回null
        }
      });
      
      const results = await Promise.all(promises);
      
      // 恢复原始方法
      memoryService.addMemory = originalAddMemory;
      
      // 验证错误处理
      expect(errorCount).toBe(2); // 应该有2个操作失败
      
      const successfulResults = results.filter(r => r !== null);
      expect(successfulResults).toHaveLength(3); // 3个操作成功
      
      // 验证成功的操作数据正确
      const allMemories = await memoryService.getMemories();
      expect(allMemories).toHaveLength(3);
    });
  });

  describe('性能和资源管理', () => {
    test('应该在高并发下保持良好性能', async () => {
      const startTime = Date.now();
      const concurrency = 50;
      
      // 创建大量并发操作
      const promises = Array.from({ length: concurrency }, async (_, index) => {
        const data = {
          title: `性能测试记忆 ${index}`,
          content: `内容 ${index}`,
          category: 'performance-test',
          tags: [`tag-${index}`]
        };
        
        return memoryService.addMemory(data);
      });
      
      const results = await Promise.all(promises);
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // 验证所有操作都成功
      expect(results).toHaveLength(concurrency);
      
      // 验证性能（应该在合理时间内完成）
      expect(duration).toBeLessThan(5000); // 5秒内完成
      
      // 验证数据完整性
      const allMemories = await memoryService.getMemories();
      expect(allMemories).toHaveLength(concurrency);
    });

    test('应该正确管理资源和清理', async () => {
      // 创建大量操作
      const operations = Array.from({ length: 100 }, (_, index) => 
        memoryService.addMemory({
          title: `资源测试 ${index}`,
          content: `内容 ${index}`,
          category: 'resource-test'
        })
      );
      
      await Promise.all(operations);
      
      // 验证服务仍然可用
      expect(await memoryService.isAvailable()).toBe(true);
      expect(await lockService.isAvailable()).toBe(true);
      expect(await cacheService.isAvailable()).toBe(true);
      
      // 验证可以继续正常操作
      const newMemory = await memoryService.addMemory({
        title: '资源测试后续操作',
        content: '验证系统仍然正常'
      });
      
      expect(newMemory).toBeDefined();
    });
  });
});
