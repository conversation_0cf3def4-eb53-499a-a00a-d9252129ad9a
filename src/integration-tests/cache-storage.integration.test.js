/**
 * 缓存存储集成测试
 * 测试缓存服务与存储服务的集成，验证数据一致性和性能优化
 */

import {
  createTestContainer,
  cleanupTestEnvironment,
  createTestMemories,
  delay,
  SERVICE_TYPES
} from './test-helpers.js';

describe('缓存存储集成测试', () => {
  let container;
  let memoryService;
  let settingsService;
  let cacheService;
  let storageRepository;

  beforeEach(async () => {
    container = createTestContainer();
    memoryService = container.resolve(SERVICE_TYPES.MEMORY_STORAGE_SERVICE);
    settingsService = container.resolve(SERVICE_TYPES.SETTINGS_SERVICE);
    cacheService = container.resolve(SERVICE_TYPES.CACHE_SERVICE);
    storageRepository = container.resolve(SERVICE_TYPES.STORAGE_REPOSITORY);
  });

  afterEach(async () => {
    await cleanupTestEnvironment(container);
  });

  describe('记忆缓存集成', () => {
    test('应该在创建记忆时更新缓存', async () => {
      const testData = createTestMemories(1)[0];

      // 缓存应该为空
      let cachedMemories = await cacheService.get('memories');
      expect(cachedMemories).toBeNull();

      // 创建记忆
      const memory = await memoryService.addMemory(testData);

      // 缓存应该被更新
      cachedMemories = await cacheService.get('memories');
      expect(cachedMemories).toBeTruthy();
      expect(Array.isArray(cachedMemories)).toBe(true);
      expect(cachedMemories).toHaveLength(1);
      expect(cachedMemories[0].id).toBe(memory.id);
    });

    test('应该在更新记忆时同步更新缓存', async () => {
      const testData = createTestMemories(1)[0];
      const memory = await memoryService.addMemory(testData);

      // 更新记忆
      const updates = { title: '更新后的标题' };
      const updatedMemory = await memoryService.updateMemory(memory.id, updates);

      // 验证缓存中的数据已更新
      const cachedMemories = await cacheService.get('memories');
      const cachedMemory = cachedMemories.find(m => m.id === memory.id);
      expect(cachedMemory.title).toBe(updates.title);
      expect(cachedMemory.version).toBe(updatedMemory.version);
    });

    test('应该在删除记忆时更新缓存', async () => {
      const testData = createTestMemories(2);
      const memories = [];
      
      for (const data of testData) {
        const memory = await memoryService.addMemory(data);
        memories.push(memory);
      }

      // 删除一个记忆
      await memoryService.deleteMemory(memories[0].id);

      // 验证缓存中的记忆被标记为已删除
      const cachedMemories = await cacheService.get('memories');
      const deletedMemory = cachedMemories.find(m => m.id === memories[0].id);
      expect(deletedMemory.isDeleted).toBe(true);
    });

    test('应该在缓存失效时从存储重新加载', async () => {
      const testData = createTestMemories(1)[0];
      const memory = await memoryService.addMemory(testData);

      // 验证缓存中有数据
      let cachedMemories = await cacheService.get('memories');
      expect(cachedMemories).toHaveLength(1);

      // 手动清除缓存
      await cacheService.delete('memories');
      cachedMemories = await cacheService.get('memories');
      expect(cachedMemories).toBeNull();

      // 获取记忆（应该从存储重新加载）
      const retrievedMemory = await memoryService.getMemory(memory.id);
      expect(retrievedMemory).toEqual(memory);

      // 验证缓存已重新填充
      cachedMemories = await cacheService.get('memories');
      expect(cachedMemories).toHaveLength(1);
    });

    test('应该正确处理缓存命名空间', async () => {
      const testData = createTestMemories(1)[0];
      await memoryService.addMemory(testData);

      // 验证记忆缓存
      const memoriesCache = await cacheService.get('memories');
      expect(memoriesCache).toBeTruthy();

      // 验证统计缓存（如果存在）
      const statsCache = await cacheService.get('memory-stats');
      // 统计缓存可能为空，这是正常的

      // 验证不同命名空间的缓存是独立的
      await cacheService.set('test-namespace', { test: 'data' });
      const testCache = await cacheService.get('test-namespace');
      expect(testCache).toEqual({ test: 'data' });

      // 记忆缓存不应该受影响
      const memoriesCacheAfter = await cacheService.get('memories');
      expect(memoriesCacheAfter).toEqual(memoriesCache);
    });
  });

  describe('设置缓存集成', () => {
    test('应该在获取设置时使用缓存', async () => {
      // 第一次获取设置（应该从存储加载并缓存）
      const settings1 = await settingsService.getSettings();

      // 验证缓存中有数据
      const cachedSettings = await cacheService.get('settings');
      expect(cachedSettings).toEqual(settings1);

      // 第二次获取设置（应该从缓存返回）
      const settings2 = await settingsService.getSettings();
      expect(settings2).toEqual(settings1);
    });

    test('应该在更新设置时同步更新缓存', async () => {
      // 获取初始设置
      await settingsService.getSettings();

      // 更新设置
      const updates = { darkMode: true, fontSize: 16 };
      await settingsService.saveSettings(updates);

      // 验证缓存中的设置已更新
      const cachedSettings = await cacheService.get('settings');
      expect(cachedSettings.darkMode).toBe(true);
      expect(cachedSettings.fontSize).toBe(16);
      expect(cachedSettings.dataVersion).toBe(1);
    });

    test('应该在缓存失效时重新加载设置', async () => {
      // 获取设置并缓存
      const originalSettings = await settingsService.getSettings();

      // 直接修改存储中的设置（模拟外部修改）
      await storageRepository.set({
        settings: {
          ...originalSettings,
          darkMode: true,
          externalModification: true
        }
      });

      // 清除缓存
      await cacheService.delete('settings');

      // 重新获取设置
      const reloadedSettings = await settingsService.getSettings();
      expect(reloadedSettings.darkMode).toBe(true);
      expect(reloadedSettings.externalModification).toBe(true);
    });
  });

  describe('缓存性能优化', () => {
    test('应该减少存储访问次数', async () => {
      let storageAccessCount = 0;
      
      // 包装存储方法以计数访问次数
      const originalGet = storageRepository.get.bind(storageRepository);
      storageRepository.get = async (...args) => {
        storageAccessCount++;
        return originalGet(...args);
      };

      // 多次获取相同数据
      await memoryService.getMemories();
      await memoryService.getMemories();
      await memoryService.getMemories();

      // 应该只访问存储一次（后续从缓存获取）
      expect(storageAccessCount).toBe(1);
    });

    test('应该正确处理缓存TTL', async () => {
      // 设置短TTL的缓存
      await cacheService.set('test-ttl', { data: 'test' }, { ttl: 50 }); // 50ms TTL

      // 立即获取应该有数据
      let cachedData = await cacheService.get('test-ttl');
      expect(cachedData).toEqual({ data: 'test' });

      // 等待TTL过期
      await delay(60);

      // 过期后应该没有数据
      cachedData = await cacheService.get('test-ttl');
      expect(cachedData).toBeNull();
    });

    test('应该正确处理缓存大小限制', async () => {
      // 创建大量数据测试缓存容量
      const largeData = Array.from({ length: 100 }, (_, i) => ({
        id: `item-${i}`,
        data: `large-data-${i}`.repeat(100)
      }));

      // 缓存大量数据
      for (let i = 0; i < largeData.length; i++) {
        await cacheService.set(`large-item-${i}`, largeData[i]);
      }

      // 验证缓存仍然可用
      expect(await cacheService.isAvailable()).toBe(true);

      // 验证可以获取最近缓存的数据
      const lastItem = await cacheService.get('large-item-99');
      expect(lastItem).toEqual(largeData[99]);
    });
  });

  describe('数据一致性', () => {
    test('应该保证缓存和存储的数据一致性', async () => {
      const testData = createTestMemories(3);
      const memories = [];

      // 创建多个记忆
      for (const data of testData) {
        const memory = await memoryService.addMemory(data);
        memories.push(memory);
      }

      // 从缓存获取数据
      const cachedMemories = await cacheService.get('memories');

      // 清除缓存，从存储获取数据
      await cacheService.delete('memories');
      const storageMemories = await memoryService.getMemories();

      // 验证数据一致性
      expect(storageMemories).toHaveLength(cachedMemories.length);
      
      storageMemories.forEach((storageMemory, index) => {
        const cachedMemory = cachedMemories.find(m => m.id === storageMemory.id);
        expect(cachedMemory).toEqual(storageMemory);
      });
    });

    test('应该在并发操作时保持数据一致性', async () => {
      const testData = createTestMemories(5);

      // 并发创建记忆
      const promises = testData.map(async (data, index) => {
        await delay(index * 5); // 小延迟模拟真实并发
        return memoryService.addMemory(data);
      });

      const memories = await Promise.all(promises);

      // 验证所有记忆都被正确创建和缓存
      const cachedMemories = await cacheService.get('memories');
      expect(cachedMemories).toHaveLength(5);

      // 验证每个记忆都在缓存中
      memories.forEach(memory => {
        const cachedMemory = cachedMemories.find(m => m.id === memory.id);
        expect(cachedMemory).toEqual(memory);
      });
    });

    test('应该正确处理缓存失败的情况', async () => {
      const testData = createTestMemories(1)[0];

      // 模拟缓存服务失败
      const originalSet = cacheService.set.bind(cacheService);
      cacheService.set = async () => {
        throw new Error('Cache service unavailable');
      };

      // 创建记忆应该仍然成功（即使缓存失败）
      const memory = await memoryService.addMemory(testData);
      expect(memory).toBeDefined();

      // 恢复缓存服务
      cacheService.set = originalSet;

      // 验证可以从存储获取数据
      const retrievedMemory = await memoryService.getMemory(memory.id);
      expect(retrievedMemory).toEqual(memory);
    });
  });

  describe('缓存清理和维护', () => {
    test('应该能够清理特定命名空间的缓存', async () => {
      // 创建不同命名空间的缓存数据
      await cacheService.set('memories', [{ id: '1' }]);
      await cacheService.set('settings', { theme: 'dark' });
      await cacheService.set('test-data', { value: 'test' });

      // 清理特定缓存
      await cacheService.delete('memories');

      // 验证只有指定缓存被清理
      expect(await cacheService.get('memories')).toBeNull();
      expect(await cacheService.get('settings')).toBeTruthy();
      expect(await cacheService.get('test-data')).toBeTruthy();
    });

    test('应该能够清理所有缓存', async () => {
      // 创建多个缓存数据
      await cacheService.set('memories', [{ id: '1' }]);
      await cacheService.set('settings', { theme: 'dark' });
      await cacheService.set('test-data', { value: 'test' });

      // 清理所有缓存
      await cacheService.clear();

      // 验证所有缓存都被清理
      expect(await cacheService.get('memories')).toBeNull();
      expect(await cacheService.get('settings')).toBeNull();
      expect(await cacheService.get('test-data')).toBeNull();
    });
  });
});
