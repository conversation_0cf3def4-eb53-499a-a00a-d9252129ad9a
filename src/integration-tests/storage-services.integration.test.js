/**
 * 存储服务集成测试
 * 测试MemoryStorageService和SettingsService与其依赖服务的集成
 */

import {
  createTestContainer,
  cleanupTestEnvironment,
  createTestMemories,
  assertMemoryStructure,
  assertSettingsStructure,
  delay,
  SERVICE_TYPES
} from './test-helpers.js';

describe('存储服务集成测试', () => {
  let container;
  let memoryService;
  let settingsService;
  let cacheService;
  let lockService;

  beforeEach(async () => {
    container = createTestContainer();
    memoryService = container.resolve(SERVICE_TYPES.MEMORY_STORAGE_SERVICE);
    settingsService = container.resolve(SERVICE_TYPES.SETTINGS_SERVICE);
    cacheService = container.resolve(SERVICE_TYPES.CACHE_SERVICE);
    lockService = container.resolve(SERVICE_TYPES.LOCK_SERVICE);
  });

  afterEach(async () => {
    await cleanupTestEnvironment(container);
  });

  describe('记忆存储服务集成', () => {
    test('应该能够完成记忆的完整生命周期', async () => {
      const testData = createTestMemories(1)[0];

      // 1. 创建记忆
      const createdMemory = await memoryService.addMemory(testData);
      assertMemoryStructure(createdMemory);
      expect(createdMemory.title).toBe(testData.title);
      expect(createdMemory.content).toBe(testData.content);

      // 2. 读取记忆
      const retrievedMemory = await memoryService.getMemory(createdMemory.id);
      expect(retrievedMemory).toEqual(createdMemory);

      // 3. 更新记忆
      const updates = { title: '更新后的标题', content: '更新后的内容' };
      const updatedMemory = await memoryService.updateMemory(createdMemory.id, updates);
      expect(updatedMemory.title).toBe(updates.title);
      expect(updatedMemory.content).toBe(updates.content);
      expect(updatedMemory.version).toBe(createdMemory.version + 1);

      // 4. 软删除记忆
      const deletedMemory = await memoryService.deleteMemory(createdMemory.id);
      expect(deletedMemory.isDeleted).toBe(true);

      // 5. 恢复记忆
      const restoredMemory = await memoryService.restoreMemory(createdMemory.id);
      expect(restoredMemory.isDeleted).toBe(false);

      // 6. 永久删除记忆
      await memoryService.permanentDeleteMemory(createdMemory.id);
      const finalMemory = await memoryService.getMemory(createdMemory.id);
      expect(finalMemory).toBeNull();
    });

    test('应该正确处理缓存和存储的一致性', async () => {
      const testData = createTestMemories(1)[0];

      // 创建记忆
      const memory = await memoryService.addMemory(testData);

      // 验证缓存中有数据
      const cachedMemories = await cacheService.get('memories');
      expect(cachedMemories).toBeTruthy();
      expect(Array.isArray(cachedMemories)).toBe(true);

      // 清除缓存
      await cacheService.delete('memories');

      // 再次获取记忆（应该从存储中读取）
      const retrievedMemory = await memoryService.getMemory(memory.id);
      expect(retrievedMemory).toEqual(memory);

      // 验证缓存已重新填充
      const newCachedMemories = await cacheService.get('memories');
      expect(newCachedMemories).toBeTruthy();
    });

    test('应该正确处理并发操作的锁机制', async () => {
      const testData = createTestMemories(2);
      const results = [];

      // 并发创建多个记忆
      const promises = testData.map(async (data, index) => {
        await delay(index * 10); // 添加小延迟模拟真实并发
        const memory = await memoryService.addMemory(data);
        results.push(memory);
        return memory;
      });

      await Promise.all(promises);

      // 验证所有记忆都被正确创建
      expect(results).toHaveLength(2);
      results.forEach(memory => {
        assertMemoryStructure(memory);
      });

      // 验证记忆ID的唯一性
      const ids = results.map(m => m.id);
      expect(new Set(ids).size).toBe(ids.length);
    });

    test('应该正确处理批量操作', async () => {
      const testData = createTestMemories(3);

      // 批量创建记忆
      const memories = [];
      for (const data of testData) {
        const memory = await memoryService.addMemory(data);
        memories.push(memory);
      }

      // 批量获取记忆
      const ids = memories.map(m => m.id);
      const retrievedMemories = await memoryService.getMemories();
      expect(retrievedMemories).toHaveLength(3);

      // 批量删除记忆
      await memoryService.deleteMemories(ids);
      const deletedMemories = await memoryService.getMemories(true); // 包含已删除
      deletedMemories.forEach(memory => {
        expect(memory.isDeleted).toBe(true);
      });
    });

    test('应该正确处理搜索和分页', async () => {
      const testData = [
        { title: 'JavaScript学习', content: '学习JS基础', category: 'study', tags: ['JS', '编程'] },
        { title: 'Python项目', content: '开发Python应用', category: 'work', tags: ['Python', '项目'] },
        { title: 'React组件', content: '创建React组件', category: 'study', tags: ['React', '前端'] }
      ];

      // 创建测试记忆
      for (const data of testData) {
        await memoryService.addMemory(data);
      }

      // 关键词搜索
      const jsResults = await memoryService.searchMemories({ keyword: 'JavaScript' });
      expect(jsResults.memories).toHaveLength(1);
      expect(jsResults.memories[0].title).toBe('JavaScript学习');

      // 分类搜索
      const studyResults = await memoryService.searchMemories({ category: 'study' });
      expect(studyResults.memories).toHaveLength(2);

      // 标签搜索
      const programmingResults = await memoryService.searchMemories({ tags: ['编程'] });
      expect(programmingResults.memories).toHaveLength(1);

      // 分页测试
      const pagedResults = await memoryService.searchMemories({}, { page: 1, pageSize: 2 });
      expect(pagedResults.memories).toHaveLength(2);
      expect(pagedResults.pagination.total).toBe(3);
      expect(pagedResults.pagination.totalPages).toBe(2);
    });
  });

  describe('设置服务集成', () => {
    test('应该能够完成设置的完整管理流程', async () => {
      // 1. 获取默认设置
      const defaultSettings = await settingsService.getSettings();
      assertSettingsStructure(defaultSettings);
      expect(defaultSettings.categories).toHaveLength(4); // 默认4个分类

      // 2. 更新设置
      const newSettings = {
        darkMode: true,
        fontSize: 16,
        language: 'en-US'
      };
      await settingsService.saveSettings(newSettings);

      // 3. 验证设置已更新
      const updatedSettings = await settingsService.getSettings();
      expect(updatedSettings.darkMode).toBe(true);
      expect(updatedSettings.fontSize).toBe(16);
      expect(updatedSettings.language).toBe('en-US');
      expect(updatedSettings.dataVersion).toBe(1);

      // 4. 更新特定设置项
      await settingsService.updateSetting('autoSave', false);
      const finalSettings = await settingsService.getSettings();
      expect(finalSettings.autoSave).toBe(false);
      expect(finalSettings.dataVersion).toBe(2);
    });

    test('应该正确处理分类管理', async () => {
      // 添加新分类
      const newCategory = {
        id: 'test-category',
        name: '测试分类',
        color: '#ff5722',
        icon: 'TestIcon'
      };
      await settingsService.addCategory(newCategory);

      // 验证分类已添加
      const categories = await settingsService.getCategories();
      expect(categories).toHaveLength(5); // 4个默认 + 1个新增
      
      const addedCategory = await settingsService.getCategory('test-category');
      expect(addedCategory).toEqual(newCategory);

      // 更新分类
      await settingsService.updateCategory('test-category', { name: '更新的分类' });
      const updatedCategory = await settingsService.getCategory('test-category');
      expect(updatedCategory.name).toBe('更新的分类');

      // 删除分类
      await settingsService.deleteCategory('test-category');
      const deletedCategory = await settingsService.getCategory('test-category');
      expect(deletedCategory).toBeNull();
    });

    test('应该正确处理设置的导入导出', async () => {
      // 修改一些设置
      await settingsService.saveSettings({ darkMode: true, fontSize: 18 });
      await settingsService.addCategory({
        id: 'export-test',
        name: '导出测试',
        color: '#9c27b0'
      });

      // 导出设置
      const exportedData = await settingsService.exportSettings();
      expect(exportedData).toHaveProperty('version');
      expect(exportedData).toHaveProperty('timestamp');
      expect(exportedData).toHaveProperty('settings');
      expect(exportedData.settings.darkMode).toBe(true);
      expect(exportedData.settings.fontSize).toBe(18);

      // 重置设置
      const defaultSettings = settingsService.getDefaultSettings();
      await settingsService.saveSettings(defaultSettings);

      // 导入设置
      await settingsService.importSettings(exportedData);
      const importedSettings = await settingsService.getSettings();
      expect(importedSettings.darkMode).toBe(true);
      expect(importedSettings.fontSize).toBe(18);
      
      const importedCategory = await settingsService.getCategory('export-test');
      expect(importedCategory).toBeTruthy();
      expect(importedCategory.name).toBe('导出测试');
    });

    test('应该正确处理缓存机制', async () => {
      // 获取设置（应该缓存）
      await settingsService.getSettings();
      
      // 验证缓存中有数据
      const cachedSettings = await cacheService.get('settings');
      expect(cachedSettings).toBeTruthy();

      // 清除缓存
      await cacheService.delete('settings');

      // 再次获取设置（应该从存储重新加载并缓存）
      const settings = await settingsService.getSettings();
      assertSettingsStructure(settings);

      // 验证缓存已重新填充
      const newCachedSettings = await cacheService.get('settings');
      expect(newCachedSettings).toEqual(settings);
    });
  });

  describe('服务间协作', () => {
    test('记忆服务和设置服务应该能够协同工作', async () => {
      // 1. 添加自定义分类
      const customCategory = {
        id: 'integration-test',
        name: '集成测试',
        color: '#607d8b'
      };
      await settingsService.addCategory(customCategory);

      // 2. 使用自定义分类创建记忆
      const memoryData = {
        title: '集成测试记忆',
        content: '使用自定义分类的记忆',
        category: 'integration-test',
        tags: ['集成', '测试']
      };
      const memory = await memoryService.addMemory(memoryData);
      expect(memory.category).toBe('integration-test');

      // 3. 验证分类在设置中存在
      const category = await settingsService.getCategory('integration-test');
      expect(category).toEqual(customCategory);

      // 4. 按分类搜索记忆
      const searchResults = await memoryService.searchMemories({ 
        category: 'integration-test' 
      });
      expect(searchResults.memories).toHaveLength(1);
      expect(searchResults.memories[0].id).toBe(memory.id);
    });

    test('应该正确处理服务的可用性检查', async () => {
      // 检查所有服务的可用性
      expect(await memoryService.isAvailable()).toBe(true);
      expect(await settingsService.isAvailable()).toBe(true);
      expect(await cacheService.isAvailable()).toBe(true);
      expect(await lockService.isAvailable()).toBe(true);

      // 检查服务类型
      expect(memoryService.getType()).toBe('MemoryStorageService');
      expect(settingsService.getType()).toBe('SettingsService');
    });
  });
});
