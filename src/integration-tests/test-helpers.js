/**
 * 集成测试辅助工具
 * 提供测试环境设置、数据清理和断言工具
 */

import { DIContainer } from '../infrastructure/di/DIContainer.js';
import { MemoryCacheService } from '../infrastructure/cache/MemoryCacheService.js';
import { DistributedLockService } from '../infrastructure/locks/DistributedLockService.js';
import { ChromeStorageAdapter } from '../infrastructure/adapters/ChromeStorageAdapter.js';
import { ChromeStorageRepository } from '../infrastructure/repositories/ChromeStorageRepository.js';
import { MemoryStorageService } from '../application/services/MemoryStorageService.js';
import { SettingsService } from '../application/services/SettingsService.js';

// 服务类型符号
export const SERVICE_TYPES = {
  CACHE_SERVICE: Symbol('ICacheService'),
  LOCK_SERVICE: Symbol('ILockService'),
  STORAGE_ADAPTER: Symbol('IStorageAdapter'),
  STORAGE_REPOSITORY: Symbol('IStorageRepository'),
  MEMORY_STORAGE_SERVICE: Symbol('IMemoryStorageService'),
  SETTINGS_SERVICE: Symbol('ISettingsService')
};

/**
 * 创建测试用的依赖注入容器
 * @returns {DIContainer} 配置好的容器实例
 */
export function createTestContainer() {
  const container = new DIContainer();
  const deviceId = 'test-device-' + Date.now();

  // 注册缓存服务
  container.register(
    SERVICE_TYPES.CACHE_SERVICE,
    () => new MemoryCacheService(),
    { lifecycle: 'singleton' }
  );

  // 注册锁服务
  container.register(
    SERVICE_TYPES.LOCK_SERVICE,
    (container) => new DistributedLockService(container.resolve(SERVICE_TYPES.STORAGE_REPOSITORY)),
    { lifecycle: 'singleton', dependencies: [SERVICE_TYPES.STORAGE_REPOSITORY] }
  );

  // 注册存储适配器（使用内存模拟）
  container.register(
    SERVICE_TYPES.STORAGE_ADAPTER,
    () => createMockStorageAdapter(),
    { lifecycle: 'singleton' }
  );

  // 注册存储仓储（使用模拟实现）
  container.register(
    SERVICE_TYPES.STORAGE_REPOSITORY,
    (container) => createMockStorageRepository(container.resolve(SERVICE_TYPES.STORAGE_ADAPTER)),
    { lifecycle: 'singleton', dependencies: [SERVICE_TYPES.STORAGE_ADAPTER] }
  );

  // 注册记忆存储服务
  container.register(
    SERVICE_TYPES.MEMORY_STORAGE_SERVICE,
    (container) => new MemoryStorageService(
      container.resolve(SERVICE_TYPES.STORAGE_REPOSITORY),
      container.resolve(SERVICE_TYPES.CACHE_SERVICE),
      container.resolve(SERVICE_TYPES.LOCK_SERVICE),
      deviceId
    ),
    { 
      lifecycle: 'singleton', 
      dependencies: [
        SERVICE_TYPES.STORAGE_REPOSITORY,
        SERVICE_TYPES.CACHE_SERVICE,
        SERVICE_TYPES.LOCK_SERVICE
      ] 
    }
  );

  // 注册设置服务
  container.register(
    SERVICE_TYPES.SETTINGS_SERVICE,
    (container) => new SettingsService(
      container.resolve(SERVICE_TYPES.STORAGE_REPOSITORY),
      container.resolve(SERVICE_TYPES.CACHE_SERVICE),
      container.resolve(SERVICE_TYPES.LOCK_SERVICE),
      deviceId
    ),
    { 
      lifecycle: 'singleton', 
      dependencies: [
        SERVICE_TYPES.STORAGE_REPOSITORY,
        SERVICE_TYPES.CACHE_SERVICE,
        SERVICE_TYPES.LOCK_SERVICE
      ] 
    }
  );

  return container;
}

/**
 * 创建模拟的存储适配器
 * @returns {Object} 模拟的存储适配器
 */
function createMockStorageAdapter() {
  const storage = new Map();

  return {
    async get(keys) {
      if (typeof keys === 'string') {
        return { [keys]: storage.get(keys) };
      }

      if (Array.isArray(keys)) {
        const result = {};
        keys.forEach(key => {
          result[key] = storage.get(key);
        });
        return result;
      }

      if (keys === null || keys === undefined) {
        const result = {};
        storage.forEach((value, key) => {
          result[key] = value;
        });
        return result;
      }

      return {};
    },

    async set(items) {
      Object.entries(items).forEach(([key, value]) => {
        storage.set(key, value);
      });
    },

    async remove(keys) {
      const keysArray = Array.isArray(keys) ? keys : [keys];
      keysArray.forEach(key => storage.delete(key));
    },

    async clear() {
      storage.clear();
    },

    async getBytesInUse() {
      return JSON.stringify(Array.from(storage.entries())).length;
    },

    async isAvailable() {
      return true;
    },

    getType() {
      return 'MockStorageAdapter';
    }
  };
}

/**
 * 创建模拟的存储仓储
 * @param {Object} storageAdapter - 存储适配器
 * @returns {Object} 模拟的存储仓储
 */
function createMockStorageRepository(storageAdapter) {
  return {
    async get(keys) {
      return storageAdapter.get(keys);
    },

    async set(items) {
      return storageAdapter.set(items);
    },

    async remove(keys) {
      return storageAdapter.remove(keys);
    },

    async clear() {
      return storageAdapter.clear();
    },

    async getBytesInUse(keys) {
      return storageAdapter.getBytesInUse(keys);
    },

    async keys() {
      // 从存储适配器获取所有键
      const allData = await storageAdapter.get();
      return Object.keys(allData);
    },

    async isAvailable() {
      return storageAdapter.isAvailable();
    },

    getType() {
      return 'MockStorageRepository';
    }
  };
}

/**
 * 清理测试环境
 * @param {DIContainer} container - 容器实例
 */
export async function cleanupTestEnvironment(container) {
  try {
    // 检查容器是否已销毁
    if (container.disposed) {
      return;
    }

    // 清理缓存
    try {
      const cacheService = container.resolve(SERVICE_TYPES.CACHE_SERVICE);
      await cacheService.clear();
    } catch (error) {
      // 忽略缓存清理错误
    }

    // 清理存储
    try {
      const storageAdapter = container.resolve(SERVICE_TYPES.STORAGE_ADAPTER);
      await storageAdapter.clear();
    } catch (error) {
      // 忽略存储清理错误
    }

    // 销毁容器
    container.dispose();
  } catch (error) {
    // 静默处理清理错误
  }
}

/**
 * 创建测试记忆数据
 * @param {number} count - 创建数量
 * @returns {Array} 测试记忆数据数组
 */
export function createTestMemories(count = 3) {
  const memories = [];
  for (let i = 1; i <= count; i++) {
    memories.push({
      title: `测试记忆 ${i}`,
      content: `这是第 ${i} 个测试记忆的内容`,
      category: i % 2 === 0 ? 'work' : 'study',
      tags: [`标签${i}`, '测试'],
      url: `https://example.com/page${i}`
    });
  }
  return memories;
}

/**
 * 等待指定时间
 * @param {number} ms - 等待毫秒数
 * @returns {Promise} Promise对象
 */
export function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 断言记忆对象的基本结构
 * @param {Object} memory - 记忆对象
 */
export function assertMemoryStructure(memory) {
  expect(memory).toHaveProperty('id');
  expect(memory).toHaveProperty('title');
  expect(memory).toHaveProperty('content');
  expect(memory).toHaveProperty('category');
  expect(memory).toHaveProperty('tags');
  expect(memory).toHaveProperty('url');
  expect(memory).toHaveProperty('createdAt');
  expect(memory).toHaveProperty('lastModified');
  expect(memory).toHaveProperty('lastModifiedBy');
  expect(memory).toHaveProperty('version');
  expect(memory).toHaveProperty('isDeleted');
  
  expect(typeof memory.id).toBe('string');
  expect(typeof memory.title).toBe('string');
  expect(typeof memory.content).toBe('string');
  expect(Array.isArray(memory.tags)).toBe(true);
  expect(typeof memory.version).toBe('number');
  expect(typeof memory.isDeleted).toBe('boolean');
}

/**
 * 断言设置对象的基本结构
 * @param {Object} settings - 设置对象
 */
export function assertSettingsStructure(settings) {
  expect(settings).toHaveProperty('categories');
  expect(settings).toHaveProperty('defaultCategory');
  expect(settings).toHaveProperty('darkMode');
  expect(settings).toHaveProperty('fontSize');
  expect(settings).toHaveProperty('autoSave');
  expect(settings).toHaveProperty('language');
  
  expect(Array.isArray(settings.categories)).toBe(true);
  expect(typeof settings.defaultCategory).toBe('string');
  expect(typeof settings.darkMode).toBe('boolean');
  expect(typeof settings.fontSize).toBe('number');
  expect(typeof settings.autoSave).toBe('boolean');
  expect(typeof settings.language).toBe('string');
}
