/**
 * 依赖注入容器集成测试
 * 测试DIContainer与所有服务的集成和生命周期管理
 */

import {
  createTestContainer,
  cleanupTestEnvironment,
  SERVICE_TYPES
} from './test-helpers.js';

describe('依赖注入容器集成测试', () => {
  let container;

  beforeEach(() => {
    container = createTestContainer();
  });

  afterEach(async () => {
    await cleanupTestEnvironment(container);
  });

  describe('服务注册和解析', () => {
    test('应该能够解析所有注册的服务', () => {
      // 解析所有服务
      const cacheService = container.resolve(SERVICE_TYPES.CACHE_SERVICE);
      const lockService = container.resolve(SERVICE_TYPES.LOCK_SERVICE);
      const storageAdapter = container.resolve(SERVICE_TYPES.STORAGE_ADAPTER);
      const storageRepository = container.resolve(SERVICE_TYPES.STORAGE_REPOSITORY);
      const memoryService = container.resolve(SERVICE_TYPES.MEMORY_STORAGE_SERVICE);
      const settingsService = container.resolve(SERVICE_TYPES.SETTINGS_SERVICE);

      // 验证服务实例
      expect(cacheService).toBeDefined();
      expect(lockService).toBeDefined();
      expect(storageAdapter).toBeDefined();
      expect(storageRepository).toBeDefined();
      expect(memoryService).toBeDefined();
      expect(settingsService).toBeDefined();

      // 验证服务类型
      expect(cacheService.getType()).toBe('MemoryCacheService');
      expect(lockService.getType()).toBe('DistributedLockService');
      expect(storageAdapter.getType()).toBe('MockStorageAdapter');
      expect(storageRepository.getType()).toBe('MockStorageRepository');
      expect(memoryService.getType()).toBe('MemoryStorageService');
      expect(settingsService.getType()).toBe('SettingsService');
    });

    test('应该正确处理单例生命周期', () => {
      // 多次解析同一服务
      const service1 = container.resolve(SERVICE_TYPES.CACHE_SERVICE);
      const service2 = container.resolve(SERVICE_TYPES.CACHE_SERVICE);
      const service3 = container.resolve(SERVICE_TYPES.CACHE_SERVICE);

      // 验证是同一实例
      expect(service1).toBe(service2);
      expect(service2).toBe(service3);
    });

    test('应该正确处理依赖注入', () => {
      const lockService = container.resolve(SERVICE_TYPES.LOCK_SERVICE);
      const storageRepository = container.resolve(SERVICE_TYPES.STORAGE_REPOSITORY);

      // 验证锁服务使用了正确的存储仓储实例
      expect(lockService.storage).toBe(storageRepository);
    });

    test('应该正确处理复杂的依赖链', () => {
      const memoryService = container.resolve(SERVICE_TYPES.MEMORY_STORAGE_SERVICE);
      const storageRepository = container.resolve(SERVICE_TYPES.STORAGE_REPOSITORY);
      const cacheService = container.resolve(SERVICE_TYPES.CACHE_SERVICE);
      const lockService = container.resolve(SERVICE_TYPES.LOCK_SERVICE);

      // 验证记忆服务的依赖
      expect(memoryService.storageRepository).toBe(storageRepository);
      expect(memoryService.cacheService).toBe(cacheService);
      expect(memoryService.lockService).toBe(lockService);

      // 验证锁服务的依赖
      expect(lockService.storage).toBe(storageRepository);
    });
  });

  describe('子容器功能', () => {
    test('应该能够创建和使用子容器', () => {
      // 创建子容器
      const childContainer = container.createChild();

      // 子容器应该能够解析父容器的服务
      const parentCacheService = container.resolve(SERVICE_TYPES.CACHE_SERVICE);
      const childCacheService = childContainer.resolve(SERVICE_TYPES.CACHE_SERVICE);

      // 单例服务应该是同一实例
      expect(childCacheService).toBe(parentCacheService);
    });

    test('应该能够在子容器中注册作用域服务', () => {
      // 在父容器中注册作用域服务
      const TestService = class {
        constructor() {
          this.id = Math.random();
        }
        getType() { return 'TestService'; }
      };

      const TEST_SERVICE = Symbol('TestService');
      container.register(TEST_SERVICE, () => new TestService(), { lifecycle: 'scoped' });

      // 创建子容器
      const child1 = container.createChild();
      const child2 = container.createChild();

      // 在不同子容器中解析作用域服务
      const service1a = child1.resolve(TEST_SERVICE);
      const service1b = child1.resolve(TEST_SERVICE);
      const service2a = child2.resolve(TEST_SERVICE);

      // 同一子容器中应该是同一实例
      expect(service1a).toBe(service1b);
      
      // 不同子容器中应该是不同实例
      expect(service1a).not.toBe(service2a);
    });

    test('应该能够正确销毁子容器', () => {
      const childContainer = container.createChild();
      
      // 验证子容器可以正常工作
      const service = childContainer.resolve(SERVICE_TYPES.CACHE_SERVICE);
      expect(service).toBeDefined();

      // 销毁子容器
      childContainer.dispose();

      // 销毁后应该无法解析服务
      expect(() => {
        childContainer.resolve(SERVICE_TYPES.CACHE_SERVICE);
      }).toThrow('Container has been disposed');
    });
  });

  describe('错误处理', () => {
    test('应该正确处理未注册的服务', () => {
      const UNKNOWN_SERVICE = Symbol('UnknownService');

      expect(() => {
        container.resolve(UNKNOWN_SERVICE);
      }).toThrow('Service not registered');
    });

    test('应该正确检测循环依赖', () => {
      const ServiceA = class {
        constructor(serviceB) {
          this.serviceB = serviceB;
        }
      };

      const ServiceB = class {
        constructor(serviceA) {
          this.serviceA = serviceA;
        }
      };

      const SERVICE_A = Symbol('ServiceA');
      const SERVICE_B = Symbol('ServiceB');

      // 注册相互依赖的服务
      container.register(
        SERVICE_A,
        (container) => new ServiceA(container.resolve(SERVICE_B)),
        { dependencies: [SERVICE_B] }
      );

      container.register(
        SERVICE_B,
        (container) => new ServiceB(container.resolve(SERVICE_A)),
        { dependencies: [SERVICE_A] }
      );

      // 应该检测到循环依赖
      expect(() => {
        container.resolve(SERVICE_A);
      }).toThrow('Circular dependency detected');
    });

    test('应该正确处理无效的服务类型', () => {
      expect(() => {
        container.resolve('invalid-type');
      }).toThrow('Invalid service type');

      expect(() => {
        container.resolve(null);
      }).toThrow('Invalid service type');

      expect(() => {
        container.resolve(undefined);
      }).toThrow('Invalid service type');
    });
  });

  describe('容器生命周期', () => {
    test('应该能够正确销毁容器', () => {
      // 解析一些服务
      container.resolve(SERVICE_TYPES.CACHE_SERVICE);
      container.resolve(SERVICE_TYPES.MEMORY_STORAGE_SERVICE);

      // 销毁容器
      container.dispose();

      // 销毁后应该无法解析服务
      expect(() => {
        container.resolve(SERVICE_TYPES.CACHE_SERVICE);
      }).toThrow('Container has been disposed');
    });

    test('应该能够检查容器状态', () => {
      // 容器应该是活跃的
      expect(container.disposed).toBe(false);

      // 销毁容器
      container.dispose();

      // 容器应该被标记为已销毁
      expect(container.disposed).toBe(true);
    });
  });

  describe('服务实例管理', () => {
    test('应该正确管理单例服务的实例', () => {
      // 解析服务
      const service1 = container.resolve(SERVICE_TYPES.CACHE_SERVICE);
      const service2 = container.resolve(SERVICE_TYPES.LOCK_SERVICE);

      // 验证服务元数据中有实例
      const cacheMetadata = container.getServiceMetadata(SERVICE_TYPES.CACHE_SERVICE);
      expect(cacheMetadata.instance).toBe(service1);

      // 再次解析应该返回缓存的实例
      const service1Again = container.resolve(SERVICE_TYPES.CACHE_SERVICE);
      expect(service1Again).toBe(service1);
    });

    test('应该能够处理服务的可用性检查', async () => {
      const cacheService = container.resolve(SERVICE_TYPES.CACHE_SERVICE);
      const lockService = container.resolve(SERVICE_TYPES.LOCK_SERVICE);
      const memoryService = container.resolve(SERVICE_TYPES.MEMORY_STORAGE_SERVICE);
      const settingsService = container.resolve(SERVICE_TYPES.SETTINGS_SERVICE);

      // 所有服务都应该可用
      expect(await cacheService.isAvailable()).toBe(true);
      expect(await lockService.isAvailable()).toBe(true);
      expect(await memoryService.isAvailable()).toBe(true);
      expect(await settingsService.isAvailable()).toBe(true);
    });
  });

  describe('实际业务场景集成', () => {
    test('应该支持完整的业务流程', async () => {
      // 解析所需服务
      const memoryService = container.resolve(SERVICE_TYPES.MEMORY_STORAGE_SERVICE);
      const settingsService = container.resolve(SERVICE_TYPES.SETTINGS_SERVICE);

      // 1. 配置设置
      await settingsService.saveSettings({
        defaultCategory: 'integration-test',
        autoSave: true
      });

      // 2. 添加自定义分类
      await settingsService.addCategory({
        id: 'integration-test',
        name: '集成测试分类',
        color: '#4caf50'
      });

      // 3. 创建记忆
      const memory = await memoryService.addMemory({
        title: '集成测试记忆',
        content: '通过DI容器创建的记忆',
        category: 'integration-test',
        tags: ['DI', '集成测试']
      });

      // 4. 验证整个流程
      expect(memory).toBeDefined();
      expect(memory.category).toBe('integration-test');

      const settings = await settingsService.getSettings();
      expect(settings.defaultCategory).toBe('integration-test');

      const category = await settingsService.getCategory('integration-test');
      expect(category.name).toBe('集成测试分类');
    });
  });
});
