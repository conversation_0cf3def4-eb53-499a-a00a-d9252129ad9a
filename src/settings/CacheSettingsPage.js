import React, { useState, useEffect } from 'react';
import { Layout, Typography, Card, Form, Switch, InputNumber, Button, Slider, Space, Divider, Alert, Spin, Table, Tag } from 'antd';
import { ArrowLeftOutlined, ClearOutlined, SyncOutlined, DatabaseOutlined } from '@ant-design/icons';
import { storageService } from '../services';

const { Header, Content } = Layout;
const { Title, Paragraph, Text } = Typography;

const CacheSettingsPage = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [clearingCache, setClearingCache] = useState(false);
  const [cacheStats, setCacheStats] = useState(null);
  const [loadingStats, setLoadingStats] = useState(false);
  const [message, setMessage] = useState(null);

  // 初始化表单数据
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setLoading(true);
        const settings = await storageService.getSettings();
        
        // 如果没有缓存设置，创建默认设置
        if (!settings.cacheSettings) {
          settings.cacheSettings = {
            useIndexedDB: true,
            maxCacheSize: 500, // MB
            prefetchEnabled: true,
            compressionEnabled: true,
            cacheExpirationTimes: {
              memory: 7, // 天
              chunk: 3, // 天
              metadata: 1, // 小时
              index: 12, // 小时
              image: 30, // 天
              search_index: 7 // 天
            }
          };
          await storageService.saveSettings(settings);
        }
        
        // 设置表单初始值
        form.setFieldsValue({
          useIndexedDB: settings.cacheSettings.useIndexedDB,
          maxCacheSize: settings.cacheSettings.maxCacheSize,
          prefetchEnabled: settings.cacheSettings.prefetchEnabled,
          compressionEnabled: settings.cacheSettings.compressionEnabled,
          memoryExpiration: settings.cacheSettings.cacheExpirationTimes?.memory || 7,
          chunkExpiration: settings.cacheSettings.cacheExpirationTimes?.chunk || 3,
          metadataExpiration: settings.cacheSettings.cacheExpirationTimes?.metadata || 1,
          indexExpiration: settings.cacheSettings.cacheExpirationTimes?.index || 12,
          imageExpiration: settings.cacheSettings.cacheExpirationTimes?.image || 30,
          searchIndexExpiration: settings.cacheSettings.cacheExpirationTimes?.search_index || 7
        });
        
        // 加载缓存统计信息
        await loadCacheStats();
      } catch (error) {
        console.error('加载缓存设置失败:', error);
        setMessage({
          type: 'error',
          content: `加载缓存设置失败: ${error.message}`
        });
      } finally {
        setLoading(false);
      }
    };
    
    loadSettings();
  }, [form]);
  
  // 加载缓存统计信息
  const loadCacheStats = async () => {
    try {
      setLoadingStats(true);
      const stats = await storageService.getCacheStats();
      setCacheStats(stats);
    } catch (error) {
      console.error('加载缓存统计信息失败:', error);
      setMessage({
        type: 'error',
        content: `加载缓存统计信息失败: ${error.message}`
      });
    } finally {
      setLoadingStats(false);
    }
  };
  
  // 保存设置
  const handleSave = async (values) => {
    try {
      setSaving(true);
      
      // 获取当前设置
      const settings = await storageService.getSettings();
      
      // 更新缓存设置
      settings.cacheSettings = {
        useIndexedDB: values.useIndexedDB,
        maxCacheSize: values.maxCacheSize,
        prefetchEnabled: values.prefetchEnabled,
        compressionEnabled: values.compressionEnabled,
        cacheExpirationTimes: {
          memory: values.memoryExpiration,
          chunk: values.chunkExpiration,
          metadata: values.metadataExpiration,
          index: values.indexExpiration,
          image: values.imageExpiration,
          search_index: values.searchIndexExpiration
        }
      };
      
      // 保存设置
      await storageService.updateCacheSettings(settings.cacheSettings);
      
      setMessage({
        type: 'success',
        content: '缓存设置已保存'
      });
      
      // 重新加载缓存统计信息
      await loadCacheStats();
    } catch (error) {
      console.error('保存缓存设置失败:', error);
      setMessage({
        type: 'error',
        content: `保存缓存设置失败: ${error.message}`
      });
    } finally {
      setSaving(false);
    }
  };
  
  // 清空缓存
  const handleClearCache = async () => {
    try {
      setClearingCache(true);
      await storageService.clearCache();
      
      setMessage({
        type: 'success',
        content: '缓存已清空'
      });
      
      // 重新加载缓存统计信息
      await loadCacheStats();
    } catch (error) {
      console.error('清空缓存失败:', error);
      setMessage({
        type: 'error',
        content: `清空缓存失败: ${error.message}`
      });
    } finally {
      setClearingCache(false);
    }
  };
  
  // 返回上一页
  const goBack = () => {
    window.history.back();
  };
  
  // 准备缓存统计数据
  const prepareStatsData = () => {
    if (!cacheStats) return [];
    
    const data = [];
    
    // 添加总计行
    data.push({
      key: 'total',
      type: '总计',
      count: cacheStats.total || 0,
      size: formatSize(getTotalSize(cacheStats)),
      memoryCount: cacheStats.memoryCache?.total || 0
    });
    
    // 添加各类型行
    if (cacheStats.byType) {
      Object.entries(cacheStats.byType).forEach(([type, info]) => {
        data.push({
          key: type,
          type: formatCacheType(type),
          count: info.count || 0,
          size: formatSize(info.size || 0),
          memoryCount: cacheStats.memoryCache?.byType[type]?.count || 0
        });
      });
    }
    
    return data;
  };
  
  // 获取总大小
  const getTotalSize = (stats) => {
    if (!stats || !stats.byType) return 0;
    
    return Object.values(stats.byType).reduce((total, info) => {
      return total + (info.size || 0);
    }, 0);
  };
  
  // 格式化大小
  const formatSize = (bytes) => {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  // 格式化缓存类型
  const formatCacheType = (type) => {
    const typeMap = {
      'memory': '记忆',
      'chunk': '记忆块',
      'metadata': '元数据',
      'index': '索引',
      'image': '图片',
      'search_index': '搜索索引',
      'default': '默认'
    };
    
    return typeMap[type] || type;
  };
  
  // 表格列定义
  const columns = [
    {
      title: '缓存类型',
      dataIndex: 'type',
      key: 'type',
      render: (text, record) => (
        <span>
          {text}
          {record.key === 'total' && <Tag color="blue" style={{ marginLeft: 8 }}>总计</Tag>}
        </span>
      )
    },
    {
      title: '项目数量',
      dataIndex: 'count',
      key: 'count',
      sorter: (a, b) => a.count - b.count
    },
    {
      title: '内存缓存数量',
      dataIndex: 'memoryCount',
      key: 'memoryCount',
      sorter: (a, b) => a.memoryCount - b.memoryCount
    },
    {
      title: '估计大小',
      dataIndex: 'size',
      key: 'size'
    }
  ];
  
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 24px', display: 'flex', alignItems: 'center' }}>
        <Button
          type="text"
          icon={<ArrowLeftOutlined />}
          onClick={goBack}
          style={{ marginRight: '16px' }}
        />
        <Title level={3} style={{ margin: '0' }}>缓存设置</Title>
      </Header>
      
      <Content style={{ padding: '24px' }}>
        {message && (
          <Alert
            message={message.type === 'success' ? '成功' : '错误'}
            description={message.content}
            type={message.type}
            showIcon
            closable
            onClose={() => setMessage(null)}
            style={{ marginBottom: '20px' }}
          />
        )}
        
        <Card style={{ marginBottom: '20px' }}>
          <Spin spinning={loading}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              initialValues={{
                useIndexedDB: true,
                maxCacheSize: 500,
                prefetchEnabled: true,
                compressionEnabled: true,
                memoryExpiration: 7,
                chunkExpiration: 3,
                metadataExpiration: 1,
                indexExpiration: 12,
                imageExpiration: 30,
                searchIndexExpiration: 7
              }}
            >
              <Title level={4}>基本设置</Title>
              
              <Form.Item
                name="useIndexedDB"
                label="使用 IndexedDB 缓存"
                valuePropName="checked"
                extra="启用后将使用 IndexedDB 存储缓存，提供更大的存储空间和更好的性能。禁用后将使用 LocalStorage 存储缓存。"
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                name="maxCacheSize"
                label="最大缓存大小 (MB)"
                extra="设置缓存的最大大小，超过此大小将自动清理最旧的缓存。"
                rules={[{ required: true, message: '请输入最大缓存大小' }]}
              >
                <Slider
                  min={100}
                  max={1000}
                  step={50}
                  marks={{
                    100: '100MB',
                    500: '500MB',
                    1000: '1GB'
                  }}
                />
              </Form.Item>
              
              <Form.Item
                name="prefetchEnabled"
                label="启用预加载"
                valuePropName="checked"
                extra="启用后将预加载可能需要的数据，提高访问速度。"
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                name="compressionEnabled"
                label="启用压缩"
                valuePropName="checked"
                extra="启用后将压缩缓存数据，节省存储空间。"
              >
                <Switch />
              </Form.Item>
              
              <Divider />
              
              <Title level={4}>缓存过期时间</Title>
              <Paragraph>设置不同类型数据的缓存过期时间，过期后将自动从缓存中删除。</Paragraph>
              
              <Form.Item
                name="memoryExpiration"
                label="记忆缓存过期时间 (天)"
                rules={[{ required: true, message: '请输入记忆缓存过期时间' }]}
              >
                <InputNumber min={1} max={90} />
              </Form.Item>
              
              <Form.Item
                name="chunkExpiration"
                label="记忆块缓存过期时间 (天)"
                rules={[{ required: true, message: '请输入记忆块缓存过期时间' }]}
              >
                <InputNumber min={1} max={30} />
              </Form.Item>
              
              <Form.Item
                name="metadataExpiration"
                label="元数据缓存过期时间 (小时)"
                rules={[{ required: true, message: '请输入元数据缓存过期时间' }]}
              >
                <InputNumber min={1} max={48} />
              </Form.Item>
              
              <Form.Item
                name="indexExpiration"
                label="索引缓存过期时间 (小时)"
                rules={[{ required: true, message: '请输入索引缓存过期时间' }]}
              >
                <InputNumber min={1} max={72} />
              </Form.Item>
              
              <Form.Item
                name="imageExpiration"
                label="图片缓存过期时间 (天)"
                rules={[{ required: true, message: '请输入图片缓存过期时间' }]}
              >
                <InputNumber min={1} max={90} />
              </Form.Item>
              
              <Form.Item
                name="searchIndexExpiration"
                label="搜索索引缓存过期时间 (天)"
                rules={[{ required: true, message: '请输入搜索索引缓存过期时间' }]}
              >
                <InputNumber min={1} max={30} />
              </Form.Item>
              
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" loading={saving}>
                    保存设置
                  </Button>
                  <Button
                    danger
                    icon={<ClearOutlined />}
                    onClick={handleClearCache}
                    loading={clearingCache}
                  >
                    清空缓存
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Spin>
        </Card>
        
        <Card title="缓存统计">
          <Spin spinning={loadingStats}>
            <div style={{ marginBottom: '16px' }}>
              <Button
                icon={<SyncOutlined />}
                onClick={loadCacheStats}
                loading={loadingStats}
              >
                刷新统计
              </Button>
            </div>
            
            {cacheStats ? (
              <Table
                dataSource={prepareStatsData()}
                columns={columns}
                rowKey="key"
                pagination={false}
              />
            ) : (
              <Alert
                message="暂无缓存统计信息"
                type="info"
                showIcon
              />
            )}
          </Spin>
        </Card>
      </Content>
    </Layout>
  );
};

export default CacheSettingsPage;
