import React from 'react';
import { createRoot } from 'react-dom/client';
import { ConfigProvider, Layout, Typography, Breadcrumb } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import SyncSettingsPanel from '../components/SyncSettingsPanel';
import 'antd/dist/reset.css';

const { Header, Content, Footer } = Layout;
const { Title } = Typography;

// 初始化存储服务
import { storageService } from '../services';
storageService.initialize();

const SyncSettingsPage = () => {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 24px' }}>
        <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
          <Title level={3} style={{ margin: 0 }}>拾光忆栈</Title>
        </div>
      </Header>
      <Content style={{ padding: '0 24px', marginTop: 16 }}>
        <Breadcrumb style={{ margin: '16px 0' }}>
          <Breadcrumb.Item>
            <a href="options.html">设置</a>
          </Breadcrumb.Item>
          <Breadcrumb.Item>同步设置</Breadcrumb.Item>
        </Breadcrumb>
        <div style={{ background: '#fff', padding: 24, minHeight: 280 }}>
          <Title level={4}>同步设置</Title>
          <p>配置记忆数据的同步选项，包括增量同步、冲突解决和网络优化。</p>
          <SyncSettingsPanel />
        </div>
      </Content>
      <Footer style={{ textAlign: 'center' }}>
          拾光忆栈 ©{new Date().getFullYear()} 大规模记忆存储解决方案
      </Footer>
    </Layout>
  );
};

// 渲染应用
const container = document.getElementById('root');
const root = createRoot(container);
root.render(
  <ConfigProvider locale={zhCN}>
    <SyncSettingsPage />
  </ConfigProvider>
);
