/**
 * 记忆列表组件
 * 显示记忆列表，支持搜索、过滤、分页等功能
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  List,
  Card,
  Input,
  Select,
  Button,
  Space,
  Tag,
  Typography,
  Empty,
  Spin,
  Alert,
  Pagination,
  Tooltip,
  Dropdown,
  Modal,
  message
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  MoreOutlined,
  CalendarOutlined,
  TagOutlined,
  FolderOutlined
} from '@ant-design/icons';
import { useMemoryService, useSettingsService } from '../hooks/useServices.js';

const { Search } = Input;
const { Option } = Select;
const { Text, Paragraph } = Typography;

/**
 * 记忆列表组件
 */
const MemoryList = ({ onEdit, onView, onDelete }) => {
  // 服务hooks
  const {
    memories,
    loading,
    error,
    fetchMemories,
    deleteMemory,
    searchMemories
  } = useMemoryService();

  const {
    categories,
    fetchCategories
  } = useSettingsService();

  // 本地状态
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedTags, setSelectedTags] = useState([]);
  const [sortBy, setSortBy] = useState('lastModified');
  const [sortOrder, setSortOrder] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchResults, setSearchResults] = useState(null);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [memoryToDelete, setMemoryToDelete] = useState(null);

  // 初始化数据
  useEffect(() => {
    fetchMemories();
    fetchCategories();
  }, [fetchMemories, fetchCategories]);

  /**
   * 执行搜索
   */
  const performSearch = useCallback(async () => {
    const query = {
      keyword: searchQuery,
      category: selectedCategory,
      tags: selectedTags,
      sortBy,
      sortOrder
    };

    const pagination = {
      page: currentPage,
      pageSize
    };

    try {
      const result = await searchMemories(query, pagination);
      setSearchResults(result);
    } catch (err) {
      console.error('搜索失败:', err);
      message.error('搜索失败');
    }
  }, [searchQuery, selectedCategory, selectedTags, sortBy, sortOrder, currentPage, pageSize, searchMemories]);

  // 当搜索条件变化时执行搜索
  useEffect(() => {
    if (searchQuery || selectedCategory || selectedTags.length > 0) {
      performSearch();
    } else {
      setSearchResults(null);
      setCurrentPage(1);
    }
  }, [searchQuery, selectedCategory, selectedTags, sortBy, sortOrder, performSearch]);

  /**
   * 处理搜索输入
   */
  const handleSearch = (value) => {
    setSearchQuery(value);
    setCurrentPage(1);
  };

  /**
   * 处理分类过滤
   */
  const handleCategoryFilter = (value) => {
    setSelectedCategory(value);
    setCurrentPage(1);
  };

  /**
   * 处理标签过滤
   */
  const handleTagFilter = (value) => {
    setSelectedTags(value);
    setCurrentPage(1);
  };

  /**
   * 处理排序变化
   */
  const handleSortChange = (value) => {
    const [field, order] = value.split('-');
    setSortBy(field);
    setSortOrder(order);
    setCurrentPage(1);
  };

  /**
   * 处理分页变化
   */
  const handlePageChange = (page, size) => {
    setCurrentPage(page);
    if (size !== pageSize) {
      setPageSize(size);
    }
  };

  /**
   * 确认删除记忆
   */
  const confirmDelete = (memory) => {
    setMemoryToDelete(memory);
    setDeleteModalVisible(true);
  };

  /**
   * 执行删除
   */
  const handleDelete = async () => {
    if (!memoryToDelete) return;

    try {
      await deleteMemory(memoryToDelete.id);
      message.success('记忆已删除');
      setDeleteModalVisible(false);
      setMemoryToDelete(null);
      
      // 刷新列表
      if (searchResults) {
        performSearch();
      } else {
        fetchMemories();
      }
    } catch (err) {
      console.error('删除失败:', err);
      message.error('删除失败');
    }
  };

  /**
   * 获取分类信息
   */
  const getCategoryInfo = (categoryId) => {
    return categories.find(cat => cat.id === categoryId) || { name: categoryId, color: '#999' };
  };

  /**
   * 格式化日期
   */
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
  };

  /**
   * 渲染记忆项的操作菜单
   */
  const renderActionMenu = (memory) => {
    const items = [
      {
        key: 'view',
        icon: <EyeOutlined />,
        label: '查看',
        onClick: () => onView && onView(memory)
      },
      {
        key: 'edit',
        icon: <EditOutlined />,
        label: '编辑',
        onClick: () => onEdit && onEdit(memory)
      },
      {
        type: 'divider'
      },
      {
        key: 'delete',
        icon: <DeleteOutlined />,
        label: '删除',
        danger: true,
        onClick: () => confirmDelete(memory)
      }
    ];

    return (
      <Dropdown
        menu={{ items }}
        trigger={['click']}
        placement="bottomRight"
      >
        <Button type="text" icon={<MoreOutlined />} />
      </Dropdown>
    );
  };

  /**
   * 渲染记忆项
   */
  const renderMemoryItem = (memory) => {
    const categoryInfo = getCategoryInfo(memory.category);

    return (
      <List.Item key={memory.id}>
        <Card
          size="small"
          hoverable
          actions={[renderActionMenu(memory)]}
          style={{ width: '100%' }}
        >
          <Card.Meta
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text strong>{memory.title}</Text>
                <Space>
                  <Tag
                    color={categoryInfo.color}
                    icon={<FolderOutlined />}
                  >
                    {categoryInfo.name}
                  </Tag>
                </Space>
              </div>
            }
            description={
              <div>
                <Paragraph
                  ellipsis={{ rows: 2, expandable: false }}
                  style={{ marginBottom: 8 }}
                >
                  {memory.content}
                </Paragraph>
                
                {memory.tags && memory.tags.length > 0 && (
                  <div style={{ marginBottom: 8 }}>
                    <TagOutlined style={{ marginRight: 4, color: '#999' }} />
                    {memory.tags.map(tag => (
                      <Tag key={tag} size="small">{tag}</Tag>
                    ))}
                  </div>
                )}
                
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    <CalendarOutlined style={{ marginRight: 4 }} />
                    {formatDate(memory.lastModified)}
                  </Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    版本 {memory.version}
                  </Text>
                </div>
              </div>
            }
          />
        </Card>
      </List.Item>
    );
  };

  // 获取当前显示的记忆列表
  const currentMemories = searchResults ? searchResults.memories : memories.filter(m => !m.isDeleted);
  const pagination = searchResults ? searchResults.pagination : {
    total: currentMemories.length,
    page: currentPage,
    pageSize,
    totalPages: Math.ceil(currentMemories.length / pageSize)
  };

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <Alert
        message="加载失败"
        description={error.message}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={() => fetchMemories()}>
            重试
          </Button>
        }
      />
    );
  }

  return (
    <div>
      {/* 搜索和过滤栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Search
            placeholder="搜索记忆..."
            allowClear
            enterButton={<SearchOutlined />}
            size="large"
            onSearch={handleSearch}
            onChange={(e) => !e.target.value && handleSearch('')}
          />
          
          <Space wrap>
            <Select
              placeholder="选择分类"
              allowClear
              style={{ width: 120 }}
              value={selectedCategory || undefined}
              onChange={handleCategoryFilter}
            >
              {categories.map(category => (
                <Option key={category.id} value={category.id}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div
                      style={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: category.color,
                        marginRight: 8
                      }}
                    />
                    {category.name}
                  </div>
                </Option>
              ))}
            </Select>

            <Select
              mode="multiple"
              placeholder="选择标签"
              allowClear
              style={{ minWidth: 120 }}
              value={selectedTags}
              onChange={handleTagFilter}
            >
              {/* 这里可以从记忆中提取所有标签 */}
            </Select>

            <Select
              value={`${sortBy}-${sortOrder}`}
              onChange={handleSortChange}
              style={{ width: 140 }}
            >
              <Option value="lastModified-desc">最近修改</Option>
              <Option value="lastModified-asc">最早修改</Option>
              <Option value="createdAt-desc">最近创建</Option>
              <Option value="createdAt-asc">最早创建</Option>
              <Option value="title-asc">标题 A-Z</Option>
              <Option value="title-desc">标题 Z-A</Option>
            </Select>
          </Space>
        </Space>
      </Card>

      {/* 记忆列表 */}
      <Spin spinning={loading}>
        {currentMemories.length === 0 ? (
          <Empty
            description="暂无记忆"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <>
            <List
              dataSource={currentMemories}
              renderItem={renderMemoryItem}
              pagination={false}
            />
            
            {pagination.total > pageSize && (
              <div style={{ textAlign: 'center', marginTop: 16 }}>
                <Pagination
                  current={pagination.page}
                  total={pagination.total}
                  pageSize={pagination.pageSize}
                  showSizeChanger
                  showQuickJumper
                  showTotal={(total, range) => 
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条记忆`
                  }
                  onChange={handlePageChange}
                />
              </div>
            )}
          </>
        )}
      </Spin>

      {/* 删除确认对话框 */}
      <Modal
        title="确认删除"
        open={deleteModalVisible}
        onOk={handleDelete}
        onCancel={() => {
          setDeleteModalVisible(false);
          setMemoryToDelete(null);
        }}
        okText="删除"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        <p>确定要删除记忆 "{memoryToDelete?.title}" 吗？</p>
        <p style={{ color: '#999', fontSize: '14px' }}>
          删除后可以在回收站中恢复。
        </p>
      </Modal>
    </div>
  );
};

export default MemoryList;
