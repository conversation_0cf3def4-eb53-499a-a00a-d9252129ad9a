/**
 * 服务管理 Hook
 * 为React组件提供统一的服务访问接口
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import frontendServiceContainer from '../services/FrontendServiceContainer.js';

/**
 * 使用服务的Hook
 * @returns {Object} 服务状态和方法
 */
export const useServices = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isAvailable, setIsAvailable] = useState(false);
  const initializationRef = useRef(false);

  /**
   * 初始化服务
   */
  const initializeServices = useCallback(async () => {
    if (initializationRef.current) {
      return;
    }

    initializationRef.current = true;
    setIsLoading(true);
    setError(null);

    try {
      await frontendServiceContainer.initialize();
      const available = await frontendServiceContainer.isAvailable();
      
      setIsInitialized(true);
      setIsAvailable(available);
      console.log('服务初始化成功');
    } catch (err) {
      console.error('服务初始化失败:', err);
      setError(err);
      setIsInitialized(false);
      setIsAvailable(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * 重新初始化服务
   */
  const reinitializeServices = useCallback(async () => {
    initializationRef.current = false;
    frontendServiceContainer.dispose();
    await initializeServices();
  }, [initializeServices]);

  /**
   * 检查服务可用性
   */
  const checkAvailability = useCallback(async () => {
    if (!isInitialized) {
      return false;
    }

    try {
      const available = await frontendServiceContainer.isAvailable();
      setIsAvailable(available);
      return available;
    } catch (err) {
      console.error('检查服务可用性失败:', err);
      setIsAvailable(false);
      return false;
    }
  }, [isInitialized]);

  // 组件挂载时初始化服务
  useEffect(() => {
    initializeServices();

    // 组件卸载时清理
    return () => {
      if (initializationRef.current) {
        frontendServiceContainer.dispose();
      }
    };
  }, [initializeServices]);

  return {
    // 状态
    isInitialized,
    isLoading,
    error,
    isAvailable,

    // 方法
    initializeServices,
    reinitializeServices,
    checkAvailability,

    // 服务访问器
    getMemoryService: () => frontendServiceContainer.getMemoryService(),
    getSettingsService: () => frontendServiceContainer.getSettingsService(),
    getCacheService: () => frontendServiceContainer.getCacheService(),
    getLockService: () => frontendServiceContainer.getLockService(),
    getDeviceId: () => frontendServiceContainer.getDeviceId()
  };
};

/**
 * 使用记忆服务的Hook
 * @returns {Object} 记忆服务状态和方法
 */
export const useMemoryService = () => {
  const { isInitialized, getMemoryService } = useServices();
  const [memories, setMemories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * 获取所有记忆
   */
  const fetchMemories = useCallback(async (includeDeleted = false) => {
    if (!isInitialized) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const memoryService = getMemoryService();
      const result = await memoryService.getMemories(includeDeleted);
      setMemories(result);
      return result;
    } catch (err) {
      console.error('获取记忆失败:', err);
      setError(err);
      return [];
    } finally {
      setLoading(false);
    }
  }, [isInitialized, getMemoryService]);

  /**
   * 添加记忆
   */
  const addMemory = useCallback(async (memoryData) => {
    if (!isInitialized) {
      throw new Error('服务未初始化');
    }

    setLoading(true);
    setError(null);

    try {
      const memoryService = getMemoryService();
      const newMemory = await memoryService.addMemory(memoryData);
      
      // 更新本地状态
      setMemories(prev => [newMemory, ...prev]);
      
      return newMemory;
    } catch (err) {
      console.error('添加记忆失败:', err);
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [isInitialized, getMemoryService]);

  /**
   * 更新记忆
   */
  const updateMemory = useCallback(async (id, updates) => {
    if (!isInitialized) {
      throw new Error('服务未初始化');
    }

    setLoading(true);
    setError(null);

    try {
      const memoryService = getMemoryService();
      const updatedMemory = await memoryService.updateMemory(id, updates);
      
      // 更新本地状态
      setMemories(prev => prev.map(memory => 
        memory.id === id ? updatedMemory : memory
      ));
      
      return updatedMemory;
    } catch (err) {
      console.error('更新记忆失败:', err);
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [isInitialized, getMemoryService]);

  /**
   * 删除记忆
   */
  const deleteMemory = useCallback(async (id) => {
    if (!isInitialized) {
      throw new Error('服务未初始化');
    }

    setLoading(true);
    setError(null);

    try {
      const memoryService = getMemoryService();
      await memoryService.deleteMemory(id);
      
      // 更新本地状态
      setMemories(prev => prev.map(memory => 
        memory.id === id ? { ...memory, isDeleted: true } : memory
      ));
      
      return true;
    } catch (err) {
      console.error('删除记忆失败:', err);
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [isInitialized, getMemoryService]);

  /**
   * 搜索记忆
   */
  const searchMemories = useCallback(async (query, pagination) => {
    if (!isInitialized) {
      return { memories: [], pagination: { total: 0, page: 1, pageSize: 10, totalPages: 0 } };
    }

    setLoading(true);
    setError(null);

    try {
      const memoryService = getMemoryService();
      const result = await memoryService.searchMemories(query, pagination);
      return result;
    } catch (err) {
      console.error('搜索记忆失败:', err);
      setError(err);
      return { memories: [], pagination: { total: 0, page: 1, pageSize: 10, totalPages: 0 } };
    } finally {
      setLoading(false);
    }
  }, [isInitialized, getMemoryService]);

  return {
    // 状态
    memories,
    loading,
    error,
    isInitialized,

    // 方法
    fetchMemories,
    addMemory,
    updateMemory,
    deleteMemory,
    searchMemories
  };
};

/**
 * 使用设置服务的Hook
 * @returns {Object} 设置服务状态和方法
 */
export const useSettingsService = () => {
  const { isInitialized, getSettingsService } = useServices();
  const [settings, setSettings] = useState(null);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * 获取设置
   */
  const fetchSettings = useCallback(async () => {
    if (!isInitialized) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const settingsService = getSettingsService();
      const result = await settingsService.getSettings();
      setSettings(result);
      return result;
    } catch (err) {
      console.error('获取设置失败:', err);
      setError(err);
      return null;
    } finally {
      setLoading(false);
    }
  }, [isInitialized, getSettingsService]);

  /**
   * 保存设置
   */
  const saveSettings = useCallback(async (newSettings) => {
    if (!isInitialized) {
      throw new Error('服务未初始化');
    }

    setLoading(true);
    setError(null);

    try {
      const settingsService = getSettingsService();
      await settingsService.saveSettings(newSettings);
      
      // 重新获取设置
      const updatedSettings = await settingsService.getSettings();
      setSettings(updatedSettings);
      
      return updatedSettings;
    } catch (err) {
      console.error('保存设置失败:', err);
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [isInitialized, getSettingsService]);

  /**
   * 获取分类
   */
  const fetchCategories = useCallback(async () => {
    if (!isInitialized) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const settingsService = getSettingsService();
      const result = await settingsService.getCategories();
      setCategories(result);
      return result;
    } catch (err) {
      console.error('获取分类失败:', err);
      setError(err);
      return [];
    } finally {
      setLoading(false);
    }
  }, [isInitialized, getSettingsService]);

  return {
    // 状态
    settings,
    categories,
    loading,
    error,
    isInitialized,

    // 方法
    fetchSettings,
    saveSettings,
    fetchCategories
  };
};
