/**
 * 前端服务容器
 * 为前端组件提供统一的服务访问接口
 */

import { DIContainer } from '../../infrastructure/di/DIContainer.js';
import { MemoryCacheService } from '../../infrastructure/cache/MemoryCacheService.js';
import { DistributedLockService } from '../../infrastructure/locks/DistributedLockService.js';
import { ChromeStorageAdapter } from '../../infrastructure/adapters/ChromeStorageAdapter.js';
import { ChromeStorageRepository } from '../../infrastructure/repositories/ChromeStorageRepository.js';
import { MemoryStorageService } from '../../application/services/MemoryStorageService.js';
import { SettingsService } from '../../application/services/SettingsService.js';

// 服务类型符号
export const SERVICE_TYPES = {
  CACHE_SERVICE: Symbol('ICacheService'),
  LOCK_SERVICE: Symbol('ILockService'),
  STORAGE_ADAPTER: Symbol('IStorageAdapter'),
  STORAGE_REPOSITORY: Symbol('IStorageRepository'),
  MEMORY_STORAGE_SERVICE: Symbol('IMemoryStorageService'),
  SETTINGS_SERVICE: Symbol('ISettingsService')
};

/**
 * 前端服务容器类
 * 管理前端应用的所有服务实例
 */
class FrontendServiceContainer {
  constructor() {
    this.container = null;
    this.deviceId = null;
    this.initialized = false;
  }

  /**
   * 初始化服务容器
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      // 获取或生成设备ID
      this.deviceId = await this.getOrCreateDeviceId();

      // 创建依赖注入容器
      this.container = new DIContainer();

      // 注册服务
      this.registerServices();

      this.initialized = true;
      console.log('前端服务容器初始化成功');
    } catch (error) {
      console.error('前端服务容器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取或创建设备ID
   * @returns {Promise<string>}
   */
  async getOrCreateDeviceId() {
    try {
      const result = await chrome.storage.local.get(['deviceId']);
      if (result.deviceId) {
        return result.deviceId;
      }

      // 生成新的设备ID
      const deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      await chrome.storage.local.set({ deviceId });
      return deviceId;
    } catch (error) {
      console.error('获取设备ID失败:', error);
      // 回退到临时设备ID
      return `temp_device_${Date.now()}`;
    }
  }

  /**
   * 注册所有服务
   */
  registerServices() {
    // 注册缓存服务
    this.container.register(
      SERVICE_TYPES.CACHE_SERVICE,
      () => new MemoryCacheService(),
      { lifecycle: 'singleton' }
    );

    // 注册存储适配器
    this.container.register(
      SERVICE_TYPES.STORAGE_ADAPTER,
      () => new ChromeStorageAdapter(),
      { lifecycle: 'singleton' }
    );

    // 注册存储仓储
    this.container.register(
      SERVICE_TYPES.STORAGE_REPOSITORY,
      (container) => new ChromeStorageRepository(container.resolve(SERVICE_TYPES.STORAGE_ADAPTER)),
      { lifecycle: 'singleton', dependencies: [SERVICE_TYPES.STORAGE_ADAPTER] }
    );

    // 注册锁服务
    this.container.register(
      SERVICE_TYPES.LOCK_SERVICE,
      (container) => new DistributedLockService(container.resolve(SERVICE_TYPES.STORAGE_REPOSITORY)),
      { lifecycle: 'singleton', dependencies: [SERVICE_TYPES.STORAGE_REPOSITORY] }
    );

    // 注册记忆存储服务
    this.container.register(
      SERVICE_TYPES.MEMORY_STORAGE_SERVICE,
      (container) => new MemoryStorageService(
        container.resolve(SERVICE_TYPES.STORAGE_REPOSITORY),
        container.resolve(SERVICE_TYPES.CACHE_SERVICE),
        container.resolve(SERVICE_TYPES.LOCK_SERVICE),
        this.deviceId
      ),
      { 
        lifecycle: 'singleton', 
        dependencies: [
          SERVICE_TYPES.STORAGE_REPOSITORY,
          SERVICE_TYPES.CACHE_SERVICE,
          SERVICE_TYPES.LOCK_SERVICE
        ] 
      }
    );

    // 注册设置服务
    this.container.register(
      SERVICE_TYPES.SETTINGS_SERVICE,
      (container) => new SettingsService(
        container.resolve(SERVICE_TYPES.STORAGE_REPOSITORY),
        container.resolve(SERVICE_TYPES.CACHE_SERVICE),
        container.resolve(SERVICE_TYPES.LOCK_SERVICE),
        this.deviceId
      ),
      { 
        lifecycle: 'singleton', 
        dependencies: [
          SERVICE_TYPES.STORAGE_REPOSITORY,
          SERVICE_TYPES.CACHE_SERVICE,
          SERVICE_TYPES.LOCK_SERVICE
        ] 
      }
    );
  }

  /**
   * 获取记忆存储服务
   * @returns {MemoryStorageService}
   */
  getMemoryService() {
    this.ensureInitialized();
    return this.container.resolve(SERVICE_TYPES.MEMORY_STORAGE_SERVICE);
  }

  /**
   * 获取设置服务
   * @returns {SettingsService}
   */
  getSettingsService() {
    this.ensureInitialized();
    return this.container.resolve(SERVICE_TYPES.SETTINGS_SERVICE);
  }

  /**
   * 获取缓存服务
   * @returns {MemoryCacheService}
   */
  getCacheService() {
    this.ensureInitialized();
    return this.container.resolve(SERVICE_TYPES.CACHE_SERVICE);
  }

  /**
   * 获取锁服务
   * @returns {DistributedLockService}
   */
  getLockService() {
    this.ensureInitialized();
    return this.container.resolve(SERVICE_TYPES.LOCK_SERVICE);
  }

  /**
   * 确保容器已初始化
   */
  ensureInitialized() {
    if (!this.initialized) {
      throw new Error('服务容器未初始化，请先调用 initialize() 方法');
    }
  }

  /**
   * 销毁服务容器
   */
  dispose() {
    if (this.container) {
      this.container.dispose();
      this.container = null;
    }
    this.initialized = false;
  }

  /**
   * 获取设备ID
   * @returns {string}
   */
  getDeviceId() {
    return this.deviceId;
  }

  /**
   * 检查服务可用性
   * @returns {Promise<boolean>}
   */
  async isAvailable() {
    try {
      if (!this.initialized) {
        return false;
      }

      const memoryService = this.getMemoryService();
      const settingsService = this.getSettingsService();

      return await memoryService.isAvailable() && await settingsService.isAvailable();
    } catch (error) {
      console.error('检查服务可用性失败:', error);
      return false;
    }
  }
}

// 创建全局服务容器实例
export const frontendServiceContainer = new FrontendServiceContainer();

// 导出便捷访问函数
export const getMemoryService = () => frontendServiceContainer.getMemoryService();
export const getSettingsService = () => frontendServiceContainer.getSettingsService();
export const getCacheService = () => frontendServiceContainer.getCacheService();
export const getLockService = () => frontendServiceContainer.getLockService();

export default frontendServiceContainer;
