import React, { useState } from 'react';
import { Card, Typography, Button, Upload, message, Space, Divider, Alert, Checkbox, Spin, Modal, Input, Form } from 'antd';
import { UploadOutlined, DownloadOutlined, ExclamationCircleOutlined, SettingOutlined, LockOutlined } from '@ant-design/icons';
import { storageService } from '../services';
import { processSensitiveInfo, validateConfigFile, mergeConfigurations, getSensitiveInfoList } from '../utils/ConfigurationExportImport';
import { encryptSensitiveInfo, decryptSensitiveInfo, hasEncryptedSensitiveInfo } from '../utils/ConfigEncryption';

const { Title, Paragraph, Text } = Typography;

/**
 * 配置导入导出组件
 * 用于导出和导入用户配置（用户信息、OSS配置等）
 */
const ConfigurationManager = () => {
  const [exportLoading, setExportLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [exportOptions, setExportOptions] = useState({
    userInfo: true,
    cloudStorage: true,
    syncSettings: true,
    appearance: true,
    categories: true,
    cacheSettings: true,
    security: true,
    keepSensitiveInfo: false,
    encryptSensitiveInfo: false
  });

  const [exportPassword, setExportPassword] = useState('');
  const [importPassword, setImportPassword] = useState('');
  const [showPasswordInput, setShowPasswordInput] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [currentImportData, setCurrentImportData] = useState(null);

  // 导出配置
  const exportConfiguration = async () => {
    try {
      setExportLoading(true);

      // 获取当前设置
      const settings = await storageService.getSettings();

      // 创建导出配置对象
      const configToExport = {
        exportDate: new Date().toISOString(),
        version: '1.0.0',
        settings: {}
      };

      // 根据选项过滤要导出的设置
      const filteredSettings = { ...settings };

      // 用户信息
      if (!exportOptions.userInfo) {
        delete filteredSettings.userId;
        delete filteredSettings.userInfo;
      }

      // 云存储设置
      if (!exportOptions.cloudStorage) {
        delete filteredSettings.storageProvider;
        delete filteredSettings.huaweiObs;
        delete filteredSettings.minio;
        delete filteredSettings.tencentCos;
        delete filteredSettings.amazonS3;
      }

      // 同步设置
      if (!exportOptions.syncSettings) {
        delete filteredSettings.syncSettings;
      }

      // 外观设置
      if (!exportOptions.appearance) {
        delete filteredSettings.darkMode;
        delete filteredSettings.theme;
        delete filteredSettings.fontSize;
        delete filteredSettings.primaryColor;
        delete filteredSettings.compactMode;
      }

      // 分类设置
      if (!exportOptions.categories) {
        delete filteredSettings.categories;
        delete filteredSettings.defaultCategory;
      }

      // 缓存设置
      if (!exportOptions.cacheSettings) {
        delete filteredSettings.cacheSettings;
      }

      // 安全设置
      if (!exportOptions.security) {
        delete filteredSettings.encryption;
        delete filteredSettings.encryptionAlgorithm;
        delete filteredSettings.autoLock;
        delete filteredSettings.lockTimeout;
      }

      // 处理敏感信息，根据用户选择决定是否保留
      let processedSettings = processSensitiveInfo(filteredSettings, exportOptions.keepSensitiveInfo);

      // 如果选择了保留敏感信息并加密，则进行加密
      if (exportOptions.keepSensitiveInfo && exportOptions.encryptSensitiveInfo && exportPassword) {
        try {
          processedSettings = await encryptSensitiveInfo(processedSettings, exportPassword);
          // 添加加密标记
          processedSettings._hasEncryptedData = true;
        } catch (error) {
          console.error('加密敏感信息失败:', error);
          message.error(`加密敏感信息失败: ${error.message}`);
          setExportLoading(false);
          return;
        }
      }

      configToExport.settings = processedSettings;

      // 转换为 JSON 字符串
      const jsonString = JSON.stringify(configToExport, null, 2);

      // 创建 Blob 对象
      const blob = new Blob([jsonString], { type: 'application/json' });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `memory-keeper-config-${new Date().toISOString().slice(0, 10)}.json`;

      // 触发下载
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      // 释放 URL 对象
      URL.revokeObjectURL(url);

      message.success('配置导出成功');
    } catch (error) {
      console.error('导出配置失败:', error);
      message.error(`导出配置失败: ${error.message}`);
    } finally {
      setExportLoading(false);
    }
  };

  // 预览导入文件
  const handlePreview = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target.result;
        const data = JSON.parse(content);

        // 验证文件格式
        const validation = validateConfigFile(data);
        if (!validation.valid) {
          message.error(validation.message);
          return false;
        }

        // 检查是否包含加密的敏感信息
        if (data.settings._hasEncryptedData) {
          // 如果包含加密数据，先请求输入密码
          setCurrentImportData(data);
          setPasswordModalVisible(true);
        } else {
          // 如果不包含加密数据，直接显示导入确认对话框
          showImportConfirm(data);
        }
      } catch (error) {
        console.error('解析配置文件失败:', error);
        message.error(`解析配置文件失败: ${error.message}`);
      }
    };
    reader.readAsText(file);

    // 阻止自动上传
    return false;
  };

  // 处理密码输入对话框确认
  const handlePasswordConfirm = async () => {
    if (!importPassword) {
      message.error('请输入密码');
      return;
    }

    try {
      // 解密敏感信息
      const decryptedData = { ...currentImportData };
      decryptedData.settings = await decryptSensitiveInfo(currentImportData.settings, importPassword);

      // 关闭密码对话框
      setPasswordModalVisible(false);
      setImportPassword('');

      // 显示导入确认对话框
      showImportConfirm(decryptedData);
    } catch (error) {
      console.error('解密失败:', error);
      message.error(`解密失败: ${error.message}`);
    }
  };

  // 显示导入确认对话框
  const showImportConfirm = (data) => {
    // 检查配置文件是否包含敏感信息
    const hasSensitiveInfo = (
      (data.settings.huaweiObs && data.settings.huaweiObs.secretAccessKey) ||
      (data.settings.minio && data.settings.minio.secretKey) ||
      (data.settings.amazonS3 && data.settings.amazonS3.secretAccessKey) ||
      (data.settings.tencentCos && data.settings.tencentCos.secretKey) ||
      data.settings.encryptionKey
    );

    Modal.confirm({
      title: '导入配置',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>确定要导入以下配置吗？</p>
          <ul>
            {data.settings.userInfo && <li>用户信息</li>}
            {data.settings.storageProvider && <li>云存储设置 ({data.settings.storageProvider})</li>}
            {data.settings.syncSettings && <li>同步设置</li>}
            {data.settings.darkMode !== undefined && <li>外观设置</li>}
            {data.settings.categories && <li>分类设置</li>}
            {data.settings.cacheSettings && <li>缓存设置</li>}
            {data.settings.encryption !== undefined && <li>安全设置</li>}
            {hasSensitiveInfo && <li><Text type="danger">敏感信息（密钥、密码等）</Text></li>}
          </ul>
          <p>导入日期: {new Date(data.exportDate).toLocaleString()}</p>
          <Alert
            message="注意：导入配置将覆盖当前的相应设置"
            type="warning"
            showIcon
            style={{ marginTop: 16 }}
          />
          {hasSensitiveInfo && (
            <Alert
              message="安全提示"
              description="此配置文件包含敏感信息（如密钥、密码等）。导入后请注意保护您的账户安全。"
              type="error"
              showIcon
              style={{ marginTop: 8 }}
            />
          )}
        </div>
      ),
      onOk: () => importConfiguration(data),
      okText: '导入',
      cancelText: '取消',
    });
  };

  // 导入配置
  const importConfiguration = async (data) => {
    try {
      setImportLoading(true);

      // 获取当前设置
      const currentSettings = await storageService.getSettings();

      // 合并设置
      const mergedSettings = mergeConfigurations(currentSettings, data.settings);

      // 保存合并后的设置
      await storageService.saveSettings(mergedSettings);

      message.success('配置导入成功，部分设置可能需要重新输入敏感信息');

      // 清空文件列表
      setFileList([]);

      // 检查配置文件是否包含敏感信息
      const hasSensitiveInfo = (
        (data.settings.huaweiObs && data.settings.huaweiObs.secretAccessKey) ||
        (data.settings.minio && data.settings.minio.secretKey) ||
        (data.settings.amazonS3 && data.settings.amazonS3.secretAccessKey) ||
        (data.settings.tencentCos && data.settings.tencentCos.secretKey) ||
        data.settings.encryptionKey
      );

      if (hasSensitiveInfo) {
        // 如果包含敏感信息，显示成功提示
        Modal.success({
          title: '导入完成',
          content: (
            <div>
              <p>配置已成功导入，包括敏感信息（如密钥、密码等）。</p>
              <p>请注意保护您的账户安全。</p>
            </div>
          ),
          okText: '我知道了'
        });
      } else {
        // 如果不包含敏感信息，提示用户需要重新输入
        const sensitiveInfoList = getSensitiveInfoList(data.settings);

        if (sensitiveInfoList.length > 0) {
          Modal.info({
            title: '导入完成',
            content: (
              <div>
                <p>配置已成功导入，但某些敏感信息（如密钥、密码等）未包含在导入文件中。</p>
                <p>请前往相应设置页面，输入以下信息：</p>
                <ul>
                  {sensitiveInfoList.map((info, index) => (
                    <li key={index}>{info}</li>
                  ))}
                </ul>
              </div>
            ),
            okText: '我知道了'
          });
        }
      }
    } catch (error) {
      console.error('导入配置失败:', error);
      message.error(`导入配置失败: ${error.message}`);
    } finally {
      setImportLoading(false);
    }
  };

  // 处理文件变更
  const handleFileChange = (info) => {
    setFileList(info.fileList.slice(-1)); // 只保留最后上传的文件
  };

  // 处理导出选项变更
  const handleExportOptionChange = (option, checked) => {
    setExportOptions({
      ...exportOptions,
      [option]: checked
    });
  };

  return (
    <Card title="配置导入导出" bordered={false}>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
        <SettingOutlined style={{ fontSize: 24, marginRight: 12, color: '#1890ff' }} />
        <Title level={4} style={{ margin: 0 }}>配置管理</Title>
      </div>

      <Paragraph>
        此功能允许您导出当前配置（用户信息、OSS配置等）并在其他浏览器或设备上导入，方便快速设置而无需重复输入。
      </Paragraph>

      <Alert
        message="隐私与安全提示"
        description="默认情况下，导出的配置文件不包含敏感信息（如密钥、密码等）。您可以选择包含敏感信息，但请注意保管导出文件的安全。"
        type="warning"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Divider orientation="left">导出配置</Divider>

      <Space direction="vertical" style={{ width: '100%', marginBottom: 24 }}>
        <div style={{ marginBottom: 16 }}>
          <Title level={5}>选择要导出的配置项：</Title>
          <Space direction="vertical">
            <Checkbox
              checked={exportOptions.userInfo}
              onChange={(e) => handleExportOptionChange('userInfo', e.target.checked)}
            >
              用户信息
            </Checkbox>
            <Checkbox
              checked={exportOptions.cloudStorage}
              onChange={(e) => handleExportOptionChange('cloudStorage', e.target.checked)}
            >
              云存储设置
            </Checkbox>
            <Checkbox
              checked={exportOptions.syncSettings}
              onChange={(e) => handleExportOptionChange('syncSettings', e.target.checked)}
            >
              同步设置
            </Checkbox>
            <Checkbox
              checked={exportOptions.appearance}
              onChange={(e) => handleExportOptionChange('appearance', e.target.checked)}
            >
              外观设置
            </Checkbox>
            <Checkbox
              checked={exportOptions.categories}
              onChange={(e) => handleExportOptionChange('categories', e.target.checked)}
            >
              分类设置
            </Checkbox>
            <Checkbox
              checked={exportOptions.cacheSettings}
              onChange={(e) => handleExportOptionChange('cacheSettings', e.target.checked)}
            >
              缓存设置
            </Checkbox>
            <Checkbox
              checked={exportOptions.security}
              onChange={(e) => handleExportOptionChange('security', e.target.checked)}
            >
              安全设置
            </Checkbox>
            <Divider dashed style={{ margin: '8px 0' }} />
            <Checkbox
              checked={exportOptions.keepSensitiveInfo}
              onChange={(e) => handleExportOptionChange('keepSensitiveInfo', e.target.checked)}
            >
              <Text type="danger">包含敏感信息（密钥、密码等）</Text>
            </Checkbox>
            {exportOptions.keepSensitiveInfo && (
              <>
                <Alert
                  message="安全警告"
                  description="您选择了导出敏感信息，这包括云存储密钥等。请妥善保管导出的文件，防止被未授权的人访问。"
                  type="error"
                  showIcon
                  style={{ marginTop: 8, marginBottom: 8 }}
                />
                <Checkbox
                  checked={exportOptions.encryptSensitiveInfo}
                  onChange={(e) => {
                    handleExportOptionChange('encryptSensitiveInfo', e.target.checked);
                    setShowPasswordInput(e.target.checked);
                  }}
                  style={{ marginTop: 8 }}
                >
                  <Text type="warning">使用密码加密敏感信息</Text>
                </Checkbox>
                {showPasswordInput && (
                  <div style={{ marginTop: 8 }}>
                    <Input.Password
                      placeholder="请输入加密密码"
                      value={exportPassword}
                      onChange={(e) => setExportPassword(e.target.value)}
                      prefix={<LockOutlined />}
                      style={{ width: '100%' }}
                    />
                    <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginTop: 4 }}>
                      请记住此密码，导入时需要使用相同的密码解密。
                    </Text>
                  </div>
                )}
              </>
            )}
          </Space>
        </div>

        <Button
          type="primary"
          icon={<DownloadOutlined />}
          onClick={exportConfiguration}
          loading={exportLoading}
          disabled={!Object.values(exportOptions).some(Boolean)}
        >
          导出配置
        </Button>

        <Paragraph type="secondary">
          导出的配置文件为JSON格式，包含您选择的设置项。
        </Paragraph>
      </Space>

      <Divider orientation="left">导入配置</Divider>

      <Space direction="vertical" style={{ width: '100%' }}>
        <Upload
          beforeUpload={handlePreview}
          onChange={handleFileChange}
          fileList={fileList}
          accept=".json"
          maxCount={1}
        >
          <Button icon={<UploadOutlined />} loading={importLoading}>
            选择配置文件
          </Button>
        </Upload>

        <Paragraph type="secondary">
          导入配置将覆盖当前的相应设置。如果配置文件包含加密的敏感信息，您需要输入正确的密码才能解密。
        </Paragraph>

        <Modal
          title="输入解密密码"
          open={passwordModalVisible}
          onOk={handlePasswordConfirm}
          onCancel={() => {
            setPasswordModalVisible(false);
            setImportPassword('');
            setCurrentImportData(null);
          }}
          okText="确认"
          cancelText="取消"
        >
          <p>此配置文件包含加密的敏感信息，请输入密码解密。</p>
          <Input.Password
            placeholder="请输入密码"
            value={importPassword}
            onChange={(e) => setImportPassword(e.target.value)}
            prefix={<LockOutlined />}
          />
        </Modal>
      </Space>
    </Card>
  );
};

export default ConfigurationManager;
