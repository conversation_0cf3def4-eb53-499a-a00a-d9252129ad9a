/**
 * AutoSyncManager - 自动同步管理组件
 * 
 * 用于管理自动同步功能，包括定时同步和监听同步请求
 */
import React, { useEffect, useState } from 'react';
import { message } from 'antd';
import { incrementalSyncService, storageService } from '../services';

const AutoSyncManager = () => {
  const [syncSettings, setSyncSettings] = useState({
    autoSync: false,
    syncInterval: 30, // 分钟
    syncOnStartup: true,
    syncOnChange: false
  });
  
  const [syncTimer, setSyncTimer] = useState(null);
  const [lastSyncTime, setLastSyncTime] = useState(null);
  
  // 加载同步设置
  useEffect(() => {
    const loadSyncSettings = async () => {
      try {
        const settings = await storageService.getSettings();
        if (settings.syncSettings) {
          setSyncSettings(settings.syncSettings);
        }
      } catch (error) {
        console.error('加载同步设置失败:', error);
      }
    };
    
    loadSyncSettings();
    
    // 监听同步请求
    const handleSyncRequest = (message) => {
      if (message.type === 'SYNC_REQUESTED') {
        performSync();
      }
    };
    
    chrome.runtime.onMessage.addListener(handleSyncRequest);
    
    return () => {
      // 清理定时器和监听器
      if (syncTimer) {
        clearInterval(syncTimer);
      }
      chrome.runtime.onMessage.removeListener(handleSyncRequest);
    };
  }, []);
  
  // 当同步设置变化时，更新定时器
  useEffect(() => {
    // 清理现有定时器
    if (syncTimer) {
      clearInterval(syncTimer);
      setSyncTimer(null);
    }
    
    // 如果启用了自动同步，设置定时器
    if (syncSettings.autoSync && syncSettings.syncInterval > 0) {
      const interval = syncSettings.syncInterval * 60 * 1000; // 转换为毫秒
      const timer = setInterval(performSync, interval);
      setSyncTimer(timer);
      
      console.log(`自动同步已启用，间隔: ${syncSettings.syncInterval} 分钟`);
    }
    
    return () => {
      if (syncTimer) {
        clearInterval(syncTimer);
      }
    };
  }, [syncSettings]);
  
  // 执行同步
  const performSync = async () => {
    try {
      // 检查距离上次同步的时间
      if (lastSyncTime) {
        const now = new Date();
        const timeSinceLastSync = now - lastSyncTime;
        const minInterval = 60 * 1000; // 最小同步间隔（1分钟）
        
        if (timeSinceLastSync < minInterval) {
          console.log(`距离上次同步时间太短 (${Math.floor(timeSinceLastSync / 1000)}秒)，跳过本次同步`);
          return;
        }
      }
      
      console.log('开始执行自动同步...');
      setLastSyncTime(new Date());
      
      // 初始化增量同步服务
      if (!incrementalSyncService.isInitialized) {
        await incrementalSyncService.initialize();
      }
      
      // 执行同步
      const result = await incrementalSyncService.sync({
        conflictStrategy: syncSettings.conflictStrategy
      });
      
      if (result.success) {
        console.log('自动同步成功');
      } else {
        console.error('自动同步失败:', result.message);
      }
    } catch (error) {
      console.error('执行自动同步时出错:', error);
    }
  };
  
  // 这是一个无UI组件，只返回null
  return null;
};

export default AutoSyncManager;
