import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Switch, 
  Select, 
  Divider, 
  Typography, 
  Space, 
  Progress, 
  Alert, 
  Collapse, 
  Radio, 
  Tooltip,
  Modal,
  Spin,
  message
} from 'antd';
import { 
  SyncOutlined, 
  SettingOutlined, 
  CloudUploadOutlined, 
  CloudDownloadOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import incrementalSyncService from '../services/IncrementalSyncService';
import conflictResolutionService from '../services/ConflictResolutionService';
import syncOptimizationService from '../services/SyncOptimizationService';
import storageService from '../services/StorageService';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Panel } = Collapse;

const SyncSettingsPanel = () => {
  // 同步状态
  const [syncStatus, setSyncStatus] = useState({
    inProgress: false,
    phase: '',
    progress: 0,
    message: '',
    lastSync: null,
    error: null
  });
  
  // 同步设置
  const [syncSettings, setSyncSettings] = useState({
    autoSync: false,
    syncInterval: 30, // 分钟
    syncOnStartup: true,
    syncOnChange: false,
    conflictStrategy: conflictResolutionService.strategies.AUTO_NEWEST,
    compressionEnabled: true
  });
  
  // 冲突列表
  const [conflicts, setConflicts] = useState([]);
  
  // 显示冲突解决对话框
  const [showConflictModal, setShowConflictModal] = useState(false);
  const [currentConflict, setCurrentConflict] = useState(null);
  const [conflictResolution, setConflictResolution] = useState('auto_newest');
  
  // 加载同步设置
  useEffect(() => {
    const loadSyncSettings = async () => {
      try {
        const settings = await storageService.getSettings();
        if (settings.syncSettings) {
          setSyncSettings(settings.syncSettings);
        }
        
        // 获取上次同步时间
        const syncInfo = await storageService.localCache.getItem('last_sync_info');
        if (syncInfo && syncInfo.timestamp) {
          setSyncStatus(prev => ({
            ...prev,
            lastSync: new Date(syncInfo.timestamp)
          }));
        }
        
        // 获取待解决的冲突
        const pendingConflicts = conflictResolutionService.getPendingConflicts();
        setConflicts(pendingConflicts);
      } catch (error) {
        console.error('加载同步设置失败:', error);
      }
    };
    
    loadSyncSettings();
  }, []);
  
  // 保存同步设置
  const saveSyncSettings = async (newSettings) => {
    try {
      const settings = await storageService.getSettings();
      settings.syncSettings = newSettings;
      await storageService.saveSettings(settings);
      setSyncSettings(newSettings);
      message.success('同步设置已保存');
    } catch (error) {
      console.error('保存同步设置失败:', error);
      message.error('保存同步设置失败: ' + error.message);
    }
  };
  
  // 处理设置变更
  const handleSettingChange = (key, value) => {
    const newSettings = { ...syncSettings, [key]: value };
    saveSyncSettings(newSettings);
  };
  
  // 执行同步
  const handleSync = async () => {
    if (syncStatus.inProgress) {
      message.warning('同步已在进行中，请等待完成');
      return;
    }
    
    try {
      setSyncStatus({
        inProgress: true,
        phase: 'preparing',
        progress: 0,
        message: '准备同步...',
        error: null
      });
      
      // 初始化增量同步服务
      if (!incrementalSyncService.isInitialized) {
        await incrementalSyncService.initialize();
      }
      
      // 执行同步
      const result = await incrementalSyncService.sync({
        conflictStrategy: syncSettings.conflictStrategy,
        progressCallback: (progress) => {
          setSyncStatus(prev => ({
            ...prev,
            phase: progress.phase,
            progress: progress.percentage,
            message: progress.message
          }));
        },
        conflictCallback: async (conflict) => {
          // 添加到待解决的冲突列表
          conflictResolutionService.addPendingConflict(conflict);
          
          // 根据冲突策略决定如何处理
          if (syncSettings.conflictStrategy === conflictResolutionService.strategies.MANUAL) {
            // 如果是手动解决，返回一个默认决策，稍后用户可以手动解决
            return { action: 'use_newest' };
          } else {
            // 自动解决冲突
            return { action: syncSettings.conflictStrategy === conflictResolutionService.strategies.AUTO_LOCAL ? 'use_local' : 
                            syncSettings.conflictStrategy === conflictResolutionService.strategies.AUTO_REMOTE ? 'use_remote' : 'use_newest' };
          }
        }
      });
      
      if (result.success) {
        setSyncStatus(prev => ({
          ...prev,
          inProgress: false,
          phase: 'completed',
          progress: 100,
          message: '同步完成',
          lastSync: new Date(result.timestamp),
          error: null
        }));
        
        message.success('同步成功');
        
        // 更新冲突列表
        const pendingConflicts = conflictResolutionService.getPendingConflicts();
        setConflicts(pendingConflicts);
        
        // 如果有未解决的冲突，显示提示
        if (pendingConflicts.length > 0) {
          message.warning(`有 ${pendingConflicts.length} 个冲突需要解决`);
        }
      } else {
        setSyncStatus(prev => ({
          ...prev,
          inProgress: false,
          phase: 'error',
          message: '同步失败: ' + result.message,
          error: result.error
        }));
        
        message.error('同步失败: ' + result.message);
      }
    } catch (error) {
      console.error('同步失败:', error);
      
      setSyncStatus(prev => ({
        ...prev,
        inProgress: false,
        phase: 'error',
        message: '同步失败: ' + error.message,
        error
      }));
      
      message.error('同步失败: ' + error.message);
    }
  };
  
  // 解决冲突
  const handleResolveConflict = (conflict) => {
    setCurrentConflict(conflict);
    setConflictResolution('auto_newest');
    setShowConflictModal(true);
  };
  
  // 确认解决冲突
  const confirmResolveConflict = async () => {
    try {
      if (!currentConflict) return;
      
      let resolution;
      
      switch (conflictResolution) {
        case 'use_local':
          resolution = { action: 'use_local' };
          break;
          
        case 'use_remote':
          resolution = { action: 'use_remote' };
          break;
          
        case 'use_merged':
          // 创建合并的记忆
          const mergedMemory = conflictResolutionService._createMergedMemory(
            currentConflict.localMemory,
            currentConflict.remoteMemory
          );
          resolution = { action: 'use_merged', mergedMemory };
          break;
          
        case 'auto_newest':
        default:
          resolution = { action: 'use_newest' };
          break;
      }
      
      // 解决冲突
      conflictResolutionService.resolveConflict(currentConflict.id, resolution);
      
      // 更新冲突列表
      const pendingConflicts = conflictResolutionService.getPendingConflicts();
      setConflicts(pendingConflicts);
      
      setShowConflictModal(false);
      setCurrentConflict(null);
      
      message.success('冲突已解决');
    } catch (error) {
      console.error('解决冲突失败:', error);
      message.error('解决冲突失败: ' + error.message);
    }
  };
  
  // 取消解决冲突
  const cancelResolveConflict = () => {
    setShowConflictModal(false);
    setCurrentConflict(null);
  };
  
  // 渲染冲突项
  const renderConflictItem = (conflict) => {
    let title = '未知冲突';
    let description = '';
    
    if (conflict.type === 'memory') {
      title = `记忆冲突: "${conflict.localMemory?.title || conflict.remoteMemory?.title || '无标题'}"`;
      description = `本地版本 (${conflict.localMemory?.version || '无版本号'}) 和远程版本 (${conflict.remoteMemory?.version || '无版本号'}) 存在差异`;
    } else if (conflict.type === 'deletion') {
      title = `删除冲突: 记忆 ${conflict.memoryId}`;
      description = '该记忆已在远程删除，但本地仍存在';
    } else if (conflict.type === 'category') {
      title = `分类冲突: "${conflict.local?.name || conflict.remote?.name || '未知分类'}"`;
      description = '本地和远程的分类定义不一致';
    }
    
    return (
      <Alert
        key={conflict.id}
        message={title}
        description={description}
        type="warning"
        showIcon
        action={
          <Button size="small" onClick={() => handleResolveConflict(conflict)}>
            解决冲突
          </Button>
        }
        style={{ marginBottom: 10 }}
      />
    );
  };
  
  return (
    <div>
      <Card title="同步设置" extra={<SettingOutlined />}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text>自动同步</Text>
            <Switch 
              checked={syncSettings.autoSync} 
              onChange={(checked) => handleSettingChange('autoSync', checked)} 
            />
          </div>
          
          {syncSettings.autoSync && (
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text>同步间隔</Text>
              <Select 
                value={syncSettings.syncInterval} 
                style={{ width: 120 }}
                onChange={(value) => handleSettingChange('syncInterval', value)}
              >
                <Option value={5}>5 分钟</Option>
                <Option value={15}>15 分钟</Option>
                <Option value={30}>30 分钟</Option>
                <Option value={60}>1 小时</Option>
                <Option value={120}>2 小时</Option>
                <Option value={360}>6 小时</Option>
                <Option value={720}>12 小时</Option>
                <Option value={1440}>1 天</Option>
              </Select>
            </div>
          )}
          
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text>启动时同步</Text>
            <Switch 
              checked={syncSettings.syncOnStartup} 
              onChange={(checked) => handleSettingChange('syncOnStartup', checked)} 
            />
          </div>
          
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text>数据变更时同步</Text>
            <Switch 
              checked={syncSettings.syncOnChange} 
              onChange={(checked) => handleSettingChange('syncOnChange', checked)} 
            />
          </div>
          
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text>启用数据压缩</Text>
            <Switch 
              checked={syncSettings.compressionEnabled} 
              onChange={(checked) => handleSettingChange('compressionEnabled', checked)} 
            />
          </div>
          
          <Divider orientation="left">冲突解决策略</Divider>
          
          <div>
            <Select 
              value={syncSettings.conflictStrategy} 
              style={{ width: '100%' }}
              onChange={(value) => handleSettingChange('conflictStrategy', value)}
            >
              <Option value={conflictResolutionService.strategies.AUTO_NEWEST}>
                自动选择最新版本
              </Option>
              <Option value={conflictResolutionService.strategies.AUTO_LOCAL}>
                优先使用本地版本
              </Option>
              <Option value={conflictResolutionService.strategies.AUTO_REMOTE}>
                优先使用远程版本
              </Option>
              <Option value={conflictResolutionService.strategies.MANUAL}>
                手动解决冲突
              </Option>
            </Select>
            
            <Text type="secondary" style={{ display: 'block', marginTop: 8 }}>
              {syncSettings.conflictStrategy === conflictResolutionService.strategies.AUTO_NEWEST && 
                '当发生冲突时，自动选择最近修改的版本。'}
              {syncSettings.conflictStrategy === conflictResolutionService.strategies.AUTO_LOCAL && 
                '当发生冲突时，优先保留本地的修改。'}
              {syncSettings.conflictStrategy === conflictResolutionService.strategies.AUTO_REMOTE && 
                '当发生冲突时，优先使用远程的修改。'}
              {syncSettings.conflictStrategy === conflictResolutionService.strategies.MANUAL && 
                '当发生冲突时，保留冲突记录，等待手动解决。'}
            </Text>
          </div>
        </Space>
      </Card>
      
      <Card 
        title="同步状态" 
        extra={<SyncOutlined spin={syncStatus.inProgress} />}
        style={{ marginTop: 16 }}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          {syncStatus.lastSync && (
            <div>
              <Text>上次同步: </Text>
              <Text strong>{syncStatus.lastSync.toLocaleString()}</Text>
            </div>
          )}
          
          {syncStatus.inProgress && (
            <div>
              <Text>同步进度: </Text>
              <Progress 
                percent={syncStatus.progress} 
                status="active" 
                style={{ marginBottom: 8 }}
              />
              <Text>{syncStatus.message}</Text>
            </div>
          )}
          
          {syncStatus.error && (
            <Alert
              message="同步错误"
              description={syncStatus.error.message || syncStatus.message}
              type="error"
              showIcon
            />
          )}
          
          <div style={{ marginTop: 16 }}>
            <Button 
              type="primary" 
              icon={<SyncOutlined />} 
              loading={syncStatus.inProgress}
              onClick={handleSync}
              block
            >
              {syncStatus.inProgress ? '同步中...' : '立即同步'}
            </Button>
          </div>
        </Space>
      </Card>
      
      {conflicts.length > 0 && (
        <Card 
          title={`待解决的冲突 (${conflicts.length})`} 
          extra={<WarningOutlined style={{ color: '#faad14' }} />}
          style={{ marginTop: 16 }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            {conflicts.map(renderConflictItem)}
          </Space>
        </Card>
      )}
      
      {/* 冲突解决对话框 */}
      <Modal
        title="解决冲突"
        open={showConflictModal}
        onOk={confirmResolveConflict}
        onCancel={cancelResolveConflict}
        width={700}
      >
        {currentConflict && (
          <div>
            <Alert
              message={`冲突类型: ${
                currentConflict.type === 'memory' ? '记忆内容冲突' : 
                currentConflict.type === 'deletion' ? '记忆删除冲突' : 
                currentConflict.type === 'category' ? '分类冲突' : '未知冲突'
              }`}
              description={currentConflict.message}
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <Divider>选择解决方案</Divider>
            
            <Radio.Group 
              value={conflictResolution} 
              onChange={(e) => setConflictResolution(e.target.value)}
              style={{ width: '100%' }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <Radio value="auto_newest">
                  <Text strong>使用最新版本</Text>
                  <Text type="secondary" style={{ display: 'block' }}>
                    自动选择最近修改的版本
                  </Text>
                </Radio>
                
                <Radio value="use_local">
                  <Text strong>使用本地版本</Text>
                  <Text type="secondary" style={{ display: 'block' }}>
                    保留本地的修改，忽略远程的修改
                  </Text>
                </Radio>
                
                <Radio value="use_remote">
                  <Text strong>使用远程版本</Text>
                  <Text type="secondary" style={{ display: 'block' }}>
                    使用远程的修改，覆盖本地的修改
                  </Text>
                </Radio>
                
                <Radio value="use_merged">
                  <Text strong>合并两个版本</Text>
                  <Text type="secondary" style={{ display: 'block' }}>
                    尝试合并两个版本的内容（可能需要手动编辑）
                  </Text>
                </Radio>
              </Space>
            </Radio.Group>
            
            {currentConflict.type === 'memory' && (
              <div style={{ marginTop: 16 }}>
                <Collapse>
                  <Panel header="查看本地版本" key="local">
                    <div>
                      <Text strong>标题: </Text>
                      <Text>{currentConflict.localMemory?.title || '无标题'}</Text>
                    </div>
                    <div>
                      <Text strong>内容: </Text>
                      <div style={{ 
                        maxHeight: 200, 
                        overflow: 'auto', 
                        border: '1px solid #d9d9d9', 
                        borderRadius: 4,
                        padding: 8,
                        marginTop: 8
                      }}>
                        <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                          {typeof currentConflict.localMemory?.content === 'string' 
                            ? currentConflict.localMemory.content 
                            : JSON.stringify(currentConflict.localMemory?.content, null, 2)}
                        </pre>
                      </div>
                    </div>
                    <div style={{ marginTop: 8 }}>
                      <Text strong>最后修改: </Text>
                      <Text>{new Date(currentConflict.localMemory?.lastModified || currentConflict.localMemory?.createdAt).toLocaleString()}</Text>
                    </div>
                  </Panel>
                  <Panel header="查看远程版本" key="remote">
                    <div>
                      <Text strong>标题: </Text>
                      <Text>{currentConflict.remoteMemory?.title || '无标题'}</Text>
                    </div>
                    <div>
                      <Text strong>内容: </Text>
                      <div style={{ 
                        maxHeight: 200, 
                        overflow: 'auto', 
                        border: '1px solid #d9d9d9', 
                        borderRadius: 4,
                        padding: 8,
                        marginTop: 8
                      }}>
                        <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                          {typeof currentConflict.remoteMemory?.content === 'string' 
                            ? currentConflict.remoteMemory.content 
                            : JSON.stringify(currentConflict.remoteMemory?.content, null, 2)}
                        </pre>
                      </div>
                    </div>
                    <div style={{ marginTop: 8 }}>
                      <Text strong>最后修改: </Text>
                      <Text>{new Date(currentConflict.remoteMemory?.lastModified || currentConflict.remoteMemory?.createdAt).toLocaleString()}</Text>
                    </div>
                  </Panel>
                </Collapse>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default SyncSettingsPanel;
