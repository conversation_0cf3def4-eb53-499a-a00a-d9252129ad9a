import React, { useState, useEffect } from 'react';
import {
  Badge,
  Button,
  Tooltip,
  Popover,
  Space,
  Typography,
  Progress,
  Divider
} from 'antd';
import {
  SyncOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  ClockCircleOutlined,
  CloudOutlined,
  SettingOutlined
} from '@ant-design/icons';
import incrementalSyncService from '../services/IncrementalSyncService';
import conflictResolutionService from '../services/ConflictResolutionService';
import storageService from '../services/StorageService';

const { Text } = Typography;

const SyncStatusBar = ({ onOpenSettings }) => {
  // 同步状态
  const [syncStatus, setSyncStatus] = useState({
    inProgress: false,
    phase: '',
    progress: 0,
    message: '',
    lastSync: null,
    error: null
  });

  // 冲突数量
  const [conflictCount, setConflictCount] = useState(0);

  // 同步设置
  const [syncSettings, setSyncSettings] = useState({
    autoSync: false,
    syncInterval: 30,
    syncOnStartup: true,
    syncOnChange: false
  });

  // 加载同步状态和设置
  useEffect(() => {
    const loadSyncStatus = async () => {
      try {
        // 获取上次同步时间
        const syncInfo = await storageService.localCache.getItem('last_sync_info');
        if (syncInfo && syncInfo.timestamp) {
          setSyncStatus(prev => ({
            ...prev,
            lastSync: new Date(syncInfo.timestamp)
          }));
        }

        // 获取待解决的冲突
        const pendingConflicts = conflictResolutionService.getPendingConflicts();
        setConflictCount(pendingConflicts.length);

        // 获取同步设置
        const settings = await storageService.getSettings();
        if (settings.syncSettings) {
          setSyncSettings(settings.syncSettings);
        }
      } catch (error) {
        console.error('加载同步状态失败:', error);
      }
    };

    loadSyncStatus();

    // 定期检查冲突
    const checkConflictsInterval = setInterval(() => {
      const pendingConflicts = conflictResolutionService.getPendingConflicts();
      setConflictCount(pendingConflicts.length);
    }, 30000); // 每30秒检查一次

    return () => {
      clearInterval(checkConflictsInterval);
    };
  }, []);

  // 执行同步
  const handleSync = async () => {
    if (syncStatus.inProgress) {
      return;
    }

    try {
      setSyncStatus({
        inProgress: true,
        phase: 'preparing',
        progress: 0,
        message: '准备同步...',
        error: null
      });

      // 初始化增量同步服务
      if (!incrementalSyncService.isInitialized) {
        await incrementalSyncService.initialize();
      }

      // 执行同步
      const result = await incrementalSyncService.sync({
        conflictStrategy: syncSettings.conflictStrategy,
        progressCallback: (progress) => {
          setSyncStatus(prev => ({
            ...prev,
            phase: progress.phase,
            progress: progress.percentage,
            message: progress.message
          }));
        }
      });

      if (result.success) {
        setSyncStatus(prev => ({
          ...prev,
          inProgress: false,
          phase: 'completed',
          progress: 100,
          message: '同步完成',
          lastSync: new Date(result.timestamp),
          error: null
        }));

        // 更新冲突数量
        const pendingConflicts = conflictResolutionService.getPendingConflicts();
        setConflictCount(pendingConflicts.length);
      } else {
        setSyncStatus(prev => ({
          ...prev,
          inProgress: false,
          phase: 'error',
          message: '同步失败: ' + result.message,
          error: result.error
        }));
      }
    } catch (error) {
      console.error('同步失败:', error);

      setSyncStatus(prev => ({
        ...prev,
        inProgress: false,
        phase: 'error',
        message: '同步失败: ' + error.message,
        error
      }));
    }
  };

  // 获取状态图标
  const getStatusIcon = () => {
    if (syncStatus.inProgress) {
      return <SyncOutlined spin />;
    }

    if (syncStatus.error) {
      return <WarningOutlined style={{ color: '#ff4d4f' }} />;
    }

    if (conflictCount > 0) {
      return <WarningOutlined style={{ color: '#faad14' }} />;
    }

    if (syncStatus.lastSync) {
      const now = new Date();
      const lastSync = new Date(syncStatus.lastSync);
      const hoursSinceLastSync = (now - lastSync) / (1000 * 60 * 60);

      if (hoursSinceLastSync < 1) {
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      }

      if (hoursSinceLastSync < 24) {
        return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
      }

      return <ClockCircleOutlined style={{ color: '#faad14' }} />;
    }

    return <CloudOutlined />;
  };

  // 获取状态文本
  const getStatusText = () => {
    if (syncStatus.inProgress) {
      return syncStatus.message || '同步中...';
    }

    if (syncStatus.error) {
      return '同步失败';
    }

    if (conflictCount > 0) {
      return `${conflictCount} 个冲突待解决`;
    }

    if (syncStatus.lastSync) {
      const now = new Date();
      const lastSync = new Date(syncStatus.lastSync);
      const minutesSinceLastSync = Math.floor((now - lastSync) / (1000 * 60));

      if (minutesSinceLastSync < 1) {
        return '刚刚同步';
      }

      if (minutesSinceLastSync < 60) {
        return `${minutesSinceLastSync} 分钟前同步`;
      }

      const hoursSinceLastSync = Math.floor(minutesSinceLastSync / 60);

      if (hoursSinceLastSync < 24) {
        return `${hoursSinceLastSync} 小时前同步`;
      }

      const daysSinceLastSync = Math.floor(hoursSinceLastSync / 24);
      return `${daysSinceLastSync} 天前同步`;
    }

    return '未同步';
  };

  // 状态弹出内容
  const popoverContent = (
    <div style={{ maxWidth: 300 }}>
      <div>
        <Text strong>同步状态: </Text>
        <Text>{getStatusText()}</Text>
      </div>

      {syncStatus.lastSync && (
        <div>
          <Text strong>上次同步: </Text>
          <Text>{syncStatus.lastSync.toLocaleString()}</Text>
        </div>
      )}

      {syncStatus.inProgress && (
        <div style={{ marginTop: 8 }}>
          <Text>{syncStatus.message}</Text>
          <Progress
            percent={syncStatus.progress}
            status="active"
            size="small"
            style={{ marginTop: 4 }}
          />
        </div>
      )}

      {conflictCount > 0 && (
        <div style={{ marginTop: 8 }}>
          <Badge count={conflictCount} style={{ backgroundColor: '#faad14' }} />
          <Text style={{ marginLeft: 8 }}>待解决的冲突</Text>
        </div>
      )}

      <Divider style={{ margin: '8px 0' }} />

      <Space>
        <Button
          size="small"
          type="primary"
          icon={<SyncOutlined />}
          loading={syncStatus.inProgress}
          onClick={handleSync}
          disabled={syncStatus.inProgress}
        >
          同步
        </Button>

        <Button
          size="small"
          icon={<SettingOutlined />}
          onClick={onOpenSettings}
        >
          同步设置
        </Button>
      </Space>
    </div>
  );

  return (
    <Popover
      content={popoverContent}
      title="同步状态"
      trigger="click"
      placement="bottomRight"
    >
      <Badge count={conflictCount > 0 ? conflictCount : 0} size="small">
        <Button
          type="text"
          icon={getStatusIcon()}
          style={{ display: 'flex', alignItems: 'center' }}
        >
          <span style={{ marginLeft: 4 }}>{getStatusText()}</span>
        </Button>
      </Badge>
    </Popover>
  );
};

export default SyncStatusBar;
