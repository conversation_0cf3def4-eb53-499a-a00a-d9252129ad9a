import React, { useState, useEffect } from 'react';
import { Card, Typography, Select, Button, Progress, Alert, Space, Divider, Spin, Modal, List, Result } from 'antd';
import { SwapOutlined, CloudSyncOutlined, ExclamationCircleOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import storageMigrationService from '../services/StorageMigrationService';
import { StorageTypes } from '../services/storage';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;
const { confirm } = Modal;

/**
 * 存储迁移面板组件
 * 用于在不同的存储提供者之间迁移数据
 */
const StorageMigrationPanel = ({ currentProvider, settings, onMigrationComplete }) => {
  const [sourceProvider, setSourceProvider] = useState(currentProvider);
  const [destinationProvider, setDestinationProvider] = useState('');
  const [migrationPossible, setMigrationPossible] = useState(false);
  const [checkingMigration, setCheckingMigration] = useState(false);
  const [checkMessage, setCheckMessage] = useState('');
  const [migrationInProgress, setMigrationInProgress] = useState(false);
  const [migrationProgress, setMigrationProgress] = useState(0);
  const [migrationMessage, setMigrationMessage] = useState('');
  const [migrationResult, setMigrationResult] = useState(null);
  const [availableProviders, setAvailableProviders] = useState([]);

  // 初始化可用的存储提供者
  useEffect(() => {
    const providers = StorageTypes.getAllTypes().filter(type =>
      type !== 'getAllTypes' && type !== 'isValidType' && settings[type]
    );
    setAvailableProviders(providers);

    // 默认设置源提供者为当前提供者
    setSourceProvider(currentProvider);

    // 默认设置目标提供者为第一个不是当前提供者的提供者
    const otherProviders = providers.filter(p => p !== currentProvider);
    if (otherProviders.length > 0) {
      setDestinationProvider(otherProviders[0]);
    }
  }, [currentProvider, settings]);

  // 当源提供者或目标提供者变化时，检查迁移可能性
  useEffect(() => {
    const checkMigration = async () => {
      if (sourceProvider && destinationProvider && sourceProvider !== destinationProvider) {
        setCheckingMigration(true);
        setMigrationPossible(false);
        setCheckMessage('');

        try {
          const result = await storageMigrationService.checkMigrationPossibility(
            sourceProvider,
            destinationProvider
          );

          setMigrationPossible(result.possible);
          setCheckMessage(result.message);
        } catch (error) {
          console.error('检查迁移可能性失败:', error);
          setMigrationPossible(false);
          setCheckMessage(`检查失败: ${error.message}`);
        } finally {
          setCheckingMigration(false);
        }
      } else {
        setMigrationPossible(false);
        setCheckMessage('请选择不同的源和目标存储提供者');
      }
    };

    checkMigration();
  }, [sourceProvider, destinationProvider]);

  // 处理源提供者变更
  const handleSourceChange = (value) => {
    setSourceProvider(value);

    // 如果目标提供者与新的源提供者相同，则重置目标提供者
    if (value === destinationProvider) {
      const otherProviders = availableProviders.filter(p => p !== value);
      if (otherProviders.length > 0) {
        setDestinationProvider(otherProviders[0]);
      } else {
        setDestinationProvider('');
      }
    }
  };

  // 处理目标提供者变更
  const handleDestinationChange = (value) => {
    setDestinationProvider(value);
  };

  // 开始迁移
  const startMigration = () => {
    confirm({
      title: '确认迁移数据',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>您确定要将数据从 <Text strong>{getProviderDisplayName(sourceProvider)}</Text> 迁移到 <Text strong>{getProviderDisplayName(destinationProvider)}</Text> 吗？</p>
          <p>迁移过程中请不要关闭此页面或进行其他操作。</p>
          <p>迁移完成后，系统将自动切换到新的存储提供者。</p>
          <Alert
            message="智能重复文件检测"
            description="系统将自动检测并跳过相同的文件（基于MD5哈希值比较），以节省时间和成本。"
            type="info"
            showIcon
            style={{ marginTop: 12 }}
          />
        </div>
      ),
      onOk: async () => {
        setMigrationInProgress(true);
        setMigrationProgress(0);
        setMigrationMessage('准备迁移...');
        setMigrationResult(null);

        try {
          const result = await storageMigrationService.startMigration(
            sourceProvider,
            destinationProvider,
            (progress) => {
              setMigrationProgress(progress.progress);
              setMigrationMessage(progress.message);
            }
          );

          setMigrationResult(result);

          if (result.success && typeof onMigrationComplete === 'function') {
            onMigrationComplete(destinationProvider);
          }
        } catch (error) {
          console.error('迁移失败:', error);
          setMigrationResult({
            success: false,
            message: `迁移失败: ${error.message}`,
            error
          });
        } finally {
          setMigrationInProgress(false);
        }
      },
      onCancel() {
        console.log('取消迁移');
      },
    });
  };

  // 取消迁移
  const cancelMigration = async () => {
    try {
      await storageMigrationService.cancelMigration();
      setMigrationInProgress(false);
      setMigrationResult({
        success: false,
        message: '迁移已取消'
      });
    } catch (error) {
      console.error('取消迁移失败:', error);
    }
  };

  // 重置迁移状态
  const resetMigration = () => {
    setMigrationResult(null);
  };

  // 获取提供者显示名称
  const getProviderDisplayName = (provider) => {
    switch (provider) {
      case StorageTypes.HUAWEI_OBS:
        return '华为云OBS';
      case StorageTypes.MINIO:
        return 'MinIO';
      case StorageTypes.ALIYUN_OSS:
        return '阿里云OSS';
      case StorageTypes.AMAZON_S3:
        return 'Amazon S3';
      case StorageTypes.TENCENT_COS:
        return '腾讯云COS';
      default:
        return provider;
    }
  };

  // 渲染迁移结果
  const renderMigrationResult = () => {
    if (!migrationResult) return null;

    return (
      <div style={{ marginTop: 16 }}>
        {migrationResult.success ? (
          <Result
            status="success"
            title="迁移成功"
            subTitle={migrationResult.message}
            extra={[
              <Button
                key="done"
                type="primary"
                onClick={resetMigration}
              >
                完成
              </Button>
            ]}
          />
        ) : (
          <Result
            status="error"
            title="迁移失败"
            subTitle={migrationResult.message}
            extra={[
              <Button
                key="retry"
                type="primary"
                onClick={startMigration}
              >
                重试
              </Button>,
              <Button
                key="reset"
                onClick={resetMigration}
              >
                重置
              </Button>
            ]}
          >
            {migrationResult.details && migrationResult.details.errors && migrationResult.details.errors.length > 0 && (
              <div style={{ textAlign: 'left' }}>
                <Divider />
                <Title level={5}>错误详情</Title>
                <List
                  size="small"
                  bordered
                  dataSource={migrationResult.details.errors.slice(0, 5)} // 只显示前5个错误
                  renderItem={item => (
                    <List.Item>
                      <Text type="danger">{item.key}: {item.error}</Text>
                    </List.Item>
                  )}
                  footer={migrationResult.details.errors.length > 5 ? `还有 ${migrationResult.details.errors.length - 5} 个错误未显示` : null}
                />
              </div>
            )}
          </Result>
        )}

        {/* 显示重复文件统计信息 */}
        {migrationResult.success && migrationResult.details && migrationResult.details.duplicateStats && migrationResult.details.duplicateStats.totalDuplicates > 0 && (
          <div style={{ marginTop: 16, textAlign: 'left' }}>
            <Divider />
            <Alert
              message={`跳过了 ${migrationResult.details.duplicateStats.totalDuplicates} 个重复文件`}
              description="这些文件在目标存储中已经存在且MD5哈希值相同，因此被跳过以节省时间和成本。"
              type="info"
              showIcon
            />

            {/* 显示重复文件列表 */}
            {migrationResult.details.duplicateStats.duplicateFiles.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>重复文件列表</Title>
                <List
                  size="small"
                  bordered
                  dataSource={migrationResult.details.duplicateStats.duplicateFiles.slice(0, 10)} // 只显示前10个
                  renderItem={item => (
                    <List.Item>
                      <div style={{ width: '100%' }}>
                        <Text strong>{item.key}</Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          MD5: {item.md5.substring(0, 16)}...
                          {item.size > 0 && ` | 大小: ${Math.round(item.size / 1024)}KB`}
                          {item.isLargeFile && ' | 大文件'}
                        </Text>
                      </div>
                    </List.Item>
                  )}
                  footer={migrationResult.details.duplicateStats.duplicateFiles.length > 10 ? `还有 ${migrationResult.details.duplicateStats.duplicateFiles.length - 10} 个重复文件未显示` : null}
                />
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <Card title="存储迁移" style={{ marginTop: 16 }}>
      <Paragraph>
        此功能允许您将数据从一个存储提供者迁移到另一个存储提供者。迁移过程中，所有数据将被复制到新的存储位置，并且系统将自动切换到新的存储提供者。
      </Paragraph>

      {migrationResult ? (
        renderMigrationResult()
      ) : (
        <>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
              <div style={{ flex: 1 }}>
                <Text strong>源存储提供者</Text>
                <Select
                  style={{ width: '100%', marginTop: 8 }}
                  value={sourceProvider}
                  onChange={handleSourceChange}
                  disabled={migrationInProgress || checkingMigration}
                >
                  {availableProviders.map(provider => (
                    <Option key={provider} value={provider}>
                      {getProviderDisplayName(provider)}
                    </Option>
                  ))}
                </Select>
              </div>

              <SwapOutlined style={{ fontSize: 24 }} />

              <div style={{ flex: 1 }}>
                <Text strong>目标存储提供者</Text>
                <Select
                  style={{ width: '100%', marginTop: 8 }}
                  value={destinationProvider}
                  onChange={handleDestinationChange}
                  disabled={migrationInProgress || checkingMigration}
                >
                  {availableProviders
                    .filter(provider => provider !== sourceProvider)
                    .map(provider => (
                      <Option key={provider} value={provider}>
                        {getProviderDisplayName(provider)}
                      </Option>
                    ))}
                </Select>
              </div>
            </div>

            {checkingMigration ? (
              <div style={{ textAlign: 'center', padding: '16px 0' }}>
                <Spin tip="正在检查迁移可能性..." />
              </div>
            ) : checkMessage ? (
              <Alert
                message={migrationPossible ? "可以迁移" : "无法迁移"}
                description={checkMessage}
                type={migrationPossible ? "success" : "error"}
                showIcon
              />
            ) : null}

            {migrationInProgress ? (
              <div>
                <Progress
                  percent={migrationProgress}
                  status="active"
                  style={{ marginTop: 16 }}
                />
                <div style={{ textAlign: 'center', margin: '16px 0' }}>
                  <Text>{migrationMessage}</Text>
                </div>
                <Button
                  type="danger"
                  icon={<CloseCircleOutlined />}
                  onClick={cancelMigration}
                  style={{ marginTop: 8 }}
                >
                  取消迁移
                </Button>
              </div>
            ) : (
              <Button
                type="primary"
                icon={<CloudSyncOutlined />}
                onClick={startMigration}
                disabled={!migrationPossible || checkingMigration}
                style={{ marginTop: 16 }}
              >
                开始迁移
              </Button>
            )}
          </Space>

          <Divider />

          <Alert
            message="迁移注意事项"
            description={
              <ul style={{ paddingLeft: 16, margin: 0 }}>
                <li>迁移过程可能需要一些时间，取决于数据量和网络速度。</li>
                <li>迁移过程中请不要关闭此页面或进行其他操作。</li>
                <li>迁移完成后，系统将自动切换到新的存储提供者。</li>
                <li>建议在迁移前备份您的数据。</li>
              </ul>
            }
            type="info"
            showIcon
          />
        </>
      )}
    </Card>
  );
};

export default StorageMigrationPanel;
