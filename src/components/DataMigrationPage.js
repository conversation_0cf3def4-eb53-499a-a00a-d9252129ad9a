import React, { useState, useEffect } from 'react';
import { Card, Typography, Alert, Divider } from 'antd';
import { CloudSyncOutlined } from '@ant-design/icons';
import StorageMigrationPanel from './StorageMigrationPanel';
import { storageService } from '../services';

const { Title, Paragraph } = Typography;

/**
 * 数据迁移页面组件
 * 用于在不同的存储提供者之间迁移数据
 */
const DataMigrationPage = () => {
  const [settings, setSettings] = useState({});
  const [loading, setLoading] = useState(true);
  const [currentProvider, setCurrentProvider] = useState('');

  // 加载设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setLoading(true);
        const loadedSettings = await storageService.getSettings();
        setSettings(loadedSettings);
        setCurrentProvider(loadedSettings.storageProvider || 'huaweiObs');
      } catch (error) {
        console.error('加载设置失败:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, []);

  // 处理迁移完成
  const handleMigrationComplete = async (newProvider) => {
    try {
      // 更新当前提供者
      setCurrentProvider(newProvider);
      
      // 更新设置
      const updatedSettings = { ...settings, storageProvider: newProvider };
      await storageService.saveSettings(updatedSettings);
      setSettings(updatedSettings);
    } catch (error) {
      console.error('更新设置失败:', error);
    }
  };

  return (
    <Card title="数据迁移" bordered={false}>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
        <CloudSyncOutlined style={{ fontSize: 24, marginRight: 12, color: '#1890ff' }} />
        <Title level={4} style={{ margin: 0 }}>存储提供商迁移</Title>
      </div>
      
      <Paragraph>
        此功能允许您将数据从一个存储提供商迁移到另一个存储提供商。迁移过程中，所有数据将被复制到新的存储位置，并且系统将自动切换到新的存储提供商。
      </Paragraph>
      
      <Alert
        message="迁移前请注意"
        description="迁移过程可能需要一些时间，取决于您的数据量和网络速度。建议在迁移前备份您的数据，并确保两个存储提供商都已正确配置。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />
      
      <Divider />
      
      {!loading && (
        <StorageMigrationPanel
          currentProvider={currentProvider}
          settings={settings}
          onMigrationComplete={handleMigrationComplete}
        />
      )}
    </Card>
  );
};

export default DataMigrationPage;
