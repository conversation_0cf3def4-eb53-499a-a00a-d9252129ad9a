import React, { useState, useEffect } from 'react';
import { Layout, Typography, Menu, Card, Result, theme, Form, Select, Button, Divider, Space, Input, message, Switch, Radio, Slider, Upload, Modal, Popconfirm, Alert, Empty, Tabs } from 'antd';
import {
    SaveOutlined,
    CheckCircleOutlined,
    CloudOutlined,
    GoogleOutlined,
    AmazonOutlined,
    DatabaseOutlined,
    CloudServerOutlined
} from '@ant-design/icons';
import { cloudStorageService } from '../services';
import {
  HuaweiObsSettings,
  MinioSettings,
  TencentCosSettings,
  GoogleDriveSettings,
  AmazonS3Settings
} from './storage';

const { Header, Content, Footer, Sider } = Layout;
const { Title, Paragraph, Text } = Typography;

// 云存储设置表单
const CloudStorageSettings = ({settings, saveSettings}) => {
    const [form] = Form.useForm();
    const [testLoading, setTestLoading] = useState(false);
    // 初始化 activeProvider，优先使用设置中的值，如果没有则使用默认值
    const [activeProvider, setActiveProvider] = useState(settings.storageProvider || 'huaweiObs');

    useEffect(() => {
        // 如果设置中已有存储提供商配置，则设置为活跃提供商
        if (settings.storageProvider) {
            setActiveProvider(settings.storageProvider);
        }

        // 设置表单初始值，确保包含 storageProvider
        const formValues = {
            ...settings,
            storageProvider: settings.storageProvider || activeProvider
        };
        form.setFieldsValue(formValues);
    }, [form, settings]);

    const onFinish = async (values) => {
        // 确保保存当前选择的存储提供商
        values.storageProvider = activeProvider;
        const success = await saveSettings(values);
        if (success) {
            message.success('设置已保存');
        }
    };

    // 测试存储连接
    const testConnection = async () => {
        try {
            setTestLoading(true);
            const values = form.getFieldsValue();
            values.storageProvider = activeProvider;
            console.log(activeProvider)

            // 先保存当前设置
            await saveSettings(values);

            // 使用云存储服务测试连接
            await cloudStorageService.initialize();
            console.log("云存储服务初始化结果:" + cloudStorageService.isInitialized);
            const connected = await cloudStorageService.testConnection();
            console.log("云存储服务连接结果:" + connected);

            if (connected) {
                // 尝试列出备份
                console.log("开始列出备份");
                const listResult = await cloudStorageService.listBackups();
                console.log(listResult)
                const backupCount = listResult.backups.length;

                // 根据不同的存储提供商显示不同的成功消息
                let providerName = '';
                switch (activeProvider) {
                    case 'huaweiObs':
                        providerName = '华为云 OBS';
                        break;
                    case 'minio':
                        providerName = 'MinIO';
                        break;
                    case 'tencentCos':
                        providerName = '腾讯云 COS';
                        break;
                    case 'googleDrive':
                        providerName = 'Google Drive';
                        break;
                    case 'amazonS3':
                        providerName = 'Amazon S3';
                        break;
                    default:
                        providerName = '云存储';
                }

                message.success(`连接成功！已成功访问${providerName}存储，其中包含 ${backupCount} 个备份。`);
            }
        } catch (error) {
            console.error('连接测试错误:', error);
            message.error('连接失败：' + (error.message || '未知错误'));
        } finally {
            setTestLoading(false);
        }
    };

    // 处理存储提供商变更
    const handleProviderChange = (provider) => {
        setActiveProvider(provider);
        // 更新表单中的 storageProvider 值
        form.setFieldsValue({
            ...form.getFieldsValue(),
            storageProvider: provider
        });
    };

    // 获取当前存储提供商的设置组件
    const getProviderSettingsComponent = () => {
        switch (activeProvider) {
            case 'huaweiObs':
                return <HuaweiObsSettings form={form} />;
            case 'minio':
                return <MinioSettings form={form} />;
            case 'tencentCos':
                return <TencentCosSettings form={form} />;
            case 'googleDrive':
                return <GoogleDriveSettings form={form} />;
            case 'amazonS3':
                return <AmazonS3Settings form={form} />;
            default:
                return <HuaweiObsSettings form={form} />;
        }
    };

    // 获取当前存储提供商的标题
    const getProviderTitle = () => {
        switch (activeProvider) {
            case 'huaweiObs':
                return '华为云 OBS 设置';
            case 'minio':
                return 'MinIO 设置';
            case 'tencentCos':
                return '腾讯云 COS 设置';
            case 'googleDrive':
                return 'Google Drive 设置';
            case 'amazonS3':
                return 'Amazon S3 设置';
            default:
                return '云存储设置';
        }
    };

    // 获取当前存储提供商的描述
    const getProviderDescription = () => {
        switch (activeProvider) {
            case 'huaweiObs':
                return '请配置您的华为云 OBS (对象存储服务) 参数，用于存储和同步您的记忆数据。';
            case 'minio':
                return '请配置您的 MinIO 服务参数，用于存储和同步您的记忆数据。MinIO 是一个高性能的对象存储服务器，兼容 Amazon S3 API。';
            case 'tencentCos':
                return '请配置您的腾讯云 COS (对象存储服务) 参数，用于存储和同步您的记忆数据。';
            case 'googleDrive':
                return '请配置您的 Google Drive 参数，用于存储和同步您的记忆数据。使用 Google Drive 需要进行 OAuth 授权。';
            case 'amazonS3':
                return '请配置您的 Amazon S3 (简单存储服务) 参数，用于存储和同步您的记忆数据。';
            default:
                return '请配置您的云存储参数，用于存储和同步您的记忆数据。';
        }
    };

    // 获取提供商图标
    const getProviderIcon = (provider) => {
        switch (provider) {
            case 'huaweiObs':
                return <CloudServerOutlined />;
            case 'minio':
                return <DatabaseOutlined />;
            case 'tencentCos':
                return <CloudOutlined />;
            case 'googleDrive':
                return <GoogleOutlined />;
            case 'amazonS3':
                return <AmazonOutlined />;
            default:
                return <CloudOutlined />;
        }
    };



    return (
        <Card bordered={false}>
            <Form
                form={form}
                layout="vertical"
                initialValues={{
                    ...settings,
                    storageProvider: settings.storageProvider || activeProvider
                }}
                onFinish={onFinish}
            >
                <Form.Item
                    label="存储提供商"
                    name="storageProvider"
                    tooltip="选择您要使用的云存储提供商"
                >
                    <Radio.Group
                        onChange={(e) => handleProviderChange(e.target.value)}
                        value={activeProvider}
                        buttonStyle="solid"
                    >
                        <Radio.Button value="huaweiObs">
                            {getProviderIcon('huaweiObs')} 华为云 OBS
                        </Radio.Button>
                        <Radio.Button value="minio">
                            {getProviderIcon('minio')} MinIO
                        </Radio.Button>
                        <Radio.Button value="tencentCos" disabled={true}>
                            {getProviderIcon('tencentCos')} 腾讯云 COS
                        </Radio.Button>
                        <Radio.Button value="googleDrive" disabled={true}>
                            {getProviderIcon('googleDrive')} Google Drive
                        </Radio.Button>
                        <Radio.Button value="amazonS3" disabled={true}>
                            {getProviderIcon('amazonS3')} Amazon S3
                        </Radio.Button>
                    </Radio.Group>
                </Form.Item>

                <Card
                    title={getProviderTitle()}
                    bordered={false}
                    style={{ marginTop: 16 }}
                >
                    <Paragraph style={{ marginBottom: 24 }}>
                        {getProviderDescription()}
                    </Paragraph>

                    {getProviderSettingsComponent()}

                    <Divider />

                    <Form.Item>
                        <Space>
                            <Button
                                type="primary"
                                htmlType="submit"
                                icon={<SaveOutlined />}
                            >
                                保存设置
                            </Button>
                            <Button
                                icon={<CheckCircleOutlined />}
                                onClick={testConnection}
                                loading={testLoading}
                            >
                                测试连接
                            </Button>
                        </Space>
                    </Form.Item>
                </Card>

            </Form>
        </Card>
    );
};

export default CloudStorageSettings;
