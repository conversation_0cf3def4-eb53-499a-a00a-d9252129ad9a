import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Select,
  Button,
  Space,
  Input,
  message,
  Modal,
  Popconfirm,
  Alert,
  Layout,
  Typography,
  theme
} from 'antd';
import { CloudOutlined, DeleteOutlined, DownloadOutlined, PlusOutlined, EditOutlined, BulbOutlined, HeartOutlined, StarOutlined, BookOutlined, FlagOutlined, TagOutlined, TrophyOutlined, SmileOutlined, HomeOutlined, ShopOutlined  } from '@ant-design/icons';
import { storageService, ossStorageService } from '../services';

const { Header, Content, Footer, Sider } = Layout;
const { Title, Paragraph } = Typography;
const { useToken } = theme;

// 根据图标名称渲染图标
const renderIcon = (iconName) => {
  switch (iconName) {
    case 'BulbOutlined':
      return <BulbOutlined />;
    case 'HeartOutlined':
      return <HeartOutlined />;
    case 'StarOutlined':
      return <StarOutlined />;
    case 'BookOutlined':
      return <BookOutlined />;
    case 'FlagOutlined':
      return <FlagOutlined />;
    case 'TagOutlined':
      return <TagOutlined />;
    case 'TrophyOutlined':
      return <TrophyOutlined />;
    case 'SmileOutlined':
      return <SmileOutlined />;
    case 'HomeOutlined':
      return <HomeOutlined />;
    case 'ShopOutlined':
      return <ShopOutlined />;
    default:
      return <TagOutlined />;
  }
};

// 分类设置组件
const CategorySettings = () => {
  const [form] = Form.useForm();
  const [categories, setCategories] = useState([]);
  const [editingCategory, setEditingCategory] = useState(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalMode, setModalMode] = useState('add'); // 'add' 或 'edit'
  const [defaultCategory, setDefaultCategory] = useState('');
  const [loading, setLoading] = useState(true);

  // 可用的图标列表
  const availableIcons = [
    { name: 'BulbOutlined', label: '灯泡' },
    { name: 'HeartOutlined', label: '心形' },
    { name: 'StarOutlined', label: '星形' },
    { name: 'BookOutlined', label: '书本' },
    { name: 'FlagOutlined', label: '旗帜' },
    { name: 'TagOutlined', label: '标签' },
    { name: 'TrophyOutlined', label: '奖杯' },
    { name: 'SmileOutlined', label: '笑脸' },
    { name: 'HomeOutlined', label: '家庭' },
    { name: 'ShopOutlined', label: '商店' },
  ];

  // 从存储服务加载分类设置
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoading(true);
        const loadedSettings = await storageService.getSettings();
        setCategories(loadedSettings.categories || []);
        setDefaultCategory(loadedSettings.defaultCategory || '');
      } catch (error) {
        console.error('加载分类设置失败:', error);
        message.error('加载分类设置失败');
      } finally {
        setLoading(false);
      }
    };

    loadCategories();
  }, []);


  // 同步分离
  const syncCategories = async () => {
    // 检查OSS存储服务是否已初始化
    if (!ossStorageService || !ossStorageService.isInitialized) {
      await ossStorageService.initialize();
    }

    // 如果OSS存储服务已初始化，同步分类到OBS
    if (ossStorageService && ossStorageService.isInitialized) {
      try {
        console.log('正在同步分类到OBS...');
        await ossStorageService.syncCategories(true); // 从本地同步到OBS
        console.log('分类已同步到OBS');
      } catch (syncError) {
        console.error('同步分类到OBS失败:', syncError);
        // 不影响本地保存，所以不抛出异常
      }
    }
  }


  // 保存分类设置
  const saveCategories = async () => {
    try {
      // 获取当前设置
      const currentSettings = await storageService.getSettings();

      // 更新分类设置，并添加更新时间戳
      const updatedSettings = {
        ...currentSettings,
        categories,
        defaultCategory,
        categoriesLastUpdated: Date.now() // 添加时间戳，用于解决冲突
      };

      // 保存更新后的设置
      await storageService.saveSettings(updatedSettings);
      message.success('分类设置已保存');

      // 如果OSS存储服务已初始化，同步分类到OBS
      if (ossStorageService && ossStorageService.isInitialized) {
        try {
          console.log('正在同步分类到OBS...');
          await ossStorageService.syncCategories(true); // 从本地同步到OBS
          console.log('分类已同步到OBS');
        } catch (syncError) {
          console.error('同步分类到OBS失败:', syncError);
          // 不影响本地保存，所以不抛出异常
        }
      }

      return true;
    } catch (error) {
      console.error('保存分类设置失败:', error);
      message.error('保存分类设置失败');
      return false;
    }
  };

  // 添加新分类
  const addCategory = () => {
    setModalMode('add');
    setEditingCategory(null);
    form.resetFields();
    form.setFieldsValue({
      name: '',
      color: '#1890ff',
      icon: 'TagOutlined'
    });
    setIsModalVisible(true);
  };

  // 编辑分类
  const editCategory = (category) => {
    setModalMode('edit');
    setEditingCategory(category);
    form.resetFields();
    form.setFieldsValue({
      name: category.name,
      color: category.color,
      icon: category.icon
    });
    setIsModalVisible(true);
  };

  // 删除分类
  const deleteCategory = async (categoryId) => {
    try {
      // 检查是否为默认分类
      if (categoryId === defaultCategory) {
        message.error('无法删除默认分类，请先更改默认分类');
        return;
      }

      // 检查是否为最后一个分类
      if (categories.length <= 1) {
        message.error('必须保留至少一个分类');
        return;
      }

      const updatedCategories = categories.filter(cat => cat.id !== categoryId);
      setCategories(updatedCategories);

      // 保存到存储
      const currentSettings = await storageService.getSettings();
      const updatedSettings = {
        ...currentSettings,
        categories: updatedCategories,
        defaultCategory
      };
      await storageService.saveSettings(updatedSettings);
      await syncCategories();
      message.success('分类已删除');
    } catch (error) {
      console.error('删除分类失败:', error);
      message.error('删除分类失败');
    }
  };

  // 设置默认分类
  const setAsDefault = async (categoryId) => {
    try {
      setDefaultCategory(categoryId);

      // 保存到存储
      const currentSettings = await storageService.getSettings();
      const updatedSettings = {
        ...currentSettings,
        categories,
        defaultCategory: categoryId
      };
      await storageService.saveSettings(updatedSettings);
      message.success('默认分类已设置');
    } catch (error) {
      console.error('设置默认分类失败:', error);
      message.error('设置默认分类失败');
    }
  };

  // 处理模态框确认
  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      console.log('当前分类数量:', categories.length);

      if (modalMode === 'add') {
        // 添加新分类
        const newCategory = {
          id: Date.now().toString(), // 生成唯一ID
          name: values.name,
          color: values.color,
          icon: values.icon
        };
        console.log('添加新分类:', newCategory);

        // 创建新的分类数组，而不是修改原数组
        const updatedCategories = [...categories, newCategory];
        console.log('更新后的分类数量:', updatedCategories.length);

        // 更新状态
        setCategories(updatedCategories);

        // 如果这是第一个分类，将其设置为默认分类
        if (updatedCategories.length === 1) {
          setDefaultCategory(newCategory.id);
        }

        // 直接使用存储服务保存设置，而不是通过 saveCategories 函数
        try {
          // 获取当前设置
          const currentSettings = await storageService.getSettings();

          // 更新分类设置
          const settingsToSave = {
            ...currentSettings,
            categories: updatedCategories,
            defaultCategory: updatedCategories.length === 1 ? newCategory.id : currentSettings.defaultCategory
          };

          // 保存更新后的设置
          await storageService.saveSettings(settingsToSave);
          await syncCategories();
          console.log('分类保存成功，当前分类数量:', updatedCategories.length);
          message.success('分类已添加');
        } catch (saveError) {
          console.error('保存分类失败:', saveError);
          message.error('保存分类失败: ' + saveError.message);
        }
      } else {
        // 编辑分类
        console.log('编辑分类:', editingCategory);

        const updatedCategories = categories.map(cat => {
          if (cat.id === editingCategory.id) {
            return {
              ...cat,
              name: values.name,
              color: values.color,
              icon: values.icon
            };
          }
          return cat;
        });
        console.log('更新后的分类数量:', updatedCategories.length);

        // 更新状态
        setCategories(updatedCategories);

        // 直接使用存储服务保存设置
        try {
          // 获取当前设置
          const currentSettings = await storageService.getSettings();

          // 更新分类设置
          const settingsToSave = {
            ...currentSettings,
            categories: updatedCategories
          };

          // 保存更新后的设置
          await storageService.saveSettings(settingsToSave);
          await syncCategories();
          console.log('分类更新成功，当前分类数量:', updatedCategories.length);
          message.success('分类已更新');
        } catch (saveError) {
          console.error('保存分类失败:', saveError);
          message.error('保存分类失败: ' + saveError.message);
        }
      }

      setIsModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
      message.error('表单验证失败: ' + error.message);
    }
  };

  // 处理模态框取消
  const handleModalCancel = () => {
    setIsModalVisible(false);
  };

  // 处理颜色变化
  const handleColorChange = (e) => {
    const color = e.target.value;
    form.setFieldsValue({ color });
  };

  return (
      <Card title="分类设置" bordered={false}>
        <Paragraph style={{ marginBottom: 24 }}>
          管理您的记忆分类，添加记忆时可以选择分类，并根据分类进行筛选和搜索。
        </Paragraph>

        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={addCategory}
          >
            添加分类
          </Button>

          <Space>
            <Button
                icon={<CloudOutlined />}
                onClick={async () => {
                  try {
                    // 检查OSS存储服务是否已初始化
                    if (!ossStorageService || !ossStorageService.isInitialized) {
                      await ossStorageService.initialize();
                    }

                    if (!ossStorageService.isInitialized) {
                      message.error('云存储服务未初始化，请先配置云存储设置');
                      return;
                    }

                    message.loading('正在从本地同步分类到云端...', 1);
                    await ossStorageService.syncCategories(true); // 从本地同步到OBS
                    message.success('分类已成功同步到云端');
                  } catch (error) {
                    console.error('同步分类到云端失败:', error);
                    message.error('同步分类到云端失败: ' + error.message);
                  }
                }}
            >
              同步到云端
            </Button>

            <Button
                icon={<DownloadOutlined />}
                onClick={async () => {
                  try {
                    // 检查OSS存储服务是否已初始化
                    if (!ossStorageService || !ossStorageService.isInitialized) {
                      await ossStorageService.initialize();
                    }

                    if (!ossStorageService.isInitialized) {
                      message.error('云存储服务未初始化，请先配置云存储设置');
                      return;
                    }

                    message.loading('正在从云端同步分类到本地...', 1);
                    await ossStorageService.syncCategories(false); // 从OBS同步到本地

                    // 重新加载分类
                    const loadedSettings = await storageService.getSettings();
                    setCategories(loadedSettings.categories || []);
                    setDefaultCategory(loadedSettings.defaultCategory || '');

                    message.success('分类已成功从云端同步到本地');
                  } catch (error) {
                    console.error('从云端同步分类失败:', error);
                    message.error('从云端同步分类失败: ' + error.message);
                  }
                }}
            >
              从云端同步
            </Button>
          </Space>
        </div>

        <div style={{ marginBottom: 24 }}>
          {categories.length === 0 ? (
              <Alert
                  message="没有分类"
                  description="您还没有创建任何分类，点击上方的“添加分类”按钮创建您的第一个分类。"
                  type="info"
                  showIcon
              />
          ) : (
              <div>
                {categories.map(category => (
                    <div
                        key={category.id}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          padding: '12px',
                          marginBottom: '8px',
                          borderRadius: '4px',
                          border: `1px solid ${category.color}`,
                          backgroundColor: `${category.color}10`
                        }}
                    >
                      <div style={{
                        width: '24px',
                        height: '24px',
                        borderRadius: '50%',
                        backgroundColor: category.color,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginRight: '12px',
                        color: 'white'
                      }}>
                        {renderIcon(category.icon)}
                      </div>
                      <div style={{ flex: 1 }}>
                        <div style={{ fontWeight: 'bold' }}>{category.name}</div>
                        <div style={{ fontSize: '12px', color: '#888' }}>
                          {category.id === defaultCategory && (
                              <span style={{ color: category.color }}>默认分类</span>
                          )}
                        </div>
                      </div>
                      <Space>
                        {category.id !== defaultCategory && (
                            <Button
                                size="small"
                                onClick={() => setAsDefault(category.id)}
                            >
                              设为默认
                            </Button>
                        )}
                        <Button
                            size="small"
                            icon={<EditOutlined />}
                            onClick={() => editCategory(category)}
                        />
                        <Popconfirm
                            title="删除分类"
                            description="确定要删除这个分类吗？"
                            onConfirm={() => deleteCategory(category.id)}
                            okText="确定"
                            cancelText="取消"
                        >
                          <Button
                              size="small"
                              danger
                              icon={<DeleteOutlined />}
                              disabled={category.id === defaultCategory}
                          />
                        </Popconfirm>
                      </Space>
                    </div>
                ))}
              </div>
          )}
        </div>

        <Modal
            title={modalMode === 'add' ? '添加分类' : '编辑分类'}
            open={isModalVisible}
            onOk={handleModalOk}
            onCancel={handleModalCancel}
        >
          <Form
              form={form}
              layout="vertical"
          >
            <Form.Item
                name="name"
                label="分类名称"
                rules={[{ required: true, message: '请输入分类名称' }]}
            >
              <Input placeholder="输入分类名称" />
            </Form.Item>

            <Form.Item
                name="color"
                label="分类颜色"
                rules={[{ required: true, message: '请选择分类颜色' }]}
            >
              <Input
                  type="color"
                  onChange={handleColorChange}
                  style={{ width: '100%', height: '32px' }}
              />
            </Form.Item>

            <Form.Item
                name="icon"
                label="分类图标"
                rules={[{ required: true, message: '请选择分类图标' }]}
            >
              <Select placeholder="选择图标">
                {availableIcons.map(icon => (
                    <Select.Option key={icon.name} value={icon.name}>
                      {icon.label}
                    </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Form>
        </Modal>
      </Card>
  );
};

export default CategorySettings;