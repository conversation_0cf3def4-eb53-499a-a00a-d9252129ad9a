import React from 'react';
import { Form, Input, Select } from 'antd';

const AmazonS3Settings = ({ form }) => {
  // 区域变化时自动更新 Endpoint
  const handleRegionChange = (value) => {
    const endpoint = `s3.${value}.amazonaws.com`;
    form.setFieldsValue({
      amazonS3: {
        ...form.getFieldValue('amazonS3'),
        endpoint
      }
    });
  };

  return (
    <>
      <Form.Item
        label="Access Key ID"
        name={['amazonS3', 'accessKeyId']}
        tooltip="Amazon AWS 账号的 Access Key ID"
        rules={[{ required: true, message: '请输入 Access Key ID' }]}
      >
        <Input placeholder="输入 AWS Access Key ID" />
      </Form.Item>

      <Form.Item
        label="Secret Access Key"
        name={['amazonS3', 'secretAccessKey']}
        tooltip="Amazon AWS 账号的 Secret Access Key"
        rules={[{ required: true, message: '请输入 Secret Access Key' }]}
      >
        <Input.Password placeholder="输入 AWS Secret Access Key" />
      </Form.Item>

      <Form.Item
        label="区域"
        name={['amazonS3', 'region']}
        tooltip="Amazon S3 的区域，例如：us-east-1"
        rules={[{ required: true, message: '请选择区域' }]}
      >
        <Select onChange={handleRegionChange}>
          <Select.Option value="us-east-1">美国东部（弗吉尼亚北部）</Select.Option>
          <Select.Option value="us-east-2">美国东部（俄亥俄）</Select.Option>
          <Select.Option value="us-west-1">美国西部（加利福尼亚北部）</Select.Option>
          <Select.Option value="us-west-2">美国西部（俄勒冈）</Select.Option>
          <Select.Option value="ap-east-1">亚太地区（香港）</Select.Option>
          <Select.Option value="ap-south-1">亚太地区（孟买）</Select.Option>
          <Select.Option value="ap-northeast-1">亚太地区（东京）</Select.Option>
          <Select.Option value="ap-northeast-2">亚太地区（首尔）</Select.Option>
          <Select.Option value="ap-northeast-3">亚太地区（大阪）</Select.Option>
          <Select.Option value="ap-southeast-1">亚太地区（新加坡）</Select.Option>
          <Select.Option value="ap-southeast-2">亚太地区（悉尼）</Select.Option>
          <Select.Option value="ca-central-1">加拿大（中部）</Select.Option>
          <Select.Option value="eu-central-1">欧洲（法兰克福）</Select.Option>
          <Select.Option value="eu-west-1">欧洲（爱尔兰）</Select.Option>
          <Select.Option value="eu-west-2">欧洲（伦敦）</Select.Option>
          <Select.Option value="eu-west-3">欧洲（巴黎）</Select.Option>
          <Select.Option value="eu-north-1">欧洲（斯德哥尔摩）</Select.Option>
          <Select.Option value="sa-east-1">南美洲（圣保罗）</Select.Option>
        </Select>
      </Form.Item>

      <Form.Item
        label="Endpoint"
        name={['amazonS3', 'endpoint']}
        tooltip="Amazon S3 的 Endpoint，通常格式为：s3.[区域].amazonaws.com"
        rules={[{ required: true, message: '请输入 Endpoint' }]}
      >
        <Input placeholder="例如：s3.us-east-1.amazonaws.com" />
      </Form.Item>

      <Form.Item
        label="存储桶名称"
        name={['amazonS3', 'bucketName']}
        tooltip="Amazon S3 的存储桶名称"
        rules={[{ required: true, message: '请输入存储桶名称' }]}
      >
        <Input placeholder="输入存储桶名称" />
      </Form.Item>
    </>
  );
};

export default AmazonS3Settings;
