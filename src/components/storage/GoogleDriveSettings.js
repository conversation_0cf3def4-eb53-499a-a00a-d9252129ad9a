import React from 'react';
import { Form, Input, Button, Typography, Alert } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';

const { Paragraph, Text } = Typography;

const GoogleDriveSettings = ({ form }) => {
  return (
    <>
      <Alert
        message="Google Drive 授权说明"
        description="使用 Google Drive 需要进行 OAuth 授权。点击下方的授权按钮，登录您的 Google 账号并授权应用访问您的 Google Drive。"
        type="info"
        showIcon
        icon={<InfoCircleOutlined />}
        style={{ marginBottom: 16 }}
      />

      <Form.Item
        label="Client ID"
        name={['googleDrive', 'clientId']}
        tooltip="Google API Console 中创建的 OAuth 客户端 ID"
        rules={[{ required: true, message: '请输入 Client ID' }]}
      >
        <Input placeholder="输入 Google OAuth Client ID" />
      </Form.Item>

      <Form.Item
        label="Client Secret"
        name={['googleDrive', 'clientSecret']}
        tooltip="Google API Console 中创建的 OAuth 客户端密钥"
        rules={[{ required: true, message: '请输入 Client Secret' }]}
      >
        <Input.Password placeholder="输入 Google OAuth Client Secret" />
      </Form.Item>

      <Form.Item
        label="重定向 URI"
        name={['googleDrive', 'redirectUri']}
        tooltip="OAuth 授权后的重定向 URI，必须与 Google API Console 中设置的一致"
        rules={[{ required: true, message: '请输入重定向 URI' }]}
        initialValue={chrome.identity ? chrome.identity.getRedirectURL() : ''}
      >
        <Input placeholder="例如：https://example.com/oauth2callback" />
      </Form.Item>

      <Form.Item
        label="授权状态"
        name={['googleDrive', 'authStatus']}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Text type={form.getFieldValue(['googleDrive', 'refreshToken']) ? 'success' : 'danger'}>
            {form.getFieldValue(['googleDrive', 'refreshToken']) ? '已授权' : '未授权'}
          </Text>
          <Button 
            type="primary" 
            style={{ marginLeft: 16 }}
          >
            {form.getFieldValue(['googleDrive', 'refreshToken']) ? '重新授权' : '授权 Google Drive'}
          </Button>
        </div>
      </Form.Item>

      <Form.Item
        label="文件夹 ID"
        name={['googleDrive', 'folderId']}
        tooltip="Google Drive 中用于存储备份的文件夹 ID，留空将使用根目录"
      >
        <Input placeholder="输入 Google Drive 文件夹 ID（可选）" />
      </Form.Item>

      <Paragraph type="secondary" style={{ marginTop: -8, marginBottom: 16 }}>
        文件夹 ID 可以从 Google Drive 文件夹的 URL 中获取，例如：https://drive.google.com/drive/folders/1a2b3c4d5e6f7g8h9i0j，其中 1a2b3c4d5e6f7g8h9i0j 就是文件夹 ID。
      </Paragraph>
    </>
  );
};

export default GoogleDriveSettings;
