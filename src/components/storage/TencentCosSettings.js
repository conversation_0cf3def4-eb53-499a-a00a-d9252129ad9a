import React from 'react';
import { Form, Input, Select } from 'antd';

const TencentCosSettings = ({ form }) => {
  // 区域变化时自动更新 Endpoint
  const handleRegionChange = (value) => {
    const endpoint = `cos.${value}.myqcloud.com`;
    form.setFieldsValue({
      tencentCos: {
        ...form.getFieldValue('tencentCos'),
        endpoint
      }
    });
  };

  return (
    <>
      <Form.Item
        label="SecretId"
        name={['tencentCos', 'secretId']}
        tooltip="腾讯云账号的 SecretId"
        rules={[{ required: true, message: '请输入 SecretId' }]}
      >
        <Input placeholder="输入腾讯云 SecretId" />
      </Form.Item>

      <Form.Item
        label="SecretKey"
        name={['tencentCos', 'secretKey']}
        tooltip="腾讯云账号的 SecretKey"
        rules={[{ required: true, message: '请输入 SecretKey' }]}
      >
        <Input.Password placeholder="输入腾讯云 SecretKey" />
      </Form.Item>

      <Form.Item
        label="区域"
        name={['tencentCos', 'region']}
        tooltip="腾讯云 COS 的区域，例如：ap-beijing"
        rules={[{ required: true, message: '请选择区域' }]}
      >
        <Select onChange={handleRegionChange}>
          <Select.Option value="ap-beijing">华北地区(北京)</Select.Option>
          <Select.Option value="ap-nanjing">华东地区(南京)</Select.Option>
          <Select.Option value="ap-shanghai">华东地区(上海)</Select.Option>
          <Select.Option value="ap-guangzhou">华南地区(广州)</Select.Option>
          <Select.Option value="ap-chengdu">西南地区(成都)</Select.Option>
          <Select.Option value="ap-chongqing">西南地区(重庆)</Select.Option>
          <Select.Option value="ap-hongkong">中国香港</Select.Option>
          <Select.Option value="ap-singapore">新加坡</Select.Option>
          <Select.Option value="ap-mumbai">孟买</Select.Option>
          <Select.Option value="ap-seoul">首尔</Select.Option>
          <Select.Option value="ap-bangkok">曼谷</Select.Option>
          <Select.Option value="ap-tokyo">东京</Select.Option>
        </Select>
      </Form.Item>

      <Form.Item
        label="Endpoint"
        name={['tencentCos', 'endpoint']}
        tooltip="腾讯云 COS 的 Endpoint，通常格式为：cos.[区域].myqcloud.com"
        rules={[{ required: true, message: '请输入 Endpoint' }]}
      >
        <Input placeholder="例如：cos.ap-beijing.myqcloud.com" />
      </Form.Item>

      <Form.Item
        label="存储桶名称"
        name={['tencentCos', 'bucketName']}
        tooltip="腾讯云 COS 的存储桶名称"
        rules={[{ required: true, message: '请输入存储桶名称' }]}
      >
        <Input placeholder="输入存储桶名称" />
      </Form.Item>
    </>
  );
};

export default TencentCosSettings;
