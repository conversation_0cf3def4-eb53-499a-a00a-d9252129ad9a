import React from 'react';
import { Form, Input, Switch } from 'antd';

const MinioSettings = ({ form }) => {
  return (
    <>
      <Form.Item
        label="Access Key"
        name={['minio', 'accessKey']}
        tooltip="MinIO 服务的 Access Key"
        rules={[{ required: true, message: '请输入 Access Key' }]}
      >
        <Input placeholder="输入 MinIO Access Key" />
      </Form.Item>

      <Form.Item
        label="Secret Key"
        name={['minio', 'secretKey']}
        tooltip="MinIO 服务的 Secret Key"
        rules={[{ required: true, message: '请输入 Secret Key' }]}
      >
        <Input.Password placeholder="输入 MinIO Secret Key" />
      </Form.Item>

      <Form.Item
        label="服务器地址"
        name={['minio', 'endPoint']}
        tooltip="MinIO 服务器地址，例如：play.min.io"
        rules={[{ required: true, message: '请输入服务器地址' }]}
      >
        <Input placeholder="例如：play.min.io" />
      </Form.Item>

      <Form.Item
        label="端口"
        name={['minio', 'port']}
        tooltip="MinIO 服务器端口，默认为 9000"
        rules={[{ required: true, message: '请输入端口' }]}
        initialValue="9000"
      >
        <Input placeholder="例如：9000" />
      </Form.Item>

      <Form.Item
        label="使用 SSL"
        name={['minio', 'useSSL']}
        tooltip="是否使用 SSL 连接 MinIO 服务器"
        valuePropName="checked"
        initialValue={true}
      >
        <Switch />
      </Form.Item>

      <Form.Item
        label="存储桶名称"
        name={['minio', 'bucketName']}
        tooltip="MinIO 的存储桶名称"
        rules={[{ required: true, message: '请输入存储桶名称' }]}
      >
        <Input placeholder="输入存储桶名称" />
      </Form.Item>
    </>
  );
};

export default MinioSettings;
