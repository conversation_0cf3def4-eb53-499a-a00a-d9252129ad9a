import React from 'react';
import { Form, Input, Select } from 'antd';

const HuaweiObsSettings = ({ form }) => {
  // 区域变化时自动更新 Endpoint
  const handleRegionChange = (value) => {
    const endpoint = `obs.${value}.myhuaweicloud.com`;
    form.setFieldsValue({
      huaweiObs: {
        ...form.getFieldValue('huaweiObs'),
        endpoint
      }
    });
  };

  return (
    <>
      <Form.Item
        label="Access Key ID"
        name={['huaweiObs', 'accessKeyId']}
        tooltip="华为云账号的 Access Key ID"
        rules={[{ required: true, message: '请输入 Access Key ID' }]}
      >
        <Input placeholder="输入华为云 Access Key ID" />
      </Form.Item>

      <Form.Item
        label="Secret Access Key"
        name={['huaweiObs', 'secretAccessKey']}
        tooltip="华为云账号的 Secret Access Key"
        rules={[{ required: true, message: '请输入 Secret Access Key' }]}
      >
        <Input.Password placeholder="输入华为云 Secret Access Key" />
      </Form.Item>

      <Form.Item
        label="区域"
        name={['huaweiObs', 'region']}
        tooltip="华为云 OBS 的区域，例如：cn-north-4"
        rules={[{ required: true, message: '请选择区域' }]}
      >
        <Select onChange={handleRegionChange}>
          <Select.Option value="cn-north-4">华北-北京四</Select.Option>
          <Select.Option value="cn-north-1">华北-北京一</Select.Option>
          <Select.Option value="cn-east-2">华东-上海二</Select.Option>
          <Select.Option value="cn-east-3">华东-上海一</Select.Option>
          <Select.Option value="cn-south-1">华南-广州</Select.Option>
          <Select.Option value="cn-southwest-2">西南-贵阳一</Select.Option>
          <Select.Option value="ap-southeast-1">亚太-香港</Select.Option>
          <Select.Option value="ap-southeast-2">亚太-曼谷</Select.Option>
          <Select.Option value="ap-southeast-3">亚太-新加坡</Select.Option>
        </Select>
      </Form.Item>

      <Form.Item
        label="Endpoint"
        name={['huaweiObs', 'endpoint']}
        tooltip="华为云 OBS 的 Endpoint，通常格式为：obs.[区域].myhuaweicloud.com"
        rules={[{ required: true, message: '请输入 Endpoint' }]}
      >
        <Input placeholder="例如：obs.cn-north-4.myhuaweicloud.com" />
      </Form.Item>

      <Form.Item
        label="存储桶名称"
        name={['huaweiObs', 'bucketName']}
        tooltip="华为云 OBS 的存储桶名称"
        rules={[{ required: true, message: '请输入存储桶名称' }]}
      >
        <Input placeholder="输入存储桶名称" />
      </Form.Item>
    </>
  );
};

export default HuaweiObsSettings;
