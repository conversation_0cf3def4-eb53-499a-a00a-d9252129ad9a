import React, { useState, useEffect } from 'react';
import { Button, Space, Typography, Alert, Modal } from 'antd';
import { CloudOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import migrationTool from '../utils/MigrationTool';

const { Paragraph } = Typography;

/**
 * 迁移工具入口组件
 * 用于在选项页面中显示迁移工具入口
 */
const MigrationToolEntry = () => {
  const [needsMigration, setNeedsMigration] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  const [memoryCount, setMemoryCount] = useState(0);

  // 检查是否需要迁移
  useEffect(() => {
    const checkMigration = async () => {
      try {
        // 检查是否需要迁移
        const needsMigration = await migrationTool.checkNeedsMigration();
        setNeedsMigration(needsMigration);
      } catch (error) {
        console.error('检查迁移状态失败:', error);
      } finally {
        setIsChecking(false);
      }
    };
    
    checkMigration();
  }, []);

  // 打开迁移工具
  const openMigrationTool = () => {
    // 在新标签页中打开迁移工具
    window.open(chrome.runtime.getURL('migration.html'), '_blank');
  };

  // 显示迁移确认对话框
  const showMigrationConfirm = () => {
    Modal.confirm({
      title: '数据迁移',
      icon: <ExclamationCircleOutlined />,
      content: '数据迁移工具将帮助您将记忆数据转换为新的分块存储结构，以支持大规模记忆存储（10万条以上）。确定要继续吗？',
      onOk: openMigrationTool,
      okText: '继续',
      cancelText: '取消',
    });
  };

  return (
    <div style={{ marginBottom: '24px' }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        {needsMigration && (
          <Alert
            message="建议进行数据迁移"
            description="检测到您的记忆数据使用的是旧的存储结构，建议进行数据迁移以支持大规模记忆存储。"
            type="warning"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        )}
        
        <Space>
          <Button
            type="primary"
            icon={<CloudOutlined />}
            onClick={showMigrationConfirm}
          >
            数据迁移工具
          </Button>
        </Space>

        <Paragraph type="secondary">
          使用数据迁移工具将记忆数据转换为新的分块存储结构，以支持大规模记忆存储（10万条以上）。
        </Paragraph>
      </Space>
    </div>
  );
};

export default MigrationToolEntry;
