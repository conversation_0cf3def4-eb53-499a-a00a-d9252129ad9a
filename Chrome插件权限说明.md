# Chrome插件权限申请说明

## 📋 权限申请理由说明

拾光忆栈插件申请了以下三个权限，每个权限都有明确的使用目的和必要性：

### 🗄️ "storage" 权限

**申请理由：**
- **本地数据存储**：需要在用户设备上存储插件的配置信息、用户设置、临时数据等
- **记忆数据缓存**：为了提高用户体验，需要在本地缓存部分记忆数据，实现快速加载
- **离线功能支持**：当网络不稳定时，用户仍能查看已缓存的记忆内容
- **用户偏好保存**：存储用户的个性化设置，如默认分类、标签偏好、界面配置等
- **同步状态管理**：记录数据同步状态，避免重复上传或下载

**具体使用场景：**
- 保存云存储配置信息（加密存储）
- 缓存记忆列表和元数据
- 存储用户的分类和标签设置
- 记录最后同步时间和状态
- 保存用户界面偏好设置

### 🌐 "tabs" 权限

**申请理由：**
- **获取当前网页信息**：当用户保存记忆时，需要自动获取当前网页的标题、URL等信息作为记忆的来源
- **智能内容提取**：根据当前网页的内容，为记忆自动生成合适的标题和标签建议
- **上下文感知**：了解用户正在浏览的内容，提供更精准的记忆分类建议
- **来源追踪**：为每个记忆记录其来源网页，方便用户日后回溯

**具体使用场景：**
- 用户右键保存内容时，自动填充网页标题作为记忆标题
- 记录记忆的来源URL，方便用户点击回到原网页
- 根据网页域名自动建议相关标签
- 在记忆详情中显示来源网站信息

### 📝 "contextMenus" 权限

**申请理由：**
- **便捷的保存入口**：在用户右键菜单中添加"保存到拾光忆栈"选项，提供最直观的保存方式
- **选中内容保存**：当用户选中文本、图片等内容时，通过右键菜单快速保存
- **提升用户体验**：减少用户操作步骤，无需打开插件界面即可快速保存内容
- **符合用户习惯**：右键菜单是用户最熟悉的操作方式之一

**具体使用场景：**
- 用户选中网页文本后，右键选择"保存到拾光忆栈"
- 右键点击图片时，显示"保存图片到拾光忆栈"选项
- 在不同类型的内容上显示相应的保存选项
- 提供快速保存和详细保存两种模式的入口

## 🔒 隐私保护承诺

### 权限使用原则：
1. **最小权限原则**：只申请必要的权限，不申请多余权限
2. **透明使用**：所有权限的使用都有明确目的，不进行隐秘操作
3. **用户控制**：用户可以随时查看和控制数据的使用
4. **数据安全**：所有敏感数据都进行加密存储

### 数据处理说明：
- **storage权限**：仅存储用户主动保存的内容和必要的配置信息
- **tabs权限**：仅读取当前活动标签页的基本信息（标题、URL），不访问页面内容
- **contextMenus权限**：仅用于添加右键菜单项，不收集用户的右键操作数据

## 📊 权限使用统计

| 权限 | 使用频率 | 主要用途 | 用户受益 |
|------|----------|----------|----------|
| storage | 高频 | 配置存储、数据缓存 | 快速加载、离线访问 |
| tabs | 中频 | 获取网页信息 | 自动填充、来源追踪 |
| contextMenus | 高频 | 右键菜单保存 | 操作便捷、体验流畅 |

## ✅ 用户权益保障

1. **可撤销性**：用户可以随时在Chrome设置中撤销这些权限
2. **功能降级**：即使撤销某些权限，插件的核心功能仍然可用
3. **透明度**：所有权限使用都有详细的日志记录（仅用户可见）
4. **开源验证**：代码开源，用户和社区可以验证权限的实际使用情况

## 🎯 总结

这三个权限都是为了提供更好的用户体验而申请的：
- **storage**：确保数据安全存储和快速访问
- **tabs**：提供智能的内容识别和来源记录
- **contextMenus**：提供最便捷的保存操作方式

我们承诺严格按照声明的用途使用这些权限，绝不进行任何可能损害用户隐私或安全的操作。
