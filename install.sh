#!/bin/bash

# Install dependencies
echo "Installing dependencies..."
npm install

# Build the extension in development mode with watch
echo "Starting development build with watch mode..."
echo "This will continuously rebuild the extension as you make changes."
echo "Press Ctrl+C to stop the build process."
echo ""
echo "To install in Chrome:"
echo "1. Open Chrome and go to chrome://extensions/"
echo "2. Enable 'Developer mode'"
echo "3. Click 'Load unpacked' and select the 'dist' folder"
echo ""

npm start
