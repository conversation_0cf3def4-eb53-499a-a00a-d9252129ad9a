<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="652ec426-db7a-4a7c-95dc-21c8da862a47" name="Changes" comment="fix: 完成分块上传下载的测试" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "git-widget-placeholder": "google",
    "node.js.selected.package.tslint": "(autodetect)"
  }
}]]></component>
  <component name="TaskManager">
    <servers />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix: 完成分块上传下载的测试" />
    <option name="LAST_COMMIT_MESSAGE" value="fix: 完成分块上传下载的测试" />
  </component>
</project>