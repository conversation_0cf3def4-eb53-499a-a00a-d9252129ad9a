# Chrome插件权限优化方案

## 🎯 当前问题
- 使用 `<all_urls>` 导致申请了过于宽泛的 host permission
- Chrome商店审核对此类权限要求严格
- 需要最小化权限申请以提高审核通过率

## 🔧 优化方案

### 方案1: 使用 `activeTab` 权限（推荐）

#### 修改后的权限配置
```json
{
  "permissions": [
    "storage",
    "activeTab", 
    "contextMenus"
  ]
}
```

#### 优势
- **最小权限**: 只在用户主动与插件交互时才获取当前标签页权限
- **审核友好**: Chrome商店更容易批准activeTab权限
- **用户信任**: 用户更容易理解和接受这种权限模式

#### 实现调整

**1. 保留content scripts但优化触发方式**
```json
"content_scripts": [
  {
    "matches": ["<all_urls>"],
    "js": ["content.js"],
    "run_at": "document_idle"
  }
]
```

**2. 修改content script实现**
```javascript
// 优化后的content.js
// 只在用户主动操作时才激活功能

// 监听来自background的激活消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'activateContentScript') {
    // 激活快捷键监听
    activateKeyboardListeners();
    // 激活右键菜单功能
    activateContextMenuListeners();
  }
  
  if (message.action === 'saveSelectedText') {
    // 处理保存功能
    handleSaveSelectedText();
  }
});

// 延迟激活功能，减少性能影响
function activateKeyboardListeners() {
  if (!window.memoryKeeperActivated) {
    document.addEventListener('keydown', handleKeyboardShortcut);
    window.memoryKeeperActivated = true;
  }
}
```

**3. 修改background script**
```javascript
// 在用户点击右键菜单时激活content script
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'saveToMemoryManager') {
    // 先激活content script
    chrome.tabs.sendMessage(tab.id, { action: 'activateContentScript' });
    // 然后执行保存
    chrome.tabs.sendMessage(tab.id, { action: 'saveSelectedText' });
  }
});

// 在用户点击插件图标时也可以激活
chrome.action.onClicked.addListener((tab) => {
  chrome.tabs.sendMessage(tab.id, { action: 'activateContentScript' });
});
```

### 方案2: 移除content scripts，纯右键菜单模式

#### 配置
```json
{
  "permissions": [
    "storage",
    "activeTab",
    "contextMenus"
  ]
  // 移除 content_scripts
}
```

#### 实现
- 只通过右键菜单触发功能
- 在background script中处理所有逻辑
- 使用chrome.tabs.executeScript动态注入代码

### 方案3: 限制特定域名（不推荐）

#### 配置示例
```json
"content_scripts": [
  {
    "matches": [
      "https://*.edu/*",
      "https://*.com/*", 
      "https://*.org/*",
      "https://*.net/*"
    ],
    "js": ["content.js"]
  }
]
```

#### 缺点
- 仍然覆盖范围很广
- 无法覆盖所有用户可能使用的网站
- 维护成本高

## 📋 推荐实施方案

### 第一步: 修改manifest.json
```json
{
  "manifest_version": 3,
  "name": "拾光忆栈",
  "permissions": [
    "storage",
    "activeTab",
    "contextMenus"
  ],
  "content_scripts": [
    {
      "matches": ["<all_urls>"],
      "js": ["content.js"],
      "run_at": "document_idle"
    }
  ]
}
```

### 第二步: 优化content script
- 添加激活机制，只在需要时启用功能
- 减少默认监听器，提高性能
- 添加权限检查逻辑

### 第三步: 更新权限申请说明

#### 新的权限申请理由
```
1. storage - 本地数据存储
2. activeTab - 仅在用户主动使用插件时访问当前标签页
3. contextMenus - 右键菜单功能

说明：
- 使用activeTab替代tabs权限，最小化权限申请
- 只在用户主动触发时才访问页面内容
- 不收集任何用户数据，仅提供保存功能
```

## 🔒 隐私保护增强

### 1. 明确的用户控制
- 所有功能都需要用户主动触发
- 提供清晰的权限使用说明
- 支持功能开关控制

### 2. 最小化数据访问
- 只读取用户选中的内容
- 不访问页面的其他部分
- 不进行后台数据收集

### 3. 透明的权限使用
- 在插件说明中详细解释每个权限的用途
- 提供开源代码供审查
- 定期更新隐私政策

## 📊 方案对比

| 方案 | 权限范围 | 审核难度 | 功能完整性 | 用户体验 |
|------|----------|----------|------------|----------|
| activeTab | 最小 | 低 | 高 | 良好 |
| 移除content scripts | 最小 | 最低 | 中等 | 一般 |
| 限制域名 | 中等 | 中等 | 中等 | 良好 |
| 当前方案(<all_urls>) | 最大 | 高 | 最高 | 最佳 |

## 🎯 建议

**立即实施**: 方案1 (activeTab + 优化content scripts)
- 权限最小化
- 保持功能完整性
- 提高审核通过率
- 维持良好用户体验

这个方案在权限最小化和功能完整性之间取得了最佳平衡。
