#!/bin/bash

# 设置颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}开始构建拾光忆栈扩展...${NC}"

# 清理之前的构建
echo "清理之前的构建..."
rm -rf dist

# 检查并安装依赖
echo "检查依赖..."
MISSING_DEPS=false

# 检查 package.json 中的依赖是否已安装
check_and_install_deps() {
  local deps=("pako" "uuid" "lodash")

  for dep in "${deps[@]}"; do
    if ! grep -q "\"$dep\":" node_modules/$dep/package.json 2>/dev/null; then
      echo -e "${YELLOW}缺少依赖: $dep, 正在安装...${NC}"
      npm install $dep --save
      MISSING_DEPS=true
    fi
  done

  if [ "$MISSING_DEPS" = true ]; then
    echo -e "${GREEN}所有缺失的依赖已安装${NC}"
  else
    echo -e "${GREEN}所有依赖已安装${NC}"
  fi
}

check_and_install_deps

# 构建扩展
echo -e "${GREEN}开始构建...${NC}"
npm run build

# 检查构建结果
if [ $? -eq 0 ]; then
  echo -e "${GREEN}构建成功!${NC}"
  echo "扩展已构建到 dist 目录"
  echo ""
  echo "在 Chrome 中安装扩展:"
  echo "1. 打开 Chrome 并访问 chrome://extensions/"
  echo "2. 启用 '开发者模式'"
  echo "3. 点击 '加载已解压的扩展程序' 并选择 'dist' 文件夹"
else
  echo -e "${RED}构建失败!${NC}"
  echo "请检查错误信息并修复问题"
  exit 1
fi

# 显示构建文件大小信息
echo ""
echo -e "${YELLOW}构建文件大小信息:${NC}"
find dist -type f -name "*.js" | sort | xargs ls -lh | awk '{print $5 "\t" $9}'

echo ""
echo -e "${GREEN}完成!${NC}"
