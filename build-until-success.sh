#!/bin/bash

# Set maximum number of attempts
MAX_ATTEMPTS=5
ATTEMPT=1
SUCCESS=false

echo "Starting build process with up to $MAX_ATTEMPTS attempts..."

# Clean up previous build
rm -rf dist

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
  echo "Installing dependencies..."
  npm install
fi

while [ $ATTEMPT -le $MAX_ATTEMPTS ] && [ "$SUCCESS" = false ]; do
  echo "Build attempt $ATTEMPT of $MAX_ATTEMPTS..."
  
  # Run the build
  npm run build
  
  # Check if build was successful
  if [ $? -eq 0 ]; then
    SUCCESS=true
    echo "Build successful on attempt $ATTEMPT!"
  else
    echo "Build failed on attempt $ATTEMPT."
    
    if [ $ATTEMPT -lt $MAX_ATTEMPTS ]; then
      echo "Waiting 2 seconds before next attempt..."
      sleep 2
    fi
    
    ATTEMPT=$((ATTEMPT+1))
  fi
done

if [ "$SUCCESS" = true ]; then
  echo "Build completed successfully! The extension is in the 'dist' folder."
  echo "To install in Chrome:"
  echo "1. Open Chrome and go to chrome://extensions/"
  echo "2. Enable 'Developer mode'"
  echo "3. Click 'Load unpacked' and select the 'dist' folder"
  exit 0
else
  echo "Failed to build after $MAX_ATTEMPTS attempts."
  exit 1
fi
