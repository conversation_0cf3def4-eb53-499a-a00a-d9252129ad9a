# Chrome插件权限申请详细说明

## 📋 权限申请概述

拾光忆栈插件申请了以下三个权限，每个权限都有明确的使用目的和必要性。以下是基于实际代码分析的详细说明：

---

## 🗄️ "storage" 权限

### 申请理由
此权限用于在用户设备上存储插件的配置信息、用户数据和缓存，确保插件能够正常运行并提供良好的用户体验。

### 具体使用场景（基于代码分析）

#### 1. 用户设置和配置存储
**文件位置**: `src/services/StorageService.js`
- **存储内容**: 用户信息、云存储配置、同步设置、外观设置、分类设置等
- **使用方法**: `chrome.storage.local.get(['settings'])` 和 `chrome.storage.local.set({ settings })`
- **目的**: 保存用户的个性化配置，避免每次使用都需要重新设置

#### 2. 记忆数据本地缓存
**文件位置**: `src/services/StorageService.js`, `src/browse-memories/BrowseMemories.js`
- **存储内容**: 用户保存的记忆列表和元数据
- **使用方法**: `chrome.storage.local.get(['memories'])` 和 `chrome.storage.local.set({ memories })`
- **目的**: 提供离线访问能力，提高加载速度

#### 3. 设备标识和同步状态管理
**文件位置**: `src/services/StorageService.js` (第168-183行)
- **存储内容**: 设备ID、数据版本号、最后修改时间、同步锁信息
- **使用方法**: `chrome.storage.local.get(['device_id'])` 和相关锁机制
- **目的**: 支持多设备同步，防止数据冲突

#### 4. 快速保存功能
**文件位置**: `src/content/index.js` (第138-154行)
- **存储内容**: 选中的文本内容、新创建的记忆
- **使用方法**: `chrome.storage.local.set({ selectedText })` 和 `chrome.storage.local.get(['memories'])`
- **目的**: 支持右键菜单快速保存功能

#### 5. 自动备份和数据恢复
**文件位置**: `src/services/StorageService.js` (第972-980行)
- **存储内容**: 自动备份数据
- **使用方法**: `chrome.storage.local.set({ autoBackup: JSON.stringify(backup) })`
- **目的**: 在数据清理前自动备份，确保数据安全

### 数据安全保障
- 所有敏感数据都经过加密处理
- 仅存储用户主动保存的内容
- 支持数据导出和清理功能
- 不收集或传输用户隐私数据

---

## 🌐 "tabs" 权限

### 申请理由
此权限用于获取当前网页的基本信息（标题、URL），为用户保存记忆时自动填充来源信息，提升用户体验。

### 具体使用场景（基于代码分析）

#### 1. 右键菜单保存功能
**文件位置**: `src/background/index.js` (第92-97行)
- **使用方法**: `chrome.tabs.sendMessage(tab.id, { action: 'saveSelectedText' })`
- **目的**: 当用户通过右键菜单保存内容时，向当前标签页发送消息

#### 2. 打开新标签页功能
**文件位置**: `src/background/index.js` (第152行), `src/popup/Popup.js` (第24行)
- **使用方法**: `chrome.tabs.create({ url: 'browse-memories.html' })` 和 `chrome.tabs.create({ url })`
- **目的**: 打开插件的各个功能页面，如浏览记忆页面、文档页面等

#### 3. 自动获取网页信息
**文件位置**: `src/content/index.js` (第133行)
- **获取信息**: `window.location.href` (当前页面URL)
- **目的**: 为保存的记忆自动记录来源网页，方便用户日后回溯

### 权限使用限制
- **仅读取基本信息**: 只获取标题、URL等基本信息，不访问页面内容
- **不收集浏览历史**: 不记录或传输用户的浏览历史
- **用户主动触发**: 只在用户主动保存内容时才获取页面信息

---

## 📝 "contextMenus" 权限

### 申请理由
此权限用于在网页右键菜单中添加"保存到拾光忆栈"选项，为用户提供最便捷的内容保存方式。

### 具体使用场景（基于代码分析）

#### 1. 创建右键菜单项
**文件位置**: `src/background/index.js` (第5-11行)
- **使用方法**: `chrome.contextMenus.create({ id: 'saveToMemoryManager', title: '保存到拾光忆栈', contexts: ['selection'] })`
- **目的**: 在用户选中文本时显示保存选项

#### 2. 处理右键菜单点击
**文件位置**: `src/background/index.js` (第92-97行)
- **使用方法**: `chrome.contextMenus.onClicked.addListener((info, tab) => { ... })`
- **目的**: 当用户点击右键菜单项时，触发保存功能

#### 3. 选中文本预处理
**文件位置**: `src/content/index.js` (第262-268行)
- **功能**: 监听右键菜单事件，预先保存选中的文本
- **目的**: 确保右键菜单能够访问到用户选中的内容

### 用户体验优势
- **操作便捷**: 用户无需打开插件界面即可快速保存内容
- **符合习惯**: 右键菜单是用户最熟悉的操作方式
- **减少步骤**: 从选中文本到保存完成只需2步操作

---

## 🔒 隐私保护承诺

### 权限使用原则
1. **最小权限原则**: 只申请必要的权限，不申请多余权限
2. **透明使用**: 所有权限的使用都有明确目的和代码实现
3. **用户控制**: 用户可以随时查看、修改或删除存储的数据
4. **数据安全**: 敏感数据加密存储，支持私有云存储

### 具体保护措施
- **本地存储**: 数据主要存储在用户本地，不上传到第三方服务器
- **加密保护**: 敏感配置信息支持加密存储
- **开源透明**: 代码完全开源，接受社区监督
- **用户控制**: 提供完整的数据导出、导入和清理功能

---

## 📊 权限使用统计

| 权限 | 使用频率 | 主要API调用 | 用户受益 |
|------|----------|-------------|----------|
| storage | 高频 | `chrome.storage.local.get/set` | 快速加载、离线访问、设置保存 |
| tabs | 中频 | `chrome.tabs.create/sendMessage` | 自动填充来源、便捷导航 |
| contextMenus | 高频 | `chrome.contextMenus.create/onClicked` | 快速保存、操作便捷 |

---

## ✅ 审核要点说明

### 1. 权限必要性
- 每个权限都有明确的功能需求和代码实现
- 移除任何一个权限都会导致核心功能无法正常工作
- 权限使用范围严格限制在声明的用途内

### 2. 用户隐私保护
- 不收集用户个人信息或浏览数据
- 所有数据处理都在用户设备本地进行
- 支持用户完全控制自己的数据

### 3. 功能透明度
- 代码完全开源，可在GitHub查看完整实现
- 所有权限使用都有详细的代码注释和文档说明
- 用户可以随时查看插件的实际行为

### 4. 安全性保障
- 采用Chrome扩展最佳安全实践
- 支持数据加密和安全传输
- 定期更新以修复潜在安全问题

---

## 🎯 总结

拾光忆栈插件申请的三个权限都是为了实现核心功能而必需的：

- **storage权限**: 确保用户数据和设置的安全存储与快速访问
- **tabs权限**: 提供智能的内容识别和便捷的页面导航
- **contextMenus权限**: 提供最直观便捷的内容保存方式

我们承诺严格按照声明的用途使用这些权限，绝不进行任何可能损害用户隐私或安全的操作。所有代码都是开源的，欢迎审核和监督。
