# 文档图片目录

此目录包含拾光忆栈文档中使用的所有图片和图表。

## 图片组织

图片按照文档章节组织：

- `getting-started/` - 快速开始章节的图片
- `basic-features/` - 基本功能章节的图片
- `advanced-features/` - 高级功能章节的图片
- `settings/` - 设置与配置章节的图片
- `troubleshooting/` - 故障排除章节的图片
- `developers/` - 开发者文档章节的图片

## 图片命名规则

图片使用以下命名规则：

- 使用小写字母和连字符
- 名称应描述图片内容
- 例如：`popup-interface.png`, `sync-settings.png`

## 图片格式

- 屏幕截图和界面图片使用PNG格式
- 照片和复杂图像使用JPEG格式
- 图标和简单图形使用SVG格式（如可能）

## 图片尺寸

- 界面截图宽度通常为800-1200像素
- 缩略图宽度通常为400-600像素
- 图标尺寸通常为32x32或64x64像素

## 添加新图片

添加新图片时，请遵循以下步骤：

1. 将图片放在适当的子目录中
2. 使用描述性文件名
3. 优化图片大小（压缩大图片）
4. 更新本README文件，如有必要

## 图片版权

除非另有说明，所有图片均为拾光忆栈项目原创，遵循项目的MIT许可证。
