# 测试框架文档

## 概述

本项目使用Jest作为测试框架，配合React Testing Library进行React组件测试。测试框架已完全配置，支持单元测试、集成测试和覆盖率报告。

## 测试框架配置

### 核心依赖
- **Jest**: 主要测试框架
- **@testing-library/react**: React组件测试工具
- **@testing-library/jest-dom**: Jest DOM断言扩展
- **@testing-library/user-event**: 用户事件模拟
- **jest-environment-jsdom**: DOM环境模拟
- **babel-jest**: Babel转换支持

### 配置文件

#### jest.config.js
```javascript
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.js'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@components/(.*)$': '<rootDir>/src/presentation/components/$1',
    '^@services/(.*)$': '<rootDir>/src/application/services/$1',
    '^@domain/(.*)$': '<rootDir>/src/domain/$1',
    '^@infrastructure/(.*)$': '<rootDir>/src/infrastructure/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': '<rootDir>/src/test/__mocks__/fileMock.js'
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx}',
    '!src/test/**',
    '!src/**/*.test.{js,jsx}',
    '!src/**/__tests__/**',
    '!src/index.js',
    '!src/manifest.json'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

## Chrome扩展API模拟

### 全局Chrome对象模拟
测试环境中完整模拟了Chrome扩展API：

```javascript
global.chrome = {
  storage: {
    local: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn()
    },
    sync: {
      get: jest.fn(),
      set: jest.fn()
    }
  },
  runtime: {
    sendMessage: jest.fn(),
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    getURL: jest.fn(),
    lastError: null
  },
  tabs: {
    query: jest.fn(),
    create: jest.fn(),
    update: jest.fn()
  }
};
```

### 测试工具函数
提供了便捷的测试工具函数：

```javascript
global.testUtils = {
  // 模拟Chrome存储数据
  mockChromeStorage: (data) => { /* ... */ },
  
  // 创建测试数据
  createTestMemory: (overrides = {}) => ({ /* ... */ }),
  createTestCategory: (overrides = {}) => ({ /* ... */ }),
  
  // 等待异步操作
  waitFor: (callback, timeout = 1000) => { /* ... */ }
};
```

## 测试脚本

### 可用的npm脚本
```json
{
  "test": "jest",
  "test:watch": "jest --watch",
  "test:coverage": "jest --coverage",
  "test:ci": "jest --ci --coverage --watchAll=false"
}
```

### 脚本说明
- `npm test`: 运行所有测试
- `npm run test:watch`: 监视模式运行测试
- `npm run test:coverage`: 运行测试并生成覆盖率报告
- `npm run test:ci`: CI环境下运行测试

## 测试辅助工具

### renderWithProviders
用于测试React组件的辅助函数：

```javascript
import { renderWithProviders } from '@/test/helpers/renderWithProviders';

test('should render component with Redux store', () => {
  const { store } = renderWithProviders(<MyComponent />);
  // 测试逻辑
});
```

### 模拟服务创建
```javascript
import { createMockApplicationServices } from '@/test/helpers/renderWithProviders';

const mockServices = createMockApplicationServices();
// 使用模拟服务进行测试
```

### 测试数据工厂
```javascript
import { testDataFactory } from '@/test/helpers/renderWithProviders';

const testMemory = testDataFactory.memory({ title: 'Custom Title' });
const testCategory = testDataFactory.category({ name: 'Custom Category' });
```

## 覆盖率配置

### 覆盖率阈值
- 语句覆盖率: 80%
- 分支覆盖率: 80%
- 函数覆盖率: 80%
- 行覆盖率: 80%

### 覆盖率报告格式
- text: 控制台文本输出
- lcov: LCOV格式（用于CI集成）
- html: HTML报告（在coverage/目录）
- json-summary: JSON摘要

## CI/CD集成

### GitHub Actions配置
项目包含完整的GitHub Actions工作流：

```yaml
name: Test Suite
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
      - run: npm ci
      - run: npm run test:ci
      - uses: codecov/codecov-action@v3
```

## 测试最佳实践

### 1. 测试文件组织
```
src/
├── components/
│   ├── MyComponent.js
│   └── __tests__/
│       └── MyComponent.test.js
├── services/
│   ├── MyService.js
│   └── __tests__/
│       └── MyService.test.js
└── test/
    ├── setup.js
    ├── helpers/
    └── __mocks__/
```

### 2. 测试命名规范
```javascript
describe('ComponentName', () => {
  describe('when condition', () => {
    test('should do something', () => {
      // 测试逻辑
    });
  });
});
```

### 3. 异步测试
```javascript
test('should handle async operations', async () => {
  const result = await asyncFunction();
  expect(result).toBe('expected');
});
```

### 4. 模拟函数使用
```javascript
test('should call function with correct parameters', () => {
  const mockFn = jest.fn();
  component.onClick = mockFn;
  
  fireEvent.click(button);
  
  expect(mockFn).toHaveBeenCalledWith(expectedParams);
});
```

## 故障排除

### 常见问题

#### 1. Chrome API未定义
**解决方案**: 确保测试文件导入了setup.js，或者在测试中手动模拟Chrome API。

#### 2. 模块路径解析失败
**解决方案**: 检查jest.config.js中的moduleNameMapper配置是否正确。

#### 3. 静态资源导入失败
**解决方案**: 确保identity-obj-proxy和fileMock.js配置正确。

#### 4. 异步测试超时
**解决方案**: 增加测试超时时间或使用waitFor等待异步操作完成。

### 调试技巧

#### 1. 使用console.log调试
```javascript
test('debug test', () => {
  console.log('Debug info:', someVariable);
  // 测试逻辑
});
```

#### 2. 使用debugger断点
```javascript
test('debug with breakpoint', () => {
  debugger; // 在浏览器开发者工具中会暂停
  // 测试逻辑
});
```

#### 3. 查看DOM结构
```javascript
import { screen } from '@testing-library/react';

test('debug DOM', () => {
  render(<Component />);
  screen.debug(); // 打印当前DOM结构
});
```

## 下一步

测试框架已完全配置并验证。接下来可以：

1. 开始为现有组件编写测试
2. 在重构过程中使用TDD方法
3. 逐步提高测试覆盖率
4. 集成更多测试工具（如E2E测试）
