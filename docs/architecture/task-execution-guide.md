# 重构任务执行指南

## 任务执行原则

### 基本原则
1. **功能优先**：确保每个任务完成后现有功能完全正常
2. **测试驱动**：每个任务都要有相应的测试验证
3. **文档同步**：代码变更后及时更新相关文档
4. **渐进迁移**：新旧代码并存，逐步替换

### 任务执行流程
1. **理解任务**：仔细阅读任务描述和实施指导
2. **环境准备**：确保开发环境和依赖正确配置
3. **编写测试**：先编写测试用例（TDD方式）
4. **实现功能**：按照实施指导完成代码实现
5. **验证测试**：确保所有测试通过
6. **功能验证**：手动测试确保功能正常
7. **文档更新**：更新相关文档和注释
8. **代码审查**：进行代码审查和优化

## 阶段1任务详细指导

### 任务1：建立项目架构文档和开发规范

#### 实施步骤
1. **创建架构文档目录结构**
```bash
mkdir -p docs/architecture
mkdir -p docs/api
mkdir -p docs/migration
```

2. **创建分层架构设计文档**
```markdown
# docs/architecture/layered-architecture.md
- 表现层设计
- 应用层设计  
- 领域层设计
- 基础设施层设计
- 层间交互规范
```

3. **建立编码规范**
```markdown
# docs/architecture/coding-standards.md
- 命名规范
- 文件组织规范
- 注释规范
- 测试规范
```

4. **创建重构指南**
```markdown
# docs/architecture/refactoring-guide.md
- 重构原则
- 迁移策略
- 风险控制
- 回滚机制
```

#### 验收标准
- [ ] 所有架构文档创建完成
- [ ] 文档内容清晰完整
- [ ] 包含具体的实现示例
- [ ] 团队成员能够理解并遵循

### 任务2：建立测试框架和CI/CD基础

#### 实施步骤
1. **安装测试依赖**
```bash
npm install --save-dev jest @testing-library/react @testing-library/jest-dom @testing-library/user-event
```

2. **配置Jest**
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx}',
    '!src/test/**',
    '!src/**/*.test.{js,jsx}'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

3. **创建测试工具**
```javascript
// src/test/setup.js
import '@testing-library/jest-dom';

// 模拟Chrome API
global.chrome = {
  storage: {
    local: {
      get: jest.fn(),
      set: jest.fn()
    }
  },
  runtime: {
    sendMessage: jest.fn(),
    onMessage: {
      addListener: jest.fn()
    }
  }
};
```

4. **更新package.json脚本**
```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage"
  }
}
```

#### 验收标准
- [ ] 测试框架正确安装和配置
- [ ] 能够运行基本测试用例
- [ ] 测试覆盖率报告正常生成
- [ ] Chrome API模拟正确配置

### 任务3：创建Chrome API适配器层

#### 实施步骤
1. **创建适配器目录结构**
```bash
mkdir -p src/infrastructure/adapters
```

2. **实现ChromeStorageAdapter**
```javascript
// src/infrastructure/adapters/ChromeStorageAdapter.js
export class ChromeStorageAdapter {
  async get(keys) {
    return new Promise((resolve, reject) => {
      chrome.storage.local.get(keys, (result) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(result);
        }
      });
    });
  }

  async set(items) {
    return new Promise((resolve, reject) => {
      chrome.storage.local.set(items, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  async remove(keys) {
    return new Promise((resolve, reject) => {
      chrome.storage.local.remove(keys, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }
}
```

3. **实现ChromeRuntimeAdapter**
```javascript
// src/infrastructure/adapters/ChromeRuntimeAdapter.js
export class ChromeRuntimeAdapter {
  sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
  }

  onMessage(callback) {
    chrome.runtime.onMessage.addListener(callback);
  }

  getURL(path) {
    return chrome.runtime.getURL(path);
  }
}
```

4. **创建适配器接口**
```javascript
// src/infrastructure/adapters/IStorageAdapter.js
export class IStorageAdapter {
  async get(keys) {
    throw new Error('Must be implemented by subclass');
  }

  async set(items) {
    throw new Error('Must be implemented by subclass');
  }

  async remove(keys) {
    throw new Error('Must be implemented by subclass');
  }
}
```

5. **编写适配器测试**
```javascript
// src/infrastructure/adapters/__tests__/ChromeStorageAdapter.test.js
import { ChromeStorageAdapter } from '../ChromeStorageAdapter';

describe('ChromeStorageAdapter', () => {
  let adapter;

  beforeEach(() => {
    adapter = new ChromeStorageAdapter();
    // 重置Chrome API模拟
    jest.clearAllMocks();
  });

  test('should get data from chrome storage', async () => {
    const mockData = { key: 'value' };
    chrome.storage.local.get.mockImplementation((keys, callback) => {
      callback(mockData);
    });

    const result = await adapter.get(['key']);
    expect(result).toEqual(mockData);
    expect(chrome.storage.local.get).toHaveBeenCalledWith(['key'], expect.any(Function));
  });

  test('should set data to chrome storage', async () => {
    const mockData = { key: 'value' };
    chrome.storage.local.set.mockImplementation((items, callback) => {
      callback();
    });

    await adapter.set(mockData);
    expect(chrome.storage.local.set).toHaveBeenCalledWith(mockData, expect.any(Function));
  });
});
```

#### 验收标准
- [ ] 所有Chrome API都有对应的适配器
- [ ] 适配器接口清晰，易于测试
- [ ] 适配器单元测试覆盖率达到90%以上
- [ ] 适配器能够正确处理错误情况

## 任务执行检查清单

### 每个任务完成后检查
- [ ] 代码实现符合任务要求
- [ ] 所有测试用例通过
- [ ] 现有功能完全正常
- [ ] 代码质量符合规范
- [ ] 相关文档已更新
- [ ] 代码已提交到版本控制

### 每个阶段完成后检查
- [ ] 阶段目标完全达成
- [ ] 集成测试全部通过
- [ ] 性能没有明显下降
- [ ] 架构设计得到验证
- [ ] 为下一阶段做好准备

## 常见问题和解决方案

### 测试相关问题
**问题**：Chrome API在测试环境中不可用
**解决**：使用Jest模拟Chrome API，创建完整的模拟对象

**问题**：异步测试不稳定
**解决**：使用async/await和适当的等待机制

### 重构相关问题
**问题**：重构后功能异常
**解决**：立即回滚，检查实现，增加测试覆盖

**问题**：新旧代码冲突
**解决**：使用特性开关，逐步迁移

### 性能相关问题
**问题**：重构后性能下降
**解决**：进行性能分析，优化关键路径

## 下一步

完成当前文档创建后，开始执行第一个任务：建立项目架构文档和开发规范。
