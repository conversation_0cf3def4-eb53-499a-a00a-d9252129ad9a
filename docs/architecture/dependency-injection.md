# 依赖注入规范文档

## 概述

本文档定义了项目中依赖注入的设计原则、实现方式和使用规范，确保系统的松耦合和高可测试性。

## 设计原则

### 1. 依赖倒置原则
- 高层模块不依赖低层模块，两者都依赖抽象
- 抽象不依赖具体实现，具体实现依赖抽象

### 2. 单一职责原则
- 每个服务只负责一个特定的功能
- 避免创建过于复杂的服务

### 3. 接口隔离原则
- 定义细粒度的接口
- 避免强迫客户端依赖不需要的方法

## 依赖注入容器设计

### 容器接口定义
```typescript
interface IDIContainer {
  /**
   * 注册服务
   * @param token 服务标识
   * @param factory 服务工厂函数
   * @param options 注册选项
   */
  register<T>(token: string | symbol, factory: ServiceFactory<T>, options?: RegisterOptions): void;

  /**
   * 解析服务
   * @param token 服务标识
   * @returns 服务实例
   */
  resolve<T>(token: string | symbol): T;

  /**
   * 检查服务是否已注册
   * @param token 服务标识
   * @returns 是否已注册
   */
  isRegistered(token: string | symbol): boolean;

  /**
   * 创建子容器
   * @returns 子容器实例
   */
  createChild(): IDIContainer;
}

interface ServiceFactory<T> {
  (container: IDIContainer): T;
}

interface RegisterOptions {
  singleton?: boolean;
  scope?: 'transient' | 'singleton' | 'scoped';
  tags?: string[];
}
```

### 容器实现
```javascript
class DIContainer {
  constructor() {
    this.services = new Map();
    this.singletons = new Map();
    this.parent = null;
  }

  register(token, factory, options = {}) {
    const registration = {
      factory,
      options: {
        singleton: false,
        scope: 'transient',
        tags: [],
        ...options
      }
    };

    this.services.set(token, registration);
  }

  resolve(token) {
    const registration = this.getRegistration(token);
    
    if (!registration) {
      throw new Error(`Service ${String(token)} not registered`);
    }

    // 单例模式
    if (registration.options.singleton || registration.options.scope === 'singleton') {
      if (!this.singletons.has(token)) {
        const instance = registration.factory(this);
        this.singletons.set(token, instance);
      }
      return this.singletons.get(token);
    }

    // 瞬态模式
    return registration.factory(this);
  }

  getRegistration(token) {
    if (this.services.has(token)) {
      return this.services.get(token);
    }

    // 在父容器中查找
    if (this.parent) {
      return this.parent.getRegistration(token);
    }

    return null;
  }

  isRegistered(token) {
    return this.getRegistration(token) !== null;
  }

  createChild() {
    const child = new DIContainer();
    child.parent = this;
    return child;
  }
}
```

## 服务注册规范

### 服务标识符定义
```javascript
// 使用Symbol定义服务标识符
export const TYPES = {
  // 基础设施层
  IStorageAdapter: Symbol.for('IStorageAdapter'),
  ICloudStorageAdapter: Symbol.for('ICloudStorageAdapter'),
  IEncryptionService: Symbol.for('IEncryptionService'),
  ICacheService: Symbol.for('ICacheService'),

  // 领域层
  IMemoryRepository: Symbol.for('IMemoryRepository'),
  ICategoryRepository: Symbol.for('ICategoryRepository'),
  IUserRepository: Symbol.for('IUserRepository'),
  IMemoryDomainService: Symbol.for('IMemoryDomainService'),
  ISearchDomainService: Symbol.for('ISearchDomainService'),
  ISyncDomainService: Symbol.for('ISyncDomainService'),

  // 应用层
  IMemoryApplicationService: Symbol.for('IMemoryApplicationService'),
  ICategoryApplicationService: Symbol.for('ICategoryApplicationService'),
  ISyncApplicationService: Symbol.for('ISyncApplicationService'),
  IEventBus: Symbol.for('IEventBus'),

  // 配置
  AppConfig: Symbol.for('AppConfig'),
  StorageConfig: Symbol.for('StorageConfig')
};
```

### 服务注册配置
```javascript
// serviceRegistry.js
export function configureServices(container) {
  // 基础设施层服务
  container.register(
    TYPES.IStorageAdapter,
    () => new ChromeStorageAdapter(),
    { singleton: true }
  );

  container.register(
    TYPES.ICloudStorageAdapter,
    (c) => {
      const config = c.resolve(TYPES.StorageConfig);
      return createCloudStorageAdapter(config);
    },
    { singleton: true }
  );

  container.register(
    TYPES.IEncryptionService,
    () => new AESEncryptionService(),
    { singleton: true }
  );

  container.register(
    TYPES.ICacheService,
    (c) => new MemoryCacheService(c.resolve(TYPES.IStorageAdapter)),
    { singleton: true }
  );

  // 仓储层服务
  container.register(
    TYPES.IMemoryRepository,
    (c) => new LocalMemoryRepository(
      c.resolve(TYPES.IStorageAdapter),
      c.resolve(TYPES.IEncryptionService)
    ),
    { singleton: true }
  );

  container.register(
    TYPES.ICategoryRepository,
    (c) => new CategoryRepository(c.resolve(TYPES.IStorageAdapter)),
    { singleton: true }
  );

  // 领域服务
  container.register(
    TYPES.IMemoryDomainService,
    (c) => new MemoryDomainService(
      c.resolve(TYPES.IMemoryRepository),
      c.resolve(TYPES.ICategoryRepository)
    ),
    { singleton: true }
  );

  container.register(
    TYPES.ISearchDomainService,
    (c) => new SearchDomainService(
      c.resolve(TYPES.IMemoryRepository),
      c.resolve(TYPES.ICacheService)
    ),
    { singleton: true }
  );

  // 应用服务
  container.register(
    TYPES.IMemoryApplicationService,
    (c) => new MemoryApplicationService(
      c.resolve(TYPES.IMemoryDomainService),
      c.resolve(TYPES.IMemoryRepository),
      c.resolve(TYPES.IEventBus)
    ),
    { singleton: true }
  );

  // 配置对象
  container.register(
    TYPES.AppConfig,
    () => loadAppConfig(),
    { singleton: true }
  );
}

function createCloudStorageAdapter(config) {
  switch (config.provider) {
    case 'huawei':
      return new HuaweiObsAdapter(config);
    case 'minio':
      return new MinioAdapter(config);
    default:
      throw new Error(`Unsupported cloud storage provider: ${config.provider}`);
  }
}
```

## 装饰器支持

### 装饰器定义
```javascript
// decorators.js
export function injectable(target) {
  target.__injectable = true;
  return target;
}

export function inject(token) {
  return function(target, propertyKey, parameterIndex) {
    const existingTokens = Reflect.getMetadata('inject:tokens', target) || [];
    existingTokens[parameterIndex] = token;
    Reflect.defineMetadata('inject:tokens', existingTokens, target);
  };
}

export function singleton(target) {
  target.__singleton = true;
  return target;
}
```

### 装饰器使用示例
```javascript
@injectable
@singleton
export class MemoryApplicationService {
  constructor(
    @inject(TYPES.IMemoryDomainService) memoryDomainService,
    @inject(TYPES.IMemoryRepository) memoryRepository,
    @inject(TYPES.IEventBus) eventBus
  ) {
    this.memoryDomainService = memoryDomainService;
    this.memoryRepository = memoryRepository;
    this.eventBus = eventBus;
  }

  async createMemory(memoryData) {
    const memory = await this.memoryDomainService.createMemory(memoryData);
    await this.memoryRepository.save(memory);
    this.eventBus.publish(new MemoryCreatedEvent(memory));
    return memory;
  }
}
```

## 生命周期管理

### 生命周期类型
```javascript
const LifecycleType = {
  TRANSIENT: 'transient',    // 每次解析都创建新实例
  SINGLETON: 'singleton',    // 全局单例
  SCOPED: 'scoped'          // 作用域内单例
};
```

### 作用域管理
```javascript
class ScopedContainer extends DIContainer {
  constructor(parent) {
    super();
    this.parent = parent;
    this.scopedInstances = new Map();
  }

  resolve(token) {
    const registration = this.getRegistration(token);
    
    if (!registration) {
      throw new Error(`Service ${String(token)} not registered`);
    }

    // 作用域单例
    if (registration.options.scope === 'scoped') {
      if (!this.scopedInstances.has(token)) {
        const instance = registration.factory(this);
        this.scopedInstances.set(token, instance);
      }
      return this.scopedInstances.get(token);
    }

    return super.resolve(token);
  }

  dispose() {
    // 清理作用域内的实例
    for (const instance of this.scopedInstances.values()) {
      if (typeof instance.dispose === 'function') {
        instance.dispose();
      }
    }
    this.scopedInstances.clear();
  }
}
```

## 配置管理集成

### 配置驱动的服务注册
```javascript
// config/services.json
{
  "services": {
    "storage": {
      "provider": "chrome",
      "encryption": true
    },
    "cloudStorage": {
      "provider": "huawei",
      "config": {
        "endpoint": "https://obs.cn-north-4.myhuaweicloud.com",
        "bucket": "memory-keeper"
      }
    },
    "cache": {
      "provider": "memory",
      "maxSize": 100,
      "ttl": 3600
    }
  }
}

// 配置驱动的注册
function configureServicesFromConfig(container, config) {
  // 存储服务配置
  if (config.services.storage.provider === 'chrome') {
    container.register(
      TYPES.IStorageAdapter,
      () => new ChromeStorageAdapter(),
      { singleton: true }
    );
  }

  // 云存储服务配置
  const cloudConfig = config.services.cloudStorage;
  container.register(
    TYPES.ICloudStorageAdapter,
    () => createCloudStorageAdapter(cloudConfig),
    { singleton: true }
  );

  // 缓存服务配置
  const cacheConfig = config.services.cache;
  container.register(
    TYPES.ICacheService,
    (c) => new MemoryCacheService({
      maxSize: cacheConfig.maxSize,
      ttl: cacheConfig.ttl,
      storage: c.resolve(TYPES.IStorageAdapter)
    }),
    { singleton: true }
  );
}
```

## 测试支持

### 测试容器配置
```javascript
// testContainer.js
export function createTestContainer() {
  const container = new DIContainer();

  // 注册模拟服务
  container.register(
    TYPES.IStorageAdapter,
    () => new MockStorageAdapter(),
    { singleton: true }
  );

  container.register(
    TYPES.IMemoryRepository,
    (c) => new InMemoryMemoryRepository(),
    { singleton: true }
  );

  container.register(
    TYPES.IEventBus,
    () => new MockEventBus(),
    { singleton: true }
  );

  return container;
}

// 测试中使用
describe('MemoryApplicationService', () => {
  let container;
  let memoryService;

  beforeEach(() => {
    container = createTestContainer();
    memoryService = container.resolve(TYPES.IMemoryApplicationService);
  });

  test('should create memory', async () => {
    const memoryData = { title: 'Test', content: 'Content' };
    const memory = await memoryService.createMemory(memoryData);
    
    expect(memory.title).toBe('Test');
  });
});
```

### 模拟服务工厂
```javascript
export class MockServiceFactory {
  static createMockMemoryRepository() {
    return {
      save: jest.fn(),
      findById: jest.fn(),
      findAll: jest.fn(),
      delete: jest.fn()
    };
  }

  static createMockEventBus() {
    return {
      publish: jest.fn(),
      subscribe: jest.fn(),
      unsubscribe: jest.fn()
    };
  }

  static createMockStorageAdapter() {
    const storage = new Map();
    return {
      get: jest.fn((keys) => {
        const result = {};
        keys.forEach(key => {
          if (storage.has(key)) {
            result[key] = storage.get(key);
          }
        });
        return Promise.resolve(result);
      }),
      set: jest.fn((items) => {
        Object.entries(items).forEach(([key, value]) => {
          storage.set(key, value);
        });
        return Promise.resolve();
      }),
      remove: jest.fn((keys) => {
        keys.forEach(key => storage.delete(key));
        return Promise.resolve();
      })
    };
  }
}
```

## 最佳实践

### 1. 服务设计原则
- 保持服务接口简单明确
- 避免循环依赖
- 使用工厂模式处理复杂创建逻辑
- 合理使用单例模式

### 2. 依赖管理
- 优先依赖抽象而非具体实现
- 使用构造函数注入
- 避免服务定位器模式
- 明确服务的生命周期

### 3. 测试友好
- 所有依赖都通过构造函数注入
- 提供模拟实现用于测试
- 避免在构造函数中执行复杂逻辑
- 支持依赖替换

### 4. 性能考虑
- 合理使用单例模式减少对象创建
- 延迟初始化非关键服务
- 避免过深的依赖链
- 监控容器解析性能

## 常见问题和解决方案

### 问题1：循环依赖
**解决方案**：
- 重新设计服务边界
- 使用事件解耦
- 引入中介者模式
- 延迟注入

### 问题2：服务创建失败
**解决方案**：
- 检查依赖注册顺序
- 验证服务工厂函数
- 添加详细的错误信息
- 使用容器诊断工具

### 问题3：内存泄漏
**解决方案**：
- 正确管理服务生命周期
- 实现dispose模式
- 避免不必要的单例
- 定期清理作用域容器
