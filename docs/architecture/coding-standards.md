# 编码规范文档

## 概述

本文档定义了浏览器插件项目的编码规范，确保代码的一致性、可读性和可维护性。所有开发者都应严格遵循这些规范。

## 通用规范

### 文件命名规范
- **组件文件**: 使用PascalCase，如 `MemoryCard.js`
- **服务文件**: 使用PascalCase + Service后缀，如 `MemoryService.js`
- **工具文件**: 使用camelCase，如 `dateUtils.js`
- **常量文件**: 使用UPPER_SNAKE_CASE，如 `API_CONSTANTS.js`
- **测试文件**: 原文件名 + `.test.js`，如 `MemoryCard.test.js`

### 目录结构规范
```
src/
├── presentation/        # 表现层
│   ├── components/     # UI组件
│   ├── containers/     # 容器组件
│   ├── hooks/          # 自定义Hooks
│   └── pages/          # 页面组件
├── application/        # 应用层
│   ├── services/       # 应用服务
│   ├── store/          # 状态管理
│   └── events/         # 事件处理
├── domain/             # 领域层
│   ├── entities/       # 实体
│   ├── services/       # 领域服务
│   └── repositories/   # 仓储接口
└── infrastructure/     # 基础设施层
    ├── repositories/   # 仓储实现
    ├── adapters/       # 适配器
    └── services/       # 技术服务
```

## JavaScript/React 编码规范

### 变量命名
```javascript
// ✅ 正确示例
const memoryList = [];
const isLoading = false;
const MAX_RETRY_COUNT = 3;

// ❌ 错误示例
const ml = [];
const loading = false;
const maxRetryCount = 3; // 常量应使用大写
```

### 函数命名
```javascript
// ✅ 正确示例
function createMemory(data) { }
function validateMemoryData(data) { }
function handleMemoryClick(id) { }

// ❌ 错误示例
function create(data) { } // 不够具体
function validate(data) { } // 不够具体
function click(id) { } // 不够具体
```

### 类命名
```javascript
// ✅ 正确示例
class MemoryDomainService { }
class ChromeStorageAdapter { }
class ValidationError extends Error { }

// ❌ 错误示例
class memoryService { } // 应使用PascalCase
class Storage { } // 不够具体
class Error1 extends Error { } // 无意义命名
```

### React组件规范

#### 函数组件结构
```javascript
// ✅ 推荐的组件结构
import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { Button, Card } from 'antd';
import './MemoryCard.css';

/**
 * 记忆卡片组件
 * @param {Object} props - 组件属性
 * @param {Object} props.memory - 记忆对象
 * @param {Function} props.onEdit - 编辑回调
 * @param {Function} props.onDelete - 删除回调
 */
const MemoryCard = ({ memory, onEdit, onDelete }) => {
  // 1. Hooks声明
  const [isExpanded, setIsExpanded] = useState(false);

  // 2. 事件处理函数
  const handleToggleExpand = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  const handleEdit = useCallback(() => {
    onEdit(memory.id);
  }, [memory.id, onEdit]);

  // 3. 副作用
  useEffect(() => {
    // 组件挂载后的逻辑
  }, []);

  // 4. 渲染逻辑
  return (
    <Card
      title={memory.title}
      extra={
        <Button type="link" onClick={handleToggleExpand}>
          {isExpanded ? '收起' : '展开'}
        </Button>
      }
      actions={[
        <Button key="edit" onClick={handleEdit}>编辑</Button>,
        <Button key="delete" danger onClick={() => onDelete(memory.id)}>
          删除
        </Button>
      ]}
    >
      <p className={isExpanded ? 'expanded' : 'collapsed'}>
        {memory.content}
      </p>
    </Card>
  );
};

// 5. PropTypes定义
MemoryCard.propTypes = {
  memory: PropTypes.shape({
    id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    content: PropTypes.string.isRequired
  }).isRequired,
  onEdit: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired
};

export default MemoryCard;
```

#### 自定义Hook规范
```javascript
// ✅ 自定义Hook示例
import { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

/**
 * 记忆管理Hook
 * @returns {Object} 记忆相关状态和操作函数
 */
export const useMemories = () => {
  const dispatch = useDispatch();
  const memories = useSelector(state => state.memories.items);
  const loading = useSelector(state => state.memories.loading);

  const createMemory = useCallback((memoryData) => {
    dispatch(createMemoryAction(memoryData));
  }, [dispatch]);

  const updateMemory = useCallback((id, updateData) => {
    dispatch(updateMemoryAction({ id, updateData }));
  }, [dispatch]);

  return {
    memories,
    loading,
    createMemory,
    updateMemory
  };
};
```

### 服务类规范

#### 应用服务结构
```javascript
// ✅ 应用服务示例
import { injectable, inject } from 'inversify';
import { IMemoryRepository } from '../domain/repositories/IMemoryRepository';
import { MemoryDomainService } from '../domain/services/MemoryDomainService';

@injectable()
export class MemoryApplicationService {
  constructor(
    @inject('IMemoryRepository') private memoryRepository: IMemoryRepository,
    @inject('MemoryDomainService') private memoryDomainService: MemoryDomainService
  ) {}

  /**
   * 创建记忆
   * @param {Object} memoryData - 记忆数据
   * @returns {Promise<Memory>} 创建的记忆对象
   */
  async createMemory(memoryData) {
    try {
      // 1. 验证输入
      this.validateCreateMemoryRequest(memoryData);

      // 2. 调用领域服务
      const memory = await this.memoryDomainService.createMemory(memoryData);

      // 3. 持久化
      await this.memoryRepository.save(memory);

      // 4. 返回结果
      return memory;
    } catch (error) {
      this.logger.error('创建记忆失败', error);
      throw new ApplicationError('创建记忆失败', error);
    }
  }

  /**
   * 验证创建记忆请求
   * @private
   */
  validateCreateMemoryRequest(data) {
    if (!data.title || data.title.trim().length === 0) {
      throw new ValidationError('记忆标题不能为空');
    }
    if (!data.content || data.content.trim().length === 0) {
      throw new ValidationError('记忆内容不能为空');
    }
  }
}
```

## 注释规范

### JSDoc注释
```javascript
/**
 * 计算两个日期之间的天数差
 * @param {Date} startDate - 开始日期
 * @param {Date} endDate - 结束日期
 * @returns {number} 天数差
 * @throws {Error} 当日期无效时抛出错误
 * @example
 * const days = calculateDaysDiff(new Date('2023-01-01'), new Date('2023-01-10'));
 * console.log(days); // 9
 */
function calculateDaysDiff(startDate, endDate) {
  if (!(startDate instanceof Date) || !(endDate instanceof Date)) {
    throw new Error('参数必须是有效的Date对象');
  }
  
  const timeDiff = endDate.getTime() - startDate.getTime();
  return Math.ceil(timeDiff / (1000 * 3600 * 24));
}
```

### 行内注释
```javascript
// ✅ 好的注释 - 解释为什么这样做
// 使用防抖避免频繁的搜索请求
const debouncedSearch = debounce(handleSearch, 300);

// 需要深拷贝避免修改原始数据
const updatedMemory = JSON.parse(JSON.stringify(memory));

// ❌ 不好的注释 - 重复代码逻辑
// 设置loading为true
setLoading(true);

// 调用API获取数据
const data = await api.getData();
```

## 错误处理规范

### 错误类型定义
```javascript
// ✅ 自定义错误类
export class ApplicationError extends Error {
  constructor(message, cause) {
    super(message);
    this.name = 'ApplicationError';
    this.cause = cause;
    this.timestamp = new Date().toISOString();
  }
}

export class ValidationError extends ApplicationError {
  constructor(message, field) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
  }
}

export class BusinessRuleError extends ApplicationError {
  constructor(message, rule) {
    super(message);
    this.name = 'BusinessRuleError';
    this.rule = rule;
  }
}
```

### 错误处理模式
```javascript
// ✅ 推荐的错误处理
async function saveMemory(memoryData) {
  try {
    // 验证数据
    validateMemoryData(memoryData);
    
    // 保存数据
    const result = await memoryRepository.save(memoryData);
    
    return { success: true, data: result };
  } catch (error) {
    // 记录错误
    logger.error('保存记忆失败', { error, memoryData });
    
    // 根据错误类型处理
    if (error instanceof ValidationError) {
      return { success: false, error: '数据验证失败', details: error.message };
    } else if (error instanceof NetworkError) {
      return { success: false, error: '网络连接失败', retryable: true };
    } else {
      return { success: false, error: '未知错误', details: error.message };
    }
  }
}
```

## 测试规范

### 测试文件结构
```javascript
// MemoryCard.test.js
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryCard } from '../MemoryCard';

describe('MemoryCard', () => {
  // 测试数据
  const mockMemory = {
    id: '1',
    title: 'Test Memory',
    content: 'Test content'
  };

  const mockProps = {
    memory: mockMemory,
    onEdit: jest.fn(),
    onDelete: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('渲染测试', () => {
    test('应该正确渲染记忆信息', () => {
      render(<MemoryCard {...mockProps} />);
      
      expect(screen.getByText('Test Memory')).toBeInTheDocument();
      expect(screen.getByText('Test content')).toBeInTheDocument();
    });
  });

  describe('交互测试', () => {
    test('点击编辑按钮应该调用onEdit', () => {
      render(<MemoryCard {...mockProps} />);
      
      fireEvent.click(screen.getByText('编辑'));
      
      expect(mockProps.onEdit).toHaveBeenCalledWith('1');
    });
  });
});
```

### 测试命名规范
```javascript
// ✅ 好的测试描述
describe('MemoryApplicationService', () => {
  describe('createMemory', () => {
    test('应该成功创建记忆并返回记忆对象', async () => {
      // 测试逻辑
    });

    test('当标题为空时应该抛出ValidationError', async () => {
      // 测试逻辑
    });

    test('当存储失败时应该抛出ApplicationError', async () => {
      // 测试逻辑
    });
  });
});

// ❌ 不好的测试描述
describe('MemoryService', () => {
  test('test1', () => {
    // 测试逻辑
  });

  test('should work', () => {
    // 测试逻辑
  });
});
```

## 性能规范

### 避免不必要的重渲染
```javascript
// ✅ 使用React.memo优化
const MemoryCard = React.memo(({ memory, onEdit, onDelete }) => {
  // 组件逻辑
});

// ✅ 使用useCallback缓存函数
const handleEdit = useCallback(() => {
  onEdit(memory.id);
}, [memory.id, onEdit]);

// ✅ 使用useMemo缓存计算结果
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(data);
}, [data]);
```

### 异步操作规范
```javascript
// ✅ 正确的异步处理
async function loadMemories() {
  try {
    setLoading(true);
    const memories = await memoryService.getMemories();
    setMemories(memories);
  } catch (error) {
    setError(error.message);
  } finally {
    setLoading(false);
  }
}

// ✅ 使用AbortController取消请求
useEffect(() => {
  const controller = new AbortController();
  
  async function fetchData() {
    try {
      const data = await api.getData({ signal: controller.signal });
      setData(data);
    } catch (error) {
      if (error.name !== 'AbortError') {
        setError(error);
      }
    }
  }
  
  fetchData();
  
  return () => controller.abort();
}, []);
```

## 代码审查清单

### 提交前检查
- [ ] 代码符合命名规范
- [ ] 函数和类有适当的注释
- [ ] 错误处理完整
- [ ] 测试用例覆盖主要逻辑
- [ ] 没有console.log等调试代码
- [ ] 没有未使用的导入和变量
- [ ] 代码格式化正确

### 审查重点
- [ ] 业务逻辑正确性
- [ ] 性能考虑
- [ ] 安全性检查
- [ ] 可维护性评估
- [ ] 测试覆盖率
- [ ] 文档完整性

## 工具配置

### ESLint配置
```json
{
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended"
  ],
  "rules": {
    "no-console": "warn",
    "no-unused-vars": "error",
    "prefer-const": "error",
    "react/prop-types": "error"
  }
}
```

### Prettier配置
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```
