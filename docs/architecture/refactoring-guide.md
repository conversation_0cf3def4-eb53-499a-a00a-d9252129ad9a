# 重构指南文档

## 重构原则

### 核心原则
1. **功能优先**：确保重构过程中功能不受影响
2. **渐进式重构**：分阶段、小步骤进行重构
3. **测试驱动**：先写测试，再进行重构
4. **向后兼容**：保持API的向后兼容性
5. **文档同步**：及时更新相关文档

### 重构目标
- 实现清晰的分层架构
- 提高代码的可测试性
- 降低模块间的耦合度
- 提升代码的可维护性
- 增强系统的可扩展性

## 重构策略

### 1. 接口优先策略
在重构具体实现之前，先定义清晰的接口和抽象。

```javascript
// 步骤1：定义接口
interface IMemoryRepository {
  save(memory: Memory): Promise<Memory>;
  findById(id: string): Promise<Memory | null>;
  findAll(): Promise<Memory[]>;
  delete(id: string): Promise<void>;
}

// 步骤2：创建适配器包装现有实现
class StorageServiceAdapter implements IMemoryRepository {
  constructor(private storageService: StorageService) {}
  
  async save(memory: Memory): Promise<Memory> {
    // 包装现有的StorageService方法
    return this.storageService.saveMemory(memory);
  }
}

// 步骤3：逐步替换具体实现
class LocalMemoryRepository implements IMemoryRepository {
  // 新的实现
}
```

### 2. 适配器模式迁移
使用适配器模式包装现有服务，保持接口兼容。

```javascript
// 现有服务
class OldStorageService {
  saveMemory(data) { /* 现有实现 */ }
  getMemories() { /* 现有实现 */ }
}

// 适配器包装
class StorageServiceAdapter {
  constructor(oldService) {
    this.oldService = oldService;
  }
  
  async save(memory) {
    // 转换数据格式
    const data = this.convertToOldFormat(memory);
    return this.oldService.saveMemory(data);
  }
  
  async findAll() {
    const data = await this.oldService.getMemories();
    // 转换为新格式
    return this.convertToNewFormat(data);
  }
}
```

### 3. 特性开关控制
使用特性开关控制新旧实现的切换。

```javascript
// 配置管理
class FeatureFlags {
  static USE_NEW_ARCHITECTURE = process.env.USE_NEW_ARCHITECTURE === 'true';
  static USE_NEW_STORAGE = process.env.USE_NEW_STORAGE === 'true';
}

// 服务工厂
class ServiceFactory {
  static createMemoryService() {
    if (FeatureFlags.USE_NEW_ARCHITECTURE) {
      return new NewMemoryApplicationService();
    } else {
      return new LegacyMemoryServiceAdapter();
    }
  }
}
```

## 分阶段重构计划

### 阶段1：基础设施层重构
**目标**：建立可测试的基础设施，解耦外部依赖

#### 重构步骤
1. **创建适配器层**
   ```javascript
   // 封装Chrome API
   class ChromeStorageAdapter {
     async get(keys) {
       return new Promise((resolve, reject) => {
         chrome.storage.local.get(keys, (result) => {
           if (chrome.runtime.lastError) {
             reject(chrome.runtime.lastError);
           } else {
             resolve(result);
           }
         });
       });
     }
   }
   ```

2. **重构存储服务**
   ```javascript
   // 原有实现
   class StorageService {
     getSettings() {
       return new Promise((resolve) => {
         chrome.storage.local.get(['settings'], (result) => {
           resolve(result.settings);
         });
       });
     }
   }
   
   // 重构后实现
   class StorageService {
     constructor(storageAdapter) {
       this.storageAdapter = storageAdapter;
     }
     
     async getSettings() {
       const result = await this.storageAdapter.get(['settings']);
       return result.settings;
     }
   }
   ```

3. **建立依赖注入**
   ```javascript
   // 依赖注入容器
   class DIContainer {
     constructor() {
       this.services = new Map();
     }
     
     register(name, factory) {
       this.services.set(name, factory);
     }
     
     resolve(name) {
       const factory = this.services.get(name);
       return factory();
     }
   }
   
   // 服务注册
   container.register('storageAdapter', () => new ChromeStorageAdapter());
   container.register('storageService', () => 
     new StorageService(container.resolve('storageAdapter'))
   );
   ```

### 阶段2：领域层建立
**目标**：提取核心业务逻辑，建立领域模型

#### 重构步骤
1. **提取领域实体**
   ```javascript
   // 从数据结构提取实体
   class Memory {
     constructor(data) {
       this.id = data.id;
       this.title = this.validateTitle(data.title);
       this.content = data.content;
       this.category = Category.create(data.category);
       this.tags = data.tags.map(tag => Tag.create(tag));
       this.createdAt = new Date(data.createdAt);
     }
     
     validateTitle(title) {
       if (!title || title.trim().length === 0) {
         throw new DomainError('标题不能为空');
       }
       return title.trim();
     }
     
     updateContent(newContent) {
       this.content = newContent;
       this.lastModified = new Date();
       return this;
     }
   }
   ```

2. **创建领域服务**
   ```javascript
   class MemoryDomainService {
     constructor(memoryRepository) {
       this.memoryRepository = memoryRepository;
     }
     
     async createMemory(memoryData) {
       // 业务规则验证
       this.validateBusinessRules(memoryData);
       
       // 创建实体
       const memory = new Memory(memoryData);
       
       // 应用业务逻辑
       this.applyBusinessLogic(memory);
       
       return memory;
     }
   }
   ```

### 阶段3：应用层重构
**目标**：建立状态管理，协调各层交互

#### 重构步骤
1. **引入Redux状态管理**
   ```javascript
   // 创建slice
   const memoriesSlice = createSlice({
     name: 'memories',
     initialState: {
       items: [],
       loading: false,
       error: null
     },
     reducers: {
       setMemories: (state, action) => {
         state.items = action.payload;
       },
       addMemory: (state, action) => {
         state.items.push(action.payload);
       }
     }
   });
   ```

2. **创建应用服务**
   ```javascript
   class MemoryApplicationService {
     constructor(memoryDomainService, memoryRepository) {
       this.memoryDomainService = memoryDomainService;
       this.memoryRepository = memoryRepository;
     }
     
     async createMemory(memoryData) {
       // 协调领域服务和基础设施
       const memory = await this.memoryDomainService.createMemory(memoryData);
       await this.memoryRepository.save(memory);
       
       // 更新应用状态
       store.dispatch(addMemory(memory));
       
       return memory;
     }
   }
   ```

### 阶段4：表现层重构
**目标**：分离UI组件和业务逻辑

#### 重构步骤
1. **提取纯UI组件**
   ```javascript
   // 原有组件（包含业务逻辑）
   const BrowseMemories = () => {
     const [memories, setMemories] = useState([]);
     
     useEffect(() => {
       // 直接调用服务
       storageService.getMemories().then(setMemories);
     }, []);
     
     const handleDelete = (id) => {
       // 直接调用服务
       storageService.deleteMemory(id);
     };
     
     return <MemoryList memories={memories} onDelete={handleDelete} />;
   };
   
   // 重构后：纯UI组件
   const MemoryList = ({ memories, onDelete, loading }) => {
     if (loading) return <Spin />;
     
     return (
       <List
         dataSource={memories}
         renderItem={memory => (
           <MemoryCard memory={memory} onDelete={onDelete} />
         )}
       />
     );
   };
   
   // 容器组件
   const MemoryListContainer = () => {
     const dispatch = useDispatch();
     const { memories, loading } = useSelector(state => state.memories);
     
     const handleDelete = useCallback((id) => {
       dispatch(deleteMemory(id));
     }, [dispatch]);
     
     return (
       <MemoryList
         memories={memories}
         loading={loading}
         onDelete={handleDelete}
       />
     );
   };
   ```

## 迁移策略

### 1. 并行运行策略
新旧系统并行运行，逐步切换流量。

```javascript
class HybridMemoryService {
  constructor(oldService, newService) {
    this.oldService = oldService;
    this.newService = newService;
  }
  
  async getMemories() {
    if (FeatureFlags.USE_NEW_SERVICE) {
      try {
        return await this.newService.getMemories();
      } catch (error) {
        // 降级到旧服务
        console.warn('新服务失败，降级到旧服务', error);
        return await this.oldService.getMemories();
      }
    } else {
      return await this.oldService.getMemories();
    }
  }
}
```

### 2. 数据迁移策略
```javascript
class DataMigrationService {
  async migrateToNewFormat() {
    // 1. 备份现有数据
    const backup = await this.createBackup();
    
    try {
      // 2. 读取旧格式数据
      const oldData = await this.oldStorageService.getAllData();
      
      // 3. 转换为新格式
      const newData = this.convertToNewFormat(oldData);
      
      // 4. 验证转换结果
      this.validateConversion(oldData, newData);
      
      // 5. 保存新格式数据
      await this.newStorageService.saveAll(newData);
      
      // 6. 验证迁移结果
      await this.validateMigration();
      
    } catch (error) {
      // 7. 失败时恢复备份
      await this.restoreBackup(backup);
      throw error;
    }
  }
}
```

## 风险控制

### 1. 回滚机制
```javascript
class RollbackManager {
  constructor() {
    this.checkpoints = [];
  }
  
  async createCheckpoint(name) {
    const checkpoint = {
      name,
      timestamp: new Date(),
      data: await this.exportCurrentState()
    };
    this.checkpoints.push(checkpoint);
  }
  
  async rollback(checkpointName) {
    const checkpoint = this.checkpoints.find(cp => cp.name === checkpointName);
    if (!checkpoint) {
      throw new Error(`检查点 ${checkpointName} 不存在`);
    }
    
    await this.restoreState(checkpoint.data);
  }
}
```

### 2. 监控和告警
```javascript
class MigrationMonitor {
  constructor() {
    this.metrics = {
      errorCount: 0,
      successCount: 0,
      performanceMetrics: []
    };
  }
  
  recordSuccess(operation, duration) {
    this.metrics.successCount++;
    this.metrics.performanceMetrics.push({ operation, duration });
  }
  
  recordError(operation, error) {
    this.metrics.errorCount++;
    console.error(`操作失败: ${operation}`, error);
    
    // 错误率过高时触发告警
    if (this.getErrorRate() > 0.1) {
      this.triggerAlert('错误率过高，建议暂停迁移');
    }
  }
  
  getErrorRate() {
    const total = this.metrics.errorCount + this.metrics.successCount;
    return total > 0 ? this.metrics.errorCount / total : 0;
  }
}
```

### 3. 渐进式发布
```javascript
class GradualRollout {
  constructor() {
    this.rolloutPercentage = 0;
  }
  
  shouldUseNewFeature(userId) {
    // 基于用户ID的哈希值决定是否使用新功能
    const hash = this.hashUserId(userId);
    return (hash % 100) < this.rolloutPercentage;
  }
  
  increaseRollout(percentage) {
    this.rolloutPercentage = Math.min(100, this.rolloutPercentage + percentage);
    console.log(`新功能发布比例增加到 ${this.rolloutPercentage}%`);
  }
}
```

## 测试策略

### 1. 重构前测试
```javascript
// 为现有功能编写特征测试
describe('现有功能特征测试', () => {
  test('应该能够创建和检索记忆', async () => {
    const memoryData = { title: '测试', content: '内容' };
    
    // 使用现有API
    const createdMemory = await legacyService.createMemory(memoryData);
    const retrievedMemory = await legacyService.getMemory(createdMemory.id);
    
    expect(retrievedMemory.title).toBe(memoryData.title);
    expect(retrievedMemory.content).toBe(memoryData.content);
  });
});
```

### 2. 重构过程测试
```javascript
// 确保新旧实现行为一致
describe('新旧实现一致性测试', () => {
  test('新旧服务应该返回相同结果', async () => {
    const testData = generateTestData();
    
    const oldResult = await oldService.processData(testData);
    const newResult = await newService.processData(testData);
    
    expect(newResult).toEqual(oldResult);
  });
});
```

## 最佳实践

### 1. 小步骤重构
- 每次只重构一个小功能
- 每个步骤都要有测试验证
- 频繁提交代码，便于回滚

### 2. 保持向后兼容
- 不要破坏现有API
- 使用适配器模式过渡
- 逐步废弃旧接口

### 3. 文档同步更新
- 及时更新架构文档
- 记录重构决策和原因
- 维护迁移指南

### 4. 团队协作
- 定期代码审查
- 分享重构经验
- 统一重构标准

## 常见问题和解决方案

### 问题1：重构后性能下降
**解决方案**：
- 进行性能基准测试
- 识别性能瓶颈
- 优化关键路径
- 考虑回滚到稳定版本

### 问题2：新旧代码冲突
**解决方案**：
- 使用命名空间隔离
- 通过配置控制切换
- 建立清晰的边界

### 问题3：测试覆盖不足
**解决方案**：
- 补充缺失的测试用例
- 提高测试覆盖率要求
- 建立测试门禁机制
