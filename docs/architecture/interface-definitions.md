# 接口定义文档

## 概述

本文档定义了分层架构中各层之间的接口规范，确保层间交互的一致性和可维护性。

## 领域层接口

### 仓储接口

#### IMemoryRepository
```typescript
interface IMemoryRepository {
  /**
   * 保存记忆
   * @param memory 记忆实体
   * @returns 保存后的记忆实体
   */
  save(memory: Memory): Promise<Memory>;

  /**
   * 根据ID查找记忆
   * @param id 记忆ID
   * @returns 记忆实体或null
   */
  findById(id: MemoryId): Promise<Memory | null>;

  /**
   * 根据分类查找记忆
   * @param category 分类
   * @returns 记忆列表
   */
  findByCategory(category: Category): Promise<Memory[]>;

  /**
   * 根据标签查找记忆
   * @param tags 标签列表
   * @returns 记忆列表
   */
  findByTags(tags: Tag[]): Promise<Memory[]>;

  /**
   * 根据规约查找记忆
   * @param specification 查询规约
   * @returns 记忆列表
   */
  findBySpecification(specification: Specification<Memory>): Promise<Memory[]>;

  /**
   * 获取所有记忆
   * @returns 记忆列表
   */
  findAll(): Promise<Memory[]>;

  /**
   * 删除记忆
   * @param id 记忆ID
   */
  delete(id: MemoryId): Promise<void>;

  /**
   * 批量保存记忆
   * @param memories 记忆列表
   * @returns 保存后的记忆列表
   */
  saveAll(memories: Memory[]): Promise<Memory[]>;

  /**
   * 统计记忆数量
   * @param specification 查询规约（可选）
   * @returns 记忆数量
   */
  count(specification?: Specification<Memory>): Promise<number>;
}
```

#### ICategoryRepository
```typescript
interface ICategoryRepository {
  /**
   * 保存分类
   * @param category 分类实体
   * @returns 保存后的分类实体
   */
  save(category: Category): Promise<Category>;

  /**
   * 根据ID查找分类
   * @param id 分类ID
   * @returns 分类实体或null
   */
  findById(id: CategoryId): Promise<Category | null>;

  /**
   * 根据名称查找分类
   * @param name 分类名称
   * @returns 分类实体或null
   */
  findByName(name: string): Promise<Category | null>;

  /**
   * 获取所有分类
   * @returns 分类列表
   */
  findAll(): Promise<Category[]>;

  /**
   * 删除分类
   * @param id 分类ID
   */
  delete(id: CategoryId): Promise<void>;
}
```

#### IUserRepository
```typescript
interface IUserRepository {
  /**
   * 保存用户
   * @param user 用户实体
   * @returns 保存后的用户实体
   */
  save(user: User): Promise<User>;

  /**
   * 根据ID查找用户
   * @param id 用户ID
   * @returns 用户实体或null
   */
  findById(id: UserId): Promise<User | null>;

  /**
   * 获取当前用户
   * @returns 当前用户实体
   */
  getCurrentUser(): Promise<User>;

  /**
   * 更新用户设置
   * @param userId 用户ID
   * @param settings 用户设置
   */
  updateSettings(userId: UserId, settings: UserSettings): Promise<void>;
}
```

### 领域服务接口

#### IMemoryDomainService
```typescript
interface IMemoryDomainService {
  /**
   * 创建记忆
   * @param memoryData 记忆数据
   * @returns 创建的记忆实体
   */
  createMemory(memoryData: CreateMemoryData): Promise<Memory>;

  /**
   * 更新记忆
   * @param memory 记忆实体
   * @param updateData 更新数据
   * @returns 更新后的记忆实体
   */
  updateMemory(memory: Memory, updateData: UpdateMemoryData): Promise<Memory>;

  /**
   * 验证记忆数据
   * @param memoryData 记忆数据
   * @throws ValidationError 验证失败时抛出
   */
  validateMemoryData(memoryData: any): void;

  /**
   * 应用业务规则
   * @param memory 记忆实体
   * @param context 上下文信息
   */
  applyBusinessRules(memory: Memory, context: BusinessContext): void;
}
```

#### ISearchDomainService
```typescript
interface ISearchDomainService {
  /**
   * 搜索记忆
   * @param query 搜索查询
   * @param filters 过滤条件
   * @returns 搜索结果
   */
  searchMemories(query: SearchQuery, filters: SearchFilters): Promise<SearchResult<Memory>>;

  /**
   * 构建搜索规约
   * @param query 搜索查询
   * @param filters 过滤条件
   * @returns 搜索规约
   */
  buildSearchSpecification(query: SearchQuery, filters: SearchFilters): Specification<Memory>;

  /**
   * 应用搜索排序
   * @param memories 记忆列表
   * @param query 搜索查询
   * @returns 排序后的记忆列表
   */
  applySearchRanking(memories: Memory[], query: SearchQuery): Memory[];
}
```

#### ISyncDomainService
```typescript
interface ISyncDomainService {
  /**
   * 同步记忆
   * @param localMemories 本地记忆列表
   * @param remoteMemories 远程记忆列表
   * @returns 同步结果
   */
  syncMemories(localMemories: Memory[], remoteMemories: Memory[]): Promise<SyncResult>;

  /**
   * 检测冲突
   * @param localMemories 本地记忆列表
   * @param remoteMemories 远程记忆列表
   * @returns 冲突列表
   */
  detectConflicts(localMemories: Memory[], remoteMemories: Memory[]): Conflict[];

  /**
   * 解决冲突
   * @param conflicts 冲突列表
   * @param strategy 解决策略
   * @returns 解决结果
   */
  resolveConflicts(conflicts: Conflict[], strategy: ConflictResolutionStrategy): ConflictResolution[];

  /**
   * 合并记忆
   * @param localMemories 本地记忆列表
   * @param remoteMemories 远程记忆列表
   * @returns 合并后的记忆列表
   */
  mergeMemories(localMemories: Memory[], remoteMemories: Memory[]): Memory[];
}
```

## 应用层接口

### 应用服务接口

#### IMemoryApplicationService
```typescript
interface IMemoryApplicationService {
  /**
   * 创建记忆
   * @param request 创建记忆请求
   * @returns 创建的记忆
   */
  createMemory(request: CreateMemoryRequest): Promise<MemoryResponse>;

  /**
   * 更新记忆
   * @param id 记忆ID
   * @param request 更新记忆请求
   * @returns 更新后的记忆
   */
  updateMemory(id: string, request: UpdateMemoryRequest): Promise<MemoryResponse>;

  /**
   * 删除记忆
   * @param id 记忆ID
   */
  deleteMemory(id: string): Promise<void>;

  /**
   * 获取记忆
   * @param id 记忆ID
   * @returns 记忆详情
   */
  getMemory(id: string): Promise<MemoryResponse>;

  /**
   * 获取记忆列表
   * @param request 查询请求
   * @returns 记忆列表
   */
  getMemories(request: GetMemoriesRequest): Promise<MemoryListResponse>;

  /**
   * 搜索记忆
   * @param request 搜索请求
   * @returns 搜索结果
   */
  searchMemories(request: SearchMemoriesRequest): Promise<SearchMemoriesResponse>;
}
```

#### ISyncApplicationService
```typescript
interface ISyncApplicationService {
  /**
   * 执行同步
   * @returns 同步结果
   */
  performSync(): Promise<SyncResultResponse>;

  /**
   * 获取同步状态
   * @returns 同步状态
   */
  getSyncStatus(): Promise<SyncStatusResponse>;

  /**
   * 配置同步设置
   * @param settings 同步设置
   */
  configureSyncSettings(settings: SyncSettings): Promise<void>;

  /**
   * 解决同步冲突
   * @param conflictId 冲突ID
   * @param resolution 解决方案
   */
  resolveConflict(conflictId: string, resolution: ConflictResolution): Promise<void>;
}
```

### 状态管理接口

#### Redux State Shape
```typescript
interface RootState {
  memories: MemoriesState;
  categories: CategoriesState;
  settings: SettingsState;
  sync: SyncState;
  ui: UIState;
}

interface MemoriesState {
  items: Memory[];
  currentMemory: Memory | null;
  loading: boolean;
  error: string | null;
  filters: MemoryFilters;
  pagination: Pagination;
}

interface CategoriesState {
  items: Category[];
  loading: boolean;
  error: string | null;
}

interface SettingsState {
  user: UserSettings | null;
  cloudStorage: CloudStorageSettings;
  appearance: AppearanceSettings;
  sync: SyncSettings;
}

interface SyncState {
  status: SyncStatus;
  lastSyncTime: Date | null;
  conflicts: Conflict[];
  progress: SyncProgress | null;
}
```

## 基础设施层接口

### 适配器接口

#### IStorageAdapter
```typescript
interface IStorageAdapter {
  /**
   * 获取数据
   * @param keys 键列表
   * @returns 数据对象
   */
  get(keys: string[]): Promise<Record<string, any>>;

  /**
   * 设置数据
   * @param items 数据对象
   */
  set(items: Record<string, any>): Promise<void>;

  /**
   * 删除数据
   * @param keys 键列表
   */
  remove(keys: string[]): Promise<void>;

  /**
   * 清空所有数据
   */
  clear(): Promise<void>;

  /**
   * 获取存储使用情况
   * @returns 存储使用信息
   */
  getUsage(): Promise<StorageUsage>;
}
```

#### ICloudStorageAdapter
```typescript
interface ICloudStorageAdapter {
  /**
   * 初始化连接
   * @param config 配置信息
   */
  initialize(config: CloudStorageConfig): Promise<void>;

  /**
   * 上传对象
   * @param key 对象键
   * @param data 数据
   * @param metadata 元数据
   */
  putObject(key: string, data: any, metadata?: ObjectMetadata): Promise<void>;

  /**
   * 下载对象
   * @param key 对象键
   * @returns 对象数据
   */
  getObject(key: string): Promise<any>;

  /**
   * 删除对象
   * @param key 对象键
   */
  deleteObject(key: string): Promise<void>;

  /**
   * 列出对象
   * @param prefix 前缀
   * @returns 对象列表
   */
  listObjects(prefix?: string): Promise<ObjectInfo[]>;

  /**
   * 检查连接状态
   * @returns 连接是否正常
   */
  checkConnection(): Promise<boolean>;
}
```

### 技术服务接口

#### IEncryptionService
```typescript
interface IEncryptionService {
  /**
   * 加密数据
   * @param data 原始数据
   * @param key 加密密钥
   * @returns 加密后的数据
   */
  encrypt(data: string, key: string): Promise<string>;

  /**
   * 解密数据
   * @param encryptedData 加密的数据
   * @param key 解密密钥
   * @returns 原始数据
   */
  decrypt(encryptedData: string, key: string): Promise<string>;

  /**
   * 生成密钥
   * @returns 新的密钥
   */
  generateKey(): Promise<string>;

  /**
   * 验证密钥
   * @param key 密钥
   * @returns 密钥是否有效
   */
  validateKey(key: string): boolean;
}
```

#### ICacheService
```typescript
interface ICacheService {
  /**
   * 获取缓存
   * @param key 缓存键
   * @returns 缓存值
   */
  get<T>(key: string): Promise<T | null>;

  /**
   * 设置缓存
   * @param key 缓存键
   * @param value 缓存值
   * @param ttl 过期时间（秒）
   */
  set<T>(key: string, value: T, ttl?: number): Promise<void>;

  /**
   * 删除缓存
   * @param key 缓存键
   */
  delete(key: string): Promise<void>;

  /**
   * 清空缓存
   */
  clear(): Promise<void>;

  /**
   * 检查缓存是否存在
   * @param key 缓存键
   * @returns 是否存在
   */
  has(key: string): Promise<boolean>;
}
```

## 数据传输对象 (DTO)

### 请求对象

#### CreateMemoryRequest
```typescript
interface CreateMemoryRequest {
  title: string;
  content: string;
  categoryId?: string;
  tags?: string[];
  metadata?: Record<string, any>;
}
```

#### UpdateMemoryRequest
```typescript
interface UpdateMemoryRequest {
  title?: string;
  content?: string;
  categoryId?: string;
  tags?: string[];
  metadata?: Record<string, any>;
}
```

#### SearchMemoriesRequest
```typescript
interface SearchMemoriesRequest {
  query: string;
  filters?: {
    categoryId?: string;
    tags?: string[];
    dateRange?: {
      start: Date;
      end: Date;
    };
  };
  pagination?: {
    page: number;
    pageSize: number;
  };
  sorting?: {
    field: string;
    direction: 'asc' | 'desc';
  };
}
```

### 响应对象

#### MemoryResponse
```typescript
interface MemoryResponse {
  id: string;
  title: string;
  content: string;
  category: CategoryResponse;
  tags: TagResponse[];
  createdAt: Date;
  lastModified: Date;
  metadata: Record<string, any>;
}
```

#### MemoryListResponse
```typescript
interface MemoryListResponse {
  items: MemoryResponse[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}
```

#### SearchMemoriesResponse
```typescript
interface SearchMemoriesResponse {
  items: MemoryResponse[];
  total: number;
  query: string;
  executionTime: number;
  suggestions?: string[];
}
```

## 事件接口

### 领域事件

#### DomainEvent
```typescript
interface DomainEvent {
  id: string;
  aggregateId: string;
  eventType: string;
  eventData: any;
  occurredOn: Date;
  version: number;
}
```

#### MemoryCreatedEvent
```typescript
interface MemoryCreatedEvent extends DomainEvent {
  eventType: 'MemoryCreated';
  eventData: {
    memoryId: string;
    title: string;
    categoryId: string;
    userId: string;
  };
}
```

### 应用事件

#### ApplicationEvent
```typescript
interface ApplicationEvent {
  id: string;
  type: string;
  payload: any;
  timestamp: Date;
  source: string;
}
```

## 错误接口

### 错误类型

#### ApplicationError
```typescript
interface ApplicationError extends Error {
  name: 'ApplicationError';
  code: string;
  details?: any;
  cause?: Error;
  timestamp: Date;
}
```

#### ValidationError
```typescript
interface ValidationError extends ApplicationError {
  name: 'ValidationError';
  field: string;
  value: any;
  constraints: string[];
}
```

#### BusinessRuleError
```typescript
interface BusinessRuleError extends ApplicationError {
  name: 'BusinessRuleError';
  rule: string;
  context: any;
}
```
