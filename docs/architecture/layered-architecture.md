# 分层架构设计文档

## 架构概览

本文档详细描述了浏览器插件的分层架构设计，采用领域驱动设计（DDD）和清洁架构原则，实现高内聚、低耦合的系统结构。

## 架构原则

### 核心原则
1. **依赖倒置原则**：高层模块不依赖低层模块，两者都依赖抽象
2. **单一职责原则**：每个层次只负责自己的关注点
3. **接口隔离原则**：定义清晰的层间接口
4. **开闭原则**：对扩展开放，对修改封闭

### 设计目标
- 实现清晰的层次分离
- 确保每层独立可测试
- 支持业务逻辑的复用
- 便于功能扩展和维护

## 四层架构设计

### 1. 表现层 (Presentation Layer)
**位置**: `src/presentation/`
**职责**: 用户界面展示和用户交互处理

#### 组件结构
```
src/presentation/
├── components/          # 纯UI组件
│   ├── common/         # 通用组件 (Button, Input, Modal等)
│   ├── business/       # 业务组件 (MemoryCard, CategoryTag等)
│   └── layout/         # 布局组件 (Header, Sidebar等)
├── containers/         # 容器组件 (连接Redux状态)
│   ├── MemoryContainer.js
│   ├── SettingsContainer.js
│   └── SyncContainer.js
├── hooks/              # 自定义React Hooks
│   ├── useMemories.js
│   ├── useSettings.js
│   └── useSync.js
├── pages/              # 页面组件
│   ├── BrowseMemoriesPage.js
│   ├── AddMemoryPage.js
│   └── SettingsPage.js
└── utils/              # UI工具函数
    ├── formatters.js
    └── validators.js
```

#### 设计规范
- **纯UI组件**: 只负责渲染，不包含业务逻辑
- **容器组件**: 连接Redux状态，处理用户交互
- **Props接口**: 明确定义组件的输入输出
- **事件处理**: 通过回调函数向上传递事件

#### 示例代码
```javascript
// 纯UI组件示例
const MemoryCard = ({ memory, onEdit, onDelete }) => (
  <Card
    title={memory.title}
    extra={<CategoryTag category={memory.category} />}
    actions={[
      <Button onClick={() => onEdit(memory.id)}>编辑</Button>,
      <Button danger onClick={() => onDelete(memory.id)}>删除</Button>
    ]}
  >
    <p>{memory.content}</p>
    <TagList tags={memory.tags} />
  </Card>
);

// 容器组件示例
const MemoryListContainer = () => {
  const dispatch = useDispatch();
  const memories = useSelector(selectMemories);
  
  return (
    <MemoryList
      memories={memories}
      onEdit={(id) => dispatch(editMemory(id))}
      onDelete={(id) => dispatch(deleteMemory(id))}
    />
  );
};
```

### 2. 应用层 (Application Layer)
**位置**: `src/application/`
**职责**: 协调业务流程，管理应用状态

#### 组件结构
```
src/application/
├── services/           # 应用服务
│   ├── MemoryApplicationService.js
│   ├── CategoryApplicationService.js
│   └── SyncApplicationService.js
├── store/              # Redux状态管理
│   ├── index.js        # Store配置
│   ├── slices/         # Redux Slices
│   │   ├── memoriesSlice.js
│   │   ├── categoriesSlice.js
│   │   └── settingsSlice.js
│   └── selectors/      # 选择器函数
│       ├── memorySelectors.js
│       └── settingsSelectors.js
├── events/             # 应用事件
│   ├── EventBus.js
│   └── ApplicationEvents.js
└── middleware/         # Redux中间件
    ├── loggingMiddleware.js
    └── errorMiddleware.js
```

#### 设计规范
- **应用服务**: 协调领域服务，处理用例流程
- **状态管理**: 使用Redux管理全局状态
- **事件驱动**: 通过事件实现松耦合通信
- **中间件**: 处理横切关注点

#### 示例代码
```javascript
// 应用服务示例
class MemoryApplicationService {
  constructor(memoryDomainService, memoryRepository) {
    this.memoryDomainService = memoryDomainService;
    this.memoryRepository = memoryRepository;
  }

  async createMemory(memoryData) {
    // 验证输入
    this.validateMemoryData(memoryData);
    
    // 调用领域服务
    const memory = await this.memoryDomainService.createMemory(memoryData);
    
    // 保存到仓储
    await this.memoryRepository.save(memory);
    
    // 发布事件
    EventBus.publish(new MemoryCreatedEvent(memory));
    
    return memory;
  }
}
```

### 3. 领域层 (Domain Layer)
**位置**: `src/domain/`
**职责**: 核心业务逻辑和业务规则

#### 组件结构
```
src/domain/
├── entities/           # 领域实体
│   ├── Memory.js
│   ├── Category.js
│   ├── Tag.js
│   └── User.js
├── services/           # 领域服务
│   ├── MemoryDomainService.js
│   ├── SearchDomainService.js
│   └── SyncDomainService.js
├── repositories/       # 仓储接口
│   ├── IMemoryRepository.js
│   ├── ICategoryRepository.js
│   └── IUserRepository.js
├── events/             # 领域事件
│   ├── DomainEvent.js
│   ├── MemoryEvents.js
│   └── EventPublisher.js
├── specifications/     # 规约模式
│   ├── MemorySpecification.js
│   └── SearchSpecification.js
└── value-objects/      # 值对象
    ├── MemoryId.js
    ├── CategoryId.js
    └── DateRange.js
```

#### 设计规范
- **实体**: 具有唯一标识的业务对象
- **值对象**: 不可变的值类型
- **领域服务**: 不属于特定实体的业务逻辑
- **仓储接口**: 定义数据访问抽象
- **领域事件**: 表示重要的业务事件

#### 示例代码
```javascript
// 领域实体示例
class Memory {
  constructor(id, title, content, category, tags, createdAt) {
    this.id = MemoryId.create(id);
    this.title = this.validateTitle(title);
    this.content = content;
    this.category = Category.create(category);
    this.tags = tags.map(tag => Tag.create(tag));
    this.createdAt = createdAt || new Date();
    this.lastModified = new Date();
  }

  updateContent(newContent) {
    if (!newContent || newContent.trim().length === 0) {
      throw new DomainError('内容不能为空');
    }
    this.content = newContent;
    this.lastModified = new Date();
    return this;
  }

  addTag(tag) {
    const newTag = Tag.create(tag);
    if (!this.hasTag(newTag)) {
      this.tags.push(newTag);
    }
    return this;
  }
}
```

### 4. 基础设施层 (Infrastructure Layer)
**位置**: `src/infrastructure/`
**职责**: 技术实现和外部系统集成

#### 组件结构
```
src/infrastructure/
├── repositories/       # 仓储实现
│   ├── LocalMemoryRepository.js
│   ├── CloudMemoryRepository.js
│   └── CategoryRepository.js
├── adapters/           # 适配器
│   ├── ChromeStorageAdapter.js
│   ├── ChromeRuntimeAdapter.js
│   └── CloudStorageAdapter.js
├── services/           # 技术服务
│   ├── EncryptionService.js
│   ├── SearchIndexService.js
│   └── CacheService.js
├── config/             # 配置管理
│   ├── AppConfig.js
│   └── StorageConfig.js
├── di/                 # 依赖注入
│   ├── DIContainer.js
│   └── ServiceRegistry.js
└── external/           # 外部服务集成
    ├── HuaweiObsClient.js
    └── MinioClient.js
```

#### 设计规范
- **仓储实现**: 实现领域层定义的仓储接口
- **适配器**: 封装外部API和浏览器API
- **技术服务**: 提供加密、缓存等技术功能
- **配置管理**: 统一管理应用配置
- **依赖注入**: 管理服务依赖关系

## 层间交互规范

### 依赖方向
```
表现层 → 应用层 → 领域层 ← 基础设施层
```

### 交互原则
1. **表现层**只能调用应用层服务
2. **应用层**协调领域层服务，不包含业务逻辑
3. **领域层**是核心，不依赖其他层
4. **基础设施层**实现领域层定义的接口

### 数据流向
1. **用户输入** → 表现层 → 应用层 → 领域层
2. **业务处理** → 领域层 → 基础设施层（数据持久化）
3. **结果返回** → 基础设施层 → 应用层 → 表现层

## 接口设计规范

### 服务接口
```javascript
// 应用服务接口示例
interface IMemoryApplicationService {
  createMemory(memoryData: CreateMemoryRequest): Promise<Memory>;
  updateMemory(id: string, updateData: UpdateMemoryRequest): Promise<Memory>;
  deleteMemory(id: string): Promise<void>;
  getMemory(id: string): Promise<Memory>;
  searchMemories(query: SearchQuery): Promise<Memory[]>;
}
```

### 仓储接口
```javascript
// 仓储接口示例
interface IMemoryRepository {
  save(memory: Memory): Promise<Memory>;
  findById(id: MemoryId): Promise<Memory | null>;
  findByCategory(category: Category): Promise<Memory[]>;
  findBySpecification(spec: Specification): Promise<Memory[]>;
  delete(id: MemoryId): Promise<void>;
}
```

## 错误处理策略

### 错误类型层次
```
ApplicationError
├── ValidationError
├── BusinessRuleError
├── InfrastructureError
│   ├── StorageError
│   ├── NetworkError
│   └── ConfigurationError
└── DomainError
```

### 错误处理原则
1. **领域层**抛出DomainError
2. **应用层**处理并转换为ApplicationError
3. **基础设施层**抛出InfrastructureError
4. **表现层**展示用户友好的错误信息

## 测试策略

### 测试金字塔
```
E2E Tests (少量)
    ↑
Integration Tests (适量)
    ↑
Unit Tests (大量)
```

### 各层测试重点
- **表现层**: 组件渲染、用户交互
- **应用层**: 业务流程、状态管理
- **领域层**: 业务逻辑、业务规则
- **基础设施层**: 数据访问、外部集成

## 性能考虑

### 优化策略
1. **懒加载**: 按需加载组件和模块
2. **缓存**: 合理使用内存和持久化缓存
3. **批处理**: 批量处理数据操作
4. **异步处理**: 使用异步操作避免阻塞

### 监控指标
- 页面加载时间
- 内存使用情况
- API响应时间
- 用户操作响应时间

## 扩展性设计

### 插件化架构
- 支持新的存储提供商
- 支持新的同步策略
- 支持新的搜索算法
- 支持新的UI主题

### 配置化设计
- 功能开关配置
- 性能参数配置
- UI布局配置
- 业务规则配置
