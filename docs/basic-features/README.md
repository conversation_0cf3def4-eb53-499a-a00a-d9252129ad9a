# 基本功能

本节介绍拾光忆栈的基本功能，帮助您了解如何保存、浏览、搜索和组织您的记忆。

## 内容概览

- [保存记忆](./saving-memories.md) - 如何从网页保存文本、图片和视频
- [浏览记忆](./browsing-memories.md) - 如何查看和管理已保存的记忆
- [搜索与过滤](./search-and-filter.md) - 如何使用搜索和过滤功能找到特定记忆
- [分类与标签](./categories-and-tags.md) - 如何使用分类和标签组织记忆

## 功能概述

拾光忆栈的基本功能设计围绕着记忆的完整生命周期：从捕获、组织到检索和利用。

### 捕获记忆

拾光忆栈提供多种方式捕获网页内容：

- 选中文本后使用右键菜单保存
- 使用键盘快捷键（Ctrl+Shift+M）快速保存
- 通过扩展弹出窗口手动添加记忆
- 保存网页中的图片和视频

### 组织记忆

有效组织记忆是提高检索效率的关键：

- 使用分类系统对记忆进行分组
- 添加标签为记忆创建灵活的关联
- 编辑记忆标题和内容，使其更有意义
- 为重要记忆添加收藏标记

### 检索记忆

拾光忆栈提供强大的检索功能：

- 全文搜索记忆内容
- 按分类、标签、日期等过滤记忆
- 使用高级搜索语法组合多个搜索条件
- 按不同条件排序搜索结果

### 利用记忆

保存记忆的最终目的是在需要时能够利用它们：

- 查看完整的记忆内容，包括原始格式
- 复制记忆内容到剪贴板
- 查看记忆的原始来源
- 分享记忆（如果启用）

## 开始使用

如果您是拾光忆栈的新用户，建议按照以下顺序了解基本功能：

1. 首先学习[保存记忆](./saving-memories.md)，开始积累您的知识库
2. 然后了解[浏览记忆](./browsing-memories.md)的不同视图和操作
3. 掌握[搜索与过滤](./search-and-filter.md)技巧，提高检索效率
4. 最后学习如何使用[分类与标签](./categories-and-tags.md)系统组织记忆

掌握这些基本功能后，您可以进一步探索[高级功能](../advanced-features/README.md)，如多设备同步、数据加密等。
