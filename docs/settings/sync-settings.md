# 同步设置

同步设置控制拾光忆栈如何在云存储和多个设备之间同步您的数据。本指南将详细介绍如何配置和管理这些设置。

## 云存储配置

### 选择存储提供商

拾光忆栈支持多种云存储提供商：

1. 打开设置页面
2. 点击左侧菜单的"同步设置"
3. 在"云存储配置"部分，选择存储提供商：
   - **华为云OBS**：华为云对象存储服务
   - **MinIO**：开源对象存储服务（计划支持）
   - **腾讯云COS**：腾讯云对象存储服务（计划支持）
   - **Amazon S3**：亚马逊简单存储服务（计划支持）
   - **Google Drive**：谷歌云端硬盘（计划支持）

![选择存储提供商](../images/select-storage-provider.png)

### 配置存储凭证

根据选择的存储提供商，配置相应的凭证：

#### 华为云OBS

1. 填写以下信息：
   - **Access Key ID**：您的华为云访问密钥ID
   - **Secret Access Key**：您的华为云访问密钥密码
   - **区域**：您创建存储桶的区域（如cn-north-4）
   - **Endpoint**：OBS服务端点（如obs.cn-north-4.myhuaweicloud.com）
   - **Bucket名称**：您创建的存储桶名称

#### MinIO（计划支持）

1. 填写以下信息：
   - **服务器URL**：您的MinIO服务器地址
   - **Access Key**：您的MinIO访问密钥
   - **Secret Key**：您的MinIO密钥密码
   - **Bucket名称**：您创建的存储桶名称
   - **使用SSL**：是否使用SSL连接

#### 其他提供商

其他提供商的配置类似，需要填写相应的访问凭证和存储位置信息。

### 测试连接

配置完成后，测试连接以确保设置正确：

1. 点击"测试连接"按钮
2. 系统会尝试连接到云存储服务
3. 如果连接成功，会显示成功消息
4. 如果连接失败，会显示错误信息，请检查您的配置

![测试连接](../images/test-connection.png)

### 高级存储设置

配置高级存储选项：

1. 点击"高级设置"展开更多选项
2. 配置以下选项：
   - **存储路径前缀**：在云存储中使用的路径前缀
   - **超时设置**：连接和操作超时时间
   - **重试策略**：失败操作的重试次数和间隔
   - **带宽限制**：上传和下载的带宽限制
   - **代理设置**：如果需要通过代理访问云存储

## 同步配置

### 自动同步设置

配置自动同步行为：

1. 在同步设置页面找到"自动同步"部分
2. 配置以下选项：
   - **启用自动同步**：是否启用自动同步
   - **同步频率**：选择同步频率（实时、每小时、每天等）
   - **仅在Wi-Fi下同步**：是否仅在Wi-Fi连接时同步
   - **后台同步**：是否允许在后台同步
   - **启动时同步**：是否在扩展启动时自动同步

![自动同步设置](../images/auto-sync-settings.png)

### 同步范围

选择要同步的数据类型：

1. 在同步设置页面找到"同步范围"部分
2. 配置以下选项：
   - **记忆数据**：是否同步记忆内容
   - **媒体文件**：是否同步图片和视频
   - **用户设置**：是否同步用户设置
   - **分类和标签**：是否同步分类和标签结构
   - **搜索索引**：是否同步搜索索引

### 选择性同步

配置更精细的同步控制：

1. 在同步设置页面找到"选择性同步"部分
2. 配置以下选项：
   - **按分类同步**：选择要同步的特定分类
   - **按时间范围同步**：选择要同步的时间范围
   - **按标签同步**：选择要同步的特定标签
   - **媒体文件大小限制**：设置同步的媒体文件大小上限

![选择性同步](../images/selective-sync.png)

## 冲突解决

### 冲突解决策略

配置当多个设备修改相同数据时的冲突解决策略：

1. 在同步设置页面找到"冲突解决"部分
2. 选择默认策略：
   - **最新优先**：使用最后修改的版本
   - **本地优先**：优先使用本地版本
   - **云端优先**：优先使用云端版本
   - **总是询问**：每次冲突都手动解决
3. 配置高级选项：
   - **合并不冲突的字段**：是否尝试合并不冲突的字段
   - **保留历史版本**：是否保留冲突的历史版本
   - **冲突通知**：是否在发生冲突时显示通知

![冲突解决策略](../images/conflict-resolution-strategy.png)

### 手动解决冲突

配置手动解决冲突的选项：

1. 在冲突解决部分找到"手动解决冲突"选项
2. 配置以下选项：
   - **显示差异**：如何显示版本之间的差异
   - **合并视图**：是否使用并排合并视图
   - **保存合并历史**：是否保存手动合并的历史记录

## 设备管理

### 查看已同步设备

管理已同步的设备：

1. 在同步设置页面找到"设备管理"部分
2. 查看所有已同步的设备列表
3. 每个设备显示以下信息：
   - 设备名称
   - 设备类型（桌面/移动）
   - 最后活跃时间
   - 操作系统和浏览器信息

![设备管理](../images/device-management-sync.png)

### 管理设备

对已同步设备执行操作：

1. 在设备列表中找到要管理的设备
2. 可以执行以下操作：
   - **重命名**：为设备指定友好名称
   - **查看详情**：查看设备的详细信息
   - **移除**：将设备从同步列表中移除
   - **设为主设备**：将设备设为主设备（冲突时优先）

### 当前设备设置

配置当前设备的同步行为：

1. 在设备管理部分找到"当前设备"选项
2. 配置以下选项：
   - **设备名称**：设置当前设备的名称
   - **同步优先级**：设置当前设备的同步优先级
   - **离线模式**：配置离线模式下的行为

## 同步状态和历史

### 查看同步状态

监控当前的同步状态：

1. 在同步设置页面找到"同步状态"部分
2. 查看以下信息：
   - 上次同步时间
   - 同步状态（成功/失败）
   - 待同步的更改数量
   - 同步进度（如果正在进行）

![同步状态](../images/sync-status-page.png)

### 同步历史

查看过去的同步操作：

1. 在同步设置页面找到"同步历史"部分
2. 查看同步操作列表，每条记录包含：
   - 同步时间
   - 同步类型（自动/手动）
   - 同步结果
   - 同步的数据量
   - 可能的错误信息
3. 点击记录可以查看详细信息

### 同步日志

查看详细的同步日志：

1. 在同步设置页面找到"同步日志"部分
2. 点击"查看日志"按钮
3. 在日志查看器中，您可以：
   - 按日期筛选日志
   - 按级别筛选日志（信息、警告、错误）
   - 搜索特定内容
   - 导出日志文件

![同步日志](../images/sync-logs.png)

## 带宽和性能

### 带宽限制

控制同步使用的网络带宽：

1. 在同步设置页面找到"带宽设置"部分
2. 配置以下选项：
   - **上传速度限制**：限制上传速度（KB/s）
   - **下载速度限制**：限制下载速度（KB/s）
   - **移动网络限制**：在移动网络下的带宽限制
   - **计费网络限制**：在计费网络下的带宽限制

![带宽限制](../images/bandwidth-limits.png)

### 性能优化

优化同步性能：

1. 在同步设置页面找到"性能优化"部分
2. 配置以下选项：
   - **并发连接数**：同时建立的连接数
   - **分块上传大小**：大文件分块上传的块大小
   - **压缩传输**：是否压缩数据再传输
   - **增量同步**：是否使用增量同步算法
   - **智能同步**：是否使用智能同步策略

## 备份设置

### 自动备份

配置自动备份：

1. 在同步设置页面找到"备份设置"部分
2. 配置以下选项：
   - **启用自动备份**：是否启用自动备份
   - **备份频率**：备份的频率（每天、每周、每月）
   - **备份时间**：执行备份的时间
   - **备份位置**：备份文件的存储位置
   - **保留备份数量**：保留的备份版本数量

![自动备份设置](../images/auto-backup-settings-sync.png)

### 手动备份

执行手动备份：

1. 在备份设置部分找到"手动备份"选项
2. 点击"创建备份"按钮
3. 输入备份描述（可选）
4. 选择备份范围
5. 点击"开始备份"按钮

### 恢复备份

从备份恢复数据：

1. 在备份设置部分找到"恢复备份"选项
2. 点击"查看备份"按钮
3. 从备份列表中选择要恢复的备份
4. 点击"恢复"按钮
5. 选择恢复选项
6. 确认恢复操作

## 高级同步设置

### 版本控制

配置数据版本控制：

1. 在同步设置页面找到"高级设置"部分
2. 展开"版本控制"选项
3. 配置以下选项：
   - **启用版本控制**：是否保留数据的历史版本
   - **版本数量限制**：保留的最大版本数量
   - **版本保留时间**：保留版本的时间长度
   - **版本存储位置**：版本数据的存储位置

![版本控制](../images/version-control.png)

### 同步调度

配置详细的同步调度：

1. 在高级设置部分展开"同步调度"选项
2. 配置以下选项：
   - **自定义调度**：设置特定的同步时间表
   - **空闲时同步**：仅在系统空闲时同步
   - **网络条件**：基于网络条件的同步规则
   - **电源条件**：基于电源状态的同步规则

### 数据压缩

配置数据压缩选项：

1. 在高级设置部分展开"数据压缩"选项
2. 配置以下选项：
   - **启用压缩**：是否压缩数据再传输
   - **压缩级别**：压缩的强度（1-9）
   - **压缩算法**：使用的压缩算法
   - **选择性压缩**：哪些类型的数据需要压缩

## 故障排除

### 同步诊断

诊断同步问题：

1. 在同步设置页面找到"故障排除"部分
2. 点击"运行诊断"按钮
3. 系统会检查以下方面：
   - 云存储连接
   - 凭证有效性
   - 权限设置
   - 网络连接
   - 本地存储状态
4. 查看诊断结果和建议的解决方案

![同步诊断](../images/sync-diagnostics.png)

### 修复同步

修复同步问题：

1. 在故障排除部分找到"修复同步"选项
2. 选择修复操作：
   - **重置同步状态**：清除同步状态并重新开始
   - **重建索引**：重建同步索引
   - **强制同步**：忽略错误强制执行同步
   - **验证数据完整性**：检查数据完整性并修复问题
3. 点击"开始修复"按钮

### 同步日志分析

分析同步日志以解决问题：

1. 在故障排除部分找到"日志分析"选项
2. 点击"分析日志"按钮
3. 系统会分析同步日志并提供：
   - 常见错误模式
   - 性能瓶颈
   - 建议的优化措施
   - 可能的解决方案

## 下一步

现在您已经了解了如何配置同步设置，接下来可以学习：

- [缓存设置](./cache-settings.md) - 如何管理本地缓存和存储
- [外观设置](./appearance.md) - 如何自定义界面外观
- [多设备同步](../advanced-features/multi-device-sync.md) - 如何在多个设备上同步记忆
