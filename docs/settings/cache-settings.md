# 缓存设置

缓存设置控制拾光忆栈如何在本地存储和管理数据，影响应用的性能、离线可用性和存储空间使用。本指南将详细介绍如何配置和管理这些设置。

## 缓存概述

拾光忆栈使用多级缓存系统来优化性能和提供离线访问能力：

- **内存缓存**：存储最近访问的数据，提供最快的访问速度
- **IndexedDB缓存**：在浏览器的IndexedDB中存储更多数据
- **本地存储缓存**：在Chrome的本地存储中保存关键数据

![缓存系统](../images/cache-system.png)

## 基本缓存设置

### 缓存配置

配置基本缓存行为：

1. 打开设置页面
2. 点击左侧菜单的"缓存设置"
3. 在"基本设置"部分，配置以下选项：
   - **启用缓存**：是否启用本地缓存
   - **缓存模式**：选择缓存模式（标准、增强、最小）
   - **离线模式**：是否启用离线访问模式
   - **自动清理**：是否自动清理过期缓存

![基本缓存设置](../images/basic-cache-settings.png)

### 缓存大小

控制缓存使用的存储空间：

1. 在缓存设置页面找到"缓存大小"部分
2. 配置以下选项：
   - **最大缓存大小**：设置总缓存大小上限（MB）
   - **内存缓存限制**：设置内存缓存大小上限（MB）
   - **IndexedDB缓存限制**：设置IndexedDB缓存大小上限（MB）
   - **媒体缓存限制**：设置媒体文件缓存大小上限（MB）

## 高级缓存设置

### 缓存策略

配置详细的缓存策略：

1. 在缓存设置页面找到"高级设置"部分
2. 展开"缓存策略"选项
3. 配置以下选项：
   - **缓存优先级**：设置不同类型数据的缓存优先级
   - **预加载策略**：配置数据预加载行为
   - **缓存过期策略**：设置缓存数据的过期策略
   - **缓存替换算法**：选择缓存满时的替换算法（LRU、LFU等）

![缓存策略](../images/cache-strategy.png)

### 数据类型设置

为不同类型的数据配置缓存行为：

1. 在高级设置部分展开"数据类型设置"选项
2. 为每种数据类型配置缓存选项：
   - **记忆内容**：记忆文本内容的缓存设置
   - **记忆元数据**：记忆元数据的缓存设置
   - **图片**：图片文件的缓存设置
   - **视频**：视频文件的缓存设置
   - **搜索索引**：搜索索引的缓存设置
   - **用户设置**：用户设置的缓存设置

### 预加载设置

配置数据预加载行为：

1. 在高级设置部分展开"预加载设置"选项
2. 配置以下选项：
   - **启用预加载**：是否启用数据预加载
   - **预加载范围**：预加载的数据范围
   - **预加载触发条件**：何时触发预加载
   - **预加载优先级**：预加载任务的优先级
   - **网络条件限制**：基于网络条件的预加载规则

![预加载设置](../images/preload-settings.png)

## 存储管理

### 存储使用情况

查看当前的存储使用情况：

1. 在缓存设置页面找到"存储使用情况"部分
2. 查看以下信息：
   - 总存储空间
   - 已使用空间
   - 各类数据占用的空间（记忆、图片、视频等）
   - 缓存使用情况
   - 可用空间

![存储使用情况](../images/storage-usage.png)

### 清理缓存

手动清理缓存数据：

1. 在存储管理部分找到"清理缓存"选项
2. 选择要清理的缓存类型：
   - **全部缓存**：清理所有缓存数据
   - **媒体缓存**：仅清理媒体文件缓存
   - **搜索缓存**：仅清理搜索索引缓存
   - **过期缓存**：仅清理已过期的缓存
3. 点击"清理"按钮

### 存储优化

优化存储空间使用：

1. 在存储管理部分找到"存储优化"选项
2. 点击"优化存储"按钮
3. 系统会执行以下操作：
   - 压缩可压缩的数据
   - 移除冗余数据
   - 整理碎片化存储
   - 优化索引结构
4. 显示优化结果和节省的空间

![存储优化](../images/storage-optimization.png)

## 离线访问

### 离线模式设置

配置离线访问功能：

1. 在缓存设置页面找到"离线访问"部分
2. 配置以下选项：
   - **启用离线模式**：是否启用离线访问功能
   - **自动离线模式**：是否在检测到离线时自动切换
   - **离线内容范围**：离线可访问的内容范围
   - **离线编辑**：是否允许在离线状态下编辑记忆
   - **同步策略**：恢复在线后的同步策略

![离线模式设置](../images/offline-mode-settings.png)

### 手动离线准备

为离线使用准备数据：

1. 在离线访问部分找到"离线准备"选项
2. 点击"准备离线数据"按钮
3. 选择要离线可用的数据：
   - 特定分类
   - 特定标签
   - 时间范围
   - 是否包含媒体文件
4. 点击"开始准备"按钮
5. 系统会下载并缓存选定的数据

### 离线状态指示

了解当前的离线状态：

1. 在离线访问部分找到"离线状态"选项
2. 查看以下信息：
   - 当前网络状态
   - 离线模式是否激活
   - 可用的离线数据量
   - 上次同步时间
   - 预计可用时间

## 性能优化

### 性能设置

优化缓存性能：

1. 在缓存设置页面找到"性能优化"部分
2. 配置以下选项：
   - **内存使用限制**：限制应用的内存使用
   - **后台处理**：是否允许在后台处理数据
   - **延迟加载**：是否使用延迟加载技术
   - **并行处理**：是否启用并行数据处理
   - **硬件加速**：是否使用硬件加速

![性能设置](../images/performance-settings.png)

### 搜索性能

优化搜索性能：

1. 在性能优化部分找到"搜索性能"选项
2. 配置以下选项：
   - **搜索索引模式**：选择搜索索引的模式
   - **索引更新频率**：设置索引更新的频率
   - **搜索结果缓存**：是否缓存搜索结果
   - **模糊搜索性能**：调整模糊搜索的性能平衡

### 媒体处理

优化媒体文件处理：

1. 在性能优化部分找到"媒体处理"选项
2. 配置以下选项：
   - **图片处理质量**：调整图片处理的质量和性能平衡
   - **视频处理质量**：调整视频处理的质量和性能平衡
   - **缩略图生成**：配置缩略图生成策略
   - **媒体预加载**：配置媒体文件预加载行为

## 数据安全

### 缓存加密

保护缓存数据安全：

1. 在缓存设置页面找到"数据安全"部分
2. 配置以下选项：
   - **启用缓存加密**：是否加密缓存数据
   - **加密算法**：选择加密算法
   - **加密范围**：选择需要加密的数据类型
   - **密钥管理**：配置加密密钥的管理方式

![缓存加密](../images/cache-encryption.png)

### 数据隔离

配置数据隔离策略：

1. 在数据安全部分找到"数据隔离"选项
2. 配置以下选项：
   - **存储隔离**：是否使用隔离的存储区域
   - **上下文隔离**：是否在不同浏览上下文间隔离数据
   - **配置文件隔离**：是否在不同浏览器配置文件间隔离数据

### 自动清理

配置自动数据清理：

1. 在数据安全部分找到"自动清理"选项
2. 配置以下选项：
   - **启用自动清理**：是否启用自动数据清理
   - **清理频率**：设置自动清理的频率
   - **清理条件**：设置触发清理的条件
   - **清理范围**：设置要清理的数据范围
   - **安全删除**：是否使用安全删除方法

## 高级存储功能

### IndexedDB设置

配置IndexedDB存储：

1. 在缓存设置页面找到"高级存储"部分
2. 展开"IndexedDB设置"选项
3. 配置以下选项：
   - **数据库名称**：设置IndexedDB数据库名称
   - **数据库版本**：查看当前数据库版本
   - **对象存储配置**：配置对象存储结构
   - **索引设置**：配置数据索引
   - **事务模式**：设置事务处理模式

![IndexedDB设置](../images/indexeddb-settings.png)

### 存储迁移

迁移存储数据：

1. 在高级存储部分找到"存储迁移"选项
2. 选择迁移操作：
   - **从LocalStorage迁移**：将数据从LocalStorage迁移到IndexedDB
   - **导出数据库**：导出IndexedDB数据库
   - **导入数据库**：导入IndexedDB数据库
   - **重建数据库**：重建IndexedDB数据库结构
3. 点击"开始迁移"按钮

### 存储监控

监控存储性能和使用情况：

1. 在高级存储部分找到"存储监控"选项
2. 点击"启用监控"按钮
3. 查看以下监控指标：
   - 存储操作性能
   - 缓存命中率
   - 存储空间变化趋势
   - 异常操作记录
4. 配置监控选项和警报阈值

## 故障排除

### 缓存诊断

诊断缓存问题：

1. 在缓存设置页面找到"故障排除"部分
2. 点击"运行诊断"按钮
3. 系统会检查以下方面：
   - 存储可用性
   - 缓存完整性
   - 索引状态
   - 性能瓶颈
   - 异常错误
4. 查看诊断结果和建议的解决方案

![缓存诊断](../images/cache-diagnostics.png)

### 修复缓存

修复缓存问题：

1. 在故障排除部分找到"修复缓存"选项
2. 选择修复操作：
   - **验证缓存**：检查缓存完整性
   - **修复索引**：修复损坏的索引
   - **重建缓存**：完全重建缓存
   - **清除错误数据**：移除错误的缓存数据
3. 点击"开始修复"按钮

### 重置缓存

完全重置缓存：

1. 在故障排除部分找到"重置缓存"选项
2. 点击"重置缓存"按钮
3. 选择重置选项：
   - **保留用户数据**：仅重置缓存结构，保留数据
   - **完全重置**：删除所有缓存数据和结构
4. 确认重置操作

> **警告**：完全重置缓存会删除所有本地缓存的数据。如果没有同步到云存储，可能会导致数据丢失。

## 最佳实践

以下是一些缓存设置的最佳实践建议：

### 性能优化

- **增加内存缓存**：如果您的设备内存充足，增加内存缓存大小可以提高性能
- **启用预加载**：对于经常访问的数据，启用预加载可以提高响应速度
- **使用IndexedDB**：对于大量数据，使用IndexedDB比LocalStorage更高效

### 存储优化

- **定期清理缓存**：设置自动清理过期缓存，避免存储空间浪费
- **选择性缓存媒体**：媒体文件占用大量空间，可以选择只缓存重要的媒体文件
- **压缩数据**：启用数据压缩可以节省存储空间

### 离线使用

- **提前准备离线数据**：在预计离线使用前，手动准备离线数据
- **优先缓存文本内容**：文本内容占用空间小，可以优先缓存
- **设置合理的离线范围**：根据实际需求设置离线可用的数据范围

## 下一步

现在您已经了解了如何配置缓存设置，接下来可以学习：

- [外观设置](./appearance.md) - 如何自定义界面外观
- [用户设置](./user-settings.md) - 如何配置用户信息和账户
- [同步设置](./sync-settings.md) - 如何配置云存储和数据同步
