# 配置导入导出

拾光忆栈提供配置导入导出功能，让您可以在不同的浏览器、设备或平台之间轻松迁移您的设置，而无需重复输入相同的配置信息。

## 访问配置导入导出

1. 打开设置页面
2. 在左侧菜单中点击"配置导入导出"选项
3. 进入配置导入导出页面

![配置导入导出页面](../images/config-import-export.png)

## 导出配置

### 选择要导出的配置项

您可以选择要导出的配置项类型：

1. **用户信息** - 用户ID和个人资料
2. **云存储设置** - 存储提供商和相关配置
3. **同步设置** - 同步频率和冲突解决策略
4. **外观设置** - 主题、颜色和界面布局
5. **分类设置** - 自定义分类和默认分类
6. **缓存设置** - 缓存大小和过期策略
7. **安全设置** - 加密和自动锁定选项

### 导出配置文件

1. 选择要导出的配置项
2. 点击"导出配置"按钮
3. 选择保存位置
4. 配置将保存为JSON文件

### 敏感信息处理

默认情况下，导出的配置文件不包含敏感信息（如密钥、密码等）。但您可以选择包含这些信息：

1. 在导出选项中勾选“包含敏感信息（密钥、密码等）”
2. 阅读安全警告
3. 选择是否加密敏感信息
4. 导出配置

#### 密码加密保护

为了提高安全性，您可以选择使用密码加密敏感信息：

1. 勾选“使用密码加密敏感信息”
2. 输入加密密码
3. 导出配置

导入时，系统会自动检测到加密的敏感信息，并提示您输入解密密码。

> **安全警告**：即使使用密码加密，也请妥善保管导出的配置文件。请使用强密码并记住它，如果忘记密码，将无法恢复敏感信息。

## 导入配置

### 选择配置文件

1. 点击"选择配置文件"按钮
2. 选择之前导出的配置文件（JSON格式）
3. 系统将验证文件格式

### 确认导入

1. 查看导入确认对话框，显示将要导入的配置项
2. 确认导入操作
3. 系统将导入配置并应用设置

![导入确认对话框](../images/import-confirmation.png)

### 敏感信息处理

导入的配置文件可能包含不同类型的敏感信息：

#### 包含加密敏感信息的配置文件

如果导入的配置文件包含加密的敏感信息：

1. 系统会自动检测并显示密码输入对话框
2. 输入正确的密码解密敏感信息
3. 解密成功后，显示导入确认对话框
4. 确认导入后，所有设置（包括敏感信息）将直接生效

![密码解密对话框](../images/password-decrypt-dialog.png)

#### 包含非加密敏感信息的配置文件

如果导入的配置文件包含非加密的敏感信息：

1. 导入确认对话框会显示安全警告
2. 导入完成后，系统会提示您已导入敏感信息
3. 所有设置将直接生效，无需额外操作

#### 不包含敏感信息的配置文件

如果导入的配置文件不包含敏感信息：

1. 导入完成后，系统会提示您需要重新输入的敏感信息
2. 前往相应的设置页面（如云存储设置）
3. 输入缺失的敏感信息（如密钥、密码等）
4. 保存设置

![敏感信息提示](../images/sensitive-info-prompt.png)

## 配置项说明

### 用户信息

导出的用户信息包括：

- 用户ID
- 用户名
- 显示名称
- 电子邮件
- 个人资料信息

### 云存储设置

导出的云存储设置包括：

- 当前存储提供商
- 存储提供商配置（不包含密钥）
- 存储桶名称
- 区域和端点信息

### 同步设置

导出的同步设置包括：

- 自动同步选项
- 同步频率
- 冲突解决策略
- 压缩选项

### 外观设置

导出的外观设置包括：

- 暗色/亮色模式
- 主题选择
- 字体大小
- 主题颜色
- 紧凑模式选项

### 分类设置

导出的分类设置包括：

- 自定义分类列表
- 默认分类
- 分类图标和颜色

### 缓存设置

导出的缓存设置包括：

- 缓存大小限制
- 缓存存储位置
- 缓存过期策略
- 预加载选项

### 安全设置

导出的安全设置包括：

- 加密选项（不包含密钥）
- 加密算法
- 自动锁定选项
- 锁定超时时间

## 最佳实践

### 何时使用配置导入导出

配置导入导出功能在以下情况特别有用：

1. **跨设备使用** - 在多台电脑上使用拾光忆栈
2. **浏览器迁移** - 从一个浏览器迁移到另一个浏览器
3. **备份设置** - 在重新安装前备份您的设置
4. **共享配置** - 与团队成员共享标准配置

### 安全建议

使用配置导入导出功能时，请注意以下安全建议：

1. **谨慎导出敏感信息** - 除非必要，否则不要导出包含敏感信息的配置
2. **使用密码加密敏感信息** - 如果导出敏感信息，始终启用密码加密
3. **使用强密码** - 选择复杂且难以猜测的密码，不要使用常用密码
4. **妥善保管配置文件** - 配置文件可能包含个人信息，存放在安全的位置
5. **定期更新敏感信息** - 定期更改密钥和密码，尤其是在导出包含敏感信息的配置后
6. **验证导入来源** - 只导入来自可信来源的配置文件

## 故障排除

### 导出失败

如果导出失败，请尝试以下解决方案：

1. 刷新页面后重试
2. 选择较少的配置项导出
3. 检查浏览器存储权限

### 导入失败

如果导入失败，请尝试以下解决方案：

1. 确认文件格式正确（JSON格式）
2. 检查文件是否损坏
3. 尝试导入较新版本导出的配置文件

### 解密失败

如果解密敏感信息失败，请尝试以下解决方案：

1. 确认输入的密码正确，注意大小写和空格
2. 如果忘记密码，尝试导入不启用解密，然后手动输入敏感信息
3. 如果使用了多个密码，尝试其他可能的密码
4. 检查配置文件是否使用了兼容的加密版本

### 导入后设置未生效

如果导入后设置未生效，请尝试以下解决方案：

1. 刷新页面或重启浏览器
2. 检查是否已重新输入所有敏感信息
3. 手动验证各项设置

## 下一步

成功导入配置后，建议执行以下操作：

1. 验证云存储连接
2. 测试同步功能
3. 检查记忆数据是否正确显示
4. 根据需要调整导入的设置
