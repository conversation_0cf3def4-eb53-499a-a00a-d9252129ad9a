# 外观设置

外观设置允许您自定义拾光忆栈的视觉样式和界面布局，使其更符合您的个人偏好。本指南将详细介绍如何配置和管理这些设置。

## 主题设置

### 主题模式

选择拾光忆栈的基本主题模式：

1. 打开设置页面
2. 找到"外观设置"部分
3. 在"主题模式"选项中选择：
   - **亮色模式**：使用明亮的背景和深色文本
   - **暗色模式**：使用深色背景和浅色文本
   - **跟随系统**：自动匹配系统的暗色/亮色模式
   - **自动（时间）**：根据时间自动切换模式

![主题模式](../images/theme-mode.png)

### 主题颜色

自定义拾光忆栈的主题颜色：

1. 在外观设置部分找到"主题颜色"选项
2. 选择预设颜色方案或自定义颜色：
   - **预设方案**：选择内置的颜色方案
   - **自定义**：点击"自定义"按钮打开颜色选择器
3. 如果选择自定义，可以设置以下颜色：
   - **主色**：主要强调色
   - **次色**：次要强调色
   - **成功色**：表示成功的颜色
   - **警告色**：表示警告的颜色
   - **错误色**：表示错误的颜色
4. 点击"应用"按钮预览效果
5. 满意后点击"保存"按钮

![主题颜色](../images/theme-color.png)

### 自定义主题

创建和管理自定义主题：

1. 在外观设置部分找到"自定义主题"选项
2. 点击"创建新主题"按钮
3. 设置主题名称
4. 配置主题的所有视觉元素
5. 点击"保存主题"按钮
6. 已保存的主题会显示在主题列表中，可以：
   - 应用主题
   - 编辑主题
   - 删除主题
   - 导出/导入主题

## 字体设置

### 字体大小

调整拾光忆栈的字体大小：

1. 在外观设置部分找到"字体大小"选项
2. 使用滑块调整全局字体大小（12-20px）
3. 或者单独设置不同元素的字体大小：
   - **标题**：标题文本大小
   - **正文**：正文文本大小
   - **标签**：标签和按钮文本大小
   - **注释**：注释和辅助文本大小

![字体大小](../images/font-size.png)

### 字体家族

选择拾光忆栈使用的字体：

1. 在外观设置部分找到"字体家族"选项
2. 为不同元素选择字体：
   - **界面字体**：用于界面元素的字体
   - **内容字体**：用于记忆内容的字体
   - **代码字体**：用于代码块的等宽字体
3. 可以选择系统字体或自定义字体
4. 如果选择自定义字体，可以输入Web字体名称或上传字体文件

### 字体渲染

调整字体渲染选项：

1. 在外观设置部分找到"字体渲染"选项
2. 配置以下选项：
   - **字体平滑**：字体平滑方式（自动、灰度、次像素）
   - **字体粗细**：调整字体粗细
   - **字间距**：调整字符间距
   - **行高**：调整文本行高

![字体渲染](../images/font-rendering.png)

## 布局设置

### 布局模式

选择拾光忆栈的整体布局模式：

1. 在外观设置部分找到"布局模式"选项
2. 选择布局模式：
   - **标准布局**：默认的三栏布局
   - **紧凑布局**：更紧凑的布局，适合小屏幕
   - **宽屏布局**：优化利用宽屏空间的布局
   - **阅读模式**：专注于内容阅读的简化布局

![布局模式](../images/layout-mode.png)

### 侧边栏设置

自定义侧边栏行为：

1. 在外观设置部分找到"侧边栏设置"选项
2. 配置以下选项：
   - **侧边栏位置**：左侧或右侧
   - **侧边栏宽度**：调整侧边栏宽度
   - **自动隐藏**：是否自动隐藏侧边栏
   - **固定侧边栏**：是否固定侧边栏位置
   - **侧边栏内容**：选择显示在侧边栏中的内容

### 内容区域

自定义内容区域布局：

1. 在外观设置部分找到"内容区域"选项
2. 配置以下选项：
   - **内容宽度**：调整内容区域最大宽度
   - **内容对齐**：内容的对齐方式
   - **内容间距**：调整内容元素间的间距
   - **内容边距**：调整内容区域的边距

![内容区域](../images/content-area.png)

## 视图设置

### 记忆视图

自定义记忆列表的显示方式：

1. 在外观设置部分找到"记忆视图"选项
2. 配置以下选项：
   - **默认视图模式**：列表、卡片、杂志或时间线
   - **卡片大小**：调整卡片视图中的卡片大小
   - **卡片布局**：配置卡片内容的布局
   - **列表密度**：调整列表视图的密度
   - **显示缩略图**：是否显示记忆缩略图

![记忆视图](../images/memory-view.png)

### 详情视图

自定义记忆详情的显示方式：

1. 在外观设置部分找到"详情视图"选项
2. 配置以下选项：
   - **详情面板位置**：右侧、底部或全屏
   - **详情面板宽度**：调整详情面板宽度
   - **媒体显示大小**：调整图片和视频显示大小
   - **元数据显示**：配置元数据的显示方式
   - **相关记忆显示**：是否显示相关记忆

### 动画效果

配置界面动画效果：

1. 在外观设置部分找到"动画效果"选项
2. 配置以下选项：
   - **启用动画**：是否启用界面动画
   - **动画速度**：调整动画速度
   - **过渡效果**：选择过渡动画类型
   - **滚动动画**：配置滚动动画行为
   - **加载动画**：选择加载状态动画

![动画效果](../images/animation-effects.png)

## 颜色和图标

### 自定义颜色

为特定元素自定义颜色：

1. 在外观设置部分找到"自定义颜色"选项
2. 选择要自定义的元素类别：
   - **背景**：不同区域的背景颜色
   - **文本**：不同类型的文本颜色
   - **边框**：边框和分隔线颜色
   - **控件**：按钮和控件颜色
   - **状态**：不同状态的指示颜色
3. 为每个元素设置颜色
4. 点击"重置"可恢复默认颜色

![自定义颜色](../images/custom-colors.png)

### 图标设置

自定义图标显示：

1. 在外观设置部分找到"图标设置"选项
2. 配置以下选项：
   - **图标集**：选择图标集（默认、线性、填充等）
   - **图标大小**：调整图标大小
   - **图标颜色**：设置图标颜色是否跟随主题
   - **自定义图标**：为特定功能设置自定义图标

### 分类颜色

自定义分类的颜色和图标：

1. 在外观设置部分找到"分类颜色"选项
2. 为每个分类设置：
   - 颜色
   - 图标
   - 标签样式
3. 点击"自动生成"可根据分类名称自动生成颜色
4. 点击"重置"可恢复默认设置

![分类颜色](../images/category-colors.png)

## 背景设置

### 背景图片

为拾光忆栈设置背景图片：

1. 在外观设置部分找到"背景图片"选项
2. 选择背景图片来源：
   - **预设背景**：选择内置背景图片
   - **上传图片**：上传自定义背景图片
   - **网络图片**：使用网络图片URL
   - **纯色背景**：使用纯色背景
3. 配置背景显示选项：
   - **显示位置**：全局、仅主页或特定页面
   - **背景模糊**：调整背景模糊程度
   - **背景暗化**：调整背景暗化程度
   - **背景定位**：设置背景图片定位方式

![背景图片](../images/background-image.png)

### 背景效果

添加特殊背景效果：

1. 在外观设置部分找到"背景效果"选项
2. 选择背景效果类型：
   - **无**：不使用特殊效果
   - **渐变**：使用颜色渐变背景
   - **粒子**：使用动态粒子效果
   - **波纹**：使用波纹动画效果
3. 根据选择的效果类型配置详细选项

## 卡片和组件

### 卡片样式

自定义记忆卡片的样式：

1. 在外观设置部分找到"卡片样式"选项
2. 配置以下选项：
   - **卡片圆角**：调整卡片圆角大小
   - **卡片阴影**：设置卡片阴影效果
   - **卡片边框**：配置卡片边框样式
   - **卡片内边距**：调整卡片内容边距
   - **卡片背景**：设置卡片背景样式

![卡片样式](../images/card-style.png)

### 按钮样式

自定义按钮外观：

1. 在外观设置部分找到"按钮样式"选项
2. 配置以下选项：
   - **按钮形状**：圆角、方形或胶囊形
   - **按钮大小**：小、中、大或自定义
   - **按钮效果**：悬停和点击效果
   - **图标按钮**：图标按钮的显示方式
   - **按钮组**：按钮组的显示样式

### 表单元素

自定义表单元素外观：

1. 在外观设置部分找到"表单元素"选项
2. 配置以下选项：
   - **输入框样式**：输入框的外观
   - **选择器样式**：下拉选择器的外观
   - **复选框样式**：复选框的外观
   - **单选按钮样式**：单选按钮的外观
   - **开关样式**：开关控件的外观

![表单元素](../images/form-elements.png)

## 高级自定义

### 自定义CSS

添加自定义CSS样式：

1. 在外观设置部分找到"高级自定义"选项
2. 点击"自定义CSS"按钮
3. 在编辑器中输入自定义CSS代码
4. 点击"应用"按钮预览效果
5. 满意后点击"保存"按钮

> **注意**：自定义CSS可能会影响应用的稳定性和兼容性。请谨慎使用。

![自定义CSS](../images/custom-css.png)

### 导入导出主题

分享或备份您的主题设置：

1. 在高级自定义部分找到"导入导出"选项
2. 导出主题：
   - 点击"导出主题"按钮
   - 选择导出格式（JSON或主题包）
   - 保存导出文件
3. 导入主题：
   - 点击"导入主题"按钮
   - 选择主题文件
   - 选择导入选项（完全替换或合并）
   - 点击"导入"按钮

### 主题市场

探索和安装社区主题：

1. 在高级自定义部分找到"主题市场"选项
2. 点击"浏览主题"按钮
3. 在主题市场中：
   - 浏览可用主题
   - 预览主题效果
   - 安装喜欢的主题
   - 评价和分享主题

![主题市场](../images/theme-marketplace.png)

## 辅助功能设置

### 高对比度模式

启用高对比度显示：

1. 在外观设置部分找到"辅助功能"选项
2. 启用"高对比度模式"
3. 选择高对比度主题：
   - 黑底白字
   - 白底黑字
   - 黄底黑字
   - 自定义对比色

### 文本可读性

提高文本可读性：

1. 在辅助功能部分找到"文本可读性"选项
2. 配置以下选项：
   - **字体类型**：选择易读字体
   - **字符间距**：增加字符间距
   - **行间距**：增加行间距
   - **段落间距**：增加段落间距
   - **文本对齐**：设置文本对齐方式

![文本可读性](../images/text-readability.png)

### 动效减弱

减少界面动画效果：

1. 在辅助功能部分找到"动效减弱"选项
2. 配置以下选项：
   - **减少动画**：减少或禁用非必要动画
   - **减少过渡效果**：简化过渡效果
   - **静态内容**：禁用内容自动滚动
   - **闪烁控制**：减少界面闪烁效果

## 响应式设置

### 屏幕适配

配置不同屏幕尺寸的显示方式：

1. 在外观设置部分找到"响应式设置"选项
2. 为不同屏幕尺寸配置布局：
   - **大屏幕**（>1200px）
   - **中等屏幕**（768-1200px）
   - **小屏幕**（<768px）
3. 为每种屏幕尺寸设置：
   - 布局模式
   - 侧边栏行为
   - 内容显示方式
   - 字体大小调整

![屏幕适配](../images/screen-adaptation.png)

### 方向适配

配置不同屏幕方向的显示方式：

1. 在响应式设置部分找到"方向适配"选项
2. 为横向和纵向模式分别配置：
   - 布局调整
   - 内容排列
   - 控件位置
   - 导航方式

## 下一步

现在您已经了解了如何自定义拾光忆栈的外观，接下来可以学习：

- [用户设置](./user-settings.md) - 如何配置用户信息和账户
- [同步设置](./sync-settings.md) - 如何配置云存储和数据同步
- [缓存设置](./cache-settings.md) - 如何管理本地缓存和存储
