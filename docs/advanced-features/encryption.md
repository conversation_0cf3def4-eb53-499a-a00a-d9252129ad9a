# 数据加密

拾光忆栈提供强大的数据加密功能，保护您的记忆和个人信息安全。本指南将详细介绍如何配置和使用加密功能。

## 加密概述

拾光忆栈的加密系统设计基于以下原则：

1. **端到端加密**：数据在本地加密后再上传到云存储
2. **零知识设计**：加密密钥仅存储在本地，云服务无法访问您的原始数据
3. **标准加密算法**：使用业界标准的加密算法（AES-256-GCM）
4. **用户控制**：您完全控制加密密钥和加密设置

![加密概述](../images/encryption-overview.png)

## 加密的数据类型

拾光忆栈可以加密以下类型的数据：

- **记忆内容**：包括标题、正文和标签
- **元数据**：创建时间、修改时间等
- **媒体文件**：图片和视频
- **用户设置**：分类、标签和其他配置

## 启用加密

### 基本加密设置

1. 打开设置页面
2. 找到"加密设置"部分
3. 启用"加密存储"选项
4. 选择加密算法（默认为AES-256-GCM）
5. 设置加密密钥（见下文）
6. 点击"保存"按钮

![加密设置](../images/encryption-settings.png)

### 设置加密密钥

您可以通过以下方式设置加密密钥：

#### 方式一：使用密码

1. 在加密设置中选择"使用密码"
2. 输入一个强密码
3. 确认密码
4. 系统会基于您的密码生成加密密钥

> **重要**：请使用强密码（至少12个字符，包含大小写字母、数字和特殊符号）。

#### 方式二：使用随机生成的密钥

1. 在加密设置中选择"生成随机密钥"
2. 点击"生成密钥"按钮
3. 系统会生成一个随机的加密密钥
4. **务必备份此密钥**，丢失后无法恢复数据

#### 方式三：导入现有密钥

1. 在加密设置中选择"导入密钥"
2. 粘贴或上传您的密钥
3. 点击"验证密钥"确认密钥有效

### 密钥备份

无论使用哪种方式设置密钥，都强烈建议备份您的加密密钥：

1. 在加密设置中点击"导出密钥"按钮
2. 选择导出格式：
   - **文本**：复制密钥文本
   - **文件**：下载密钥文件
   - **QR码**：生成包含密钥的QR码
3. 将密钥安全地存储在离线位置

![密钥备份](../images/key-backup.png)

> **警告**：如果丢失加密密钥，您将永久失去对加密数据的访问权限。没有任何方法可以恢复丢失的密钥。

## 加密现有数据

启用加密后，您可以选择加密现有数据：

1. 在加密设置中点击"加密现有数据"按钮
2. 系统会显示加密进度
3. 加密完成后，您会收到通知

![加密现有数据](../images/encrypt-existing-data.png)

> **注意**：加密大量数据可能需要一些时间，请确保在此过程中不要关闭浏览器。

## 自动锁定

为了增强安全性，拾光忆栈提供自动锁定功能：

### 配置自动锁定

1. 在加密设置中找到"自动锁定"部分
2. 启用"自动锁定"选项
3. 设置锁定时间（如15分钟）
4. 保存设置

启用后，在指定的不活动时间后，拾光忆栈会自动锁定，要求重新输入密码才能访问数据。

### 手动锁定

您也可以随时手动锁定拾光忆栈：

1. 点击Chrome工具栏中的拾光忆栈图标
2. 在弹出窗口中点击"锁定"按钮

或者：

1. 在拾光忆栈主界面中点击顶部工具栏的"锁定"按钮

## 解锁访问

当拾光忆栈处于锁定状态时，您需要解锁才能访问数据：

1. 尝试访问拾光忆栈时，会显示解锁界面
2. 输入您的密码或提供加密密钥
3. 点击"解锁"按钮

![解锁界面](../images/unlock-screen.png)

## 更改加密设置

### 更改加密密钥

如果您需要更改加密密钥，请按照以下步骤操作：

1. 打开加密设置页面
2. 点击"更改密钥"按钮
3. 输入当前密码或密钥进行验证
4. 设置新的密码或密钥
5. 系统会使用新密钥重新加密所有数据
6. 完成后，旧密钥将不再有效

> **注意**：更改密钥需要重新加密所有数据，可能需要一些时间。

### 禁用加密

如果您想禁用加密，请按照以下步骤操作：

1. 打开加密设置页面
2. 关闭"加密存储"选项
3. 输入当前密码或密钥进行验证
4. 确认禁用加密
5. 系统会解密所有数据
6. 完成后，数据将以未加密形式存储

> **警告**：禁用加密后，您的数据在云存储中将不再受到加密保护。

## 加密技术详情

### 加密算法

拾光忆栈使用以下加密算法：

- **AES-256-GCM**：高级加密标准，256位密钥，GCM模式
- **PBKDF2**：用于从密码派生密钥
- **HKDF**：用于密钥派生和扩展

### 加密过程

数据加密过程如下：

1. 用户输入密码或提供密钥
2. 如果使用密码，通过PBKDF2（10万次迭代）派生主密钥
3. 为每个记忆生成唯一的IV（初始化向量）
4. 使用AES-256-GCM加密数据
5. 存储加密数据和IV，但不存储密钥

### 密钥管理

拾光忆栈采用以下密钥管理策略：

- 主密钥仅存储在内存中，从不写入磁盘
- 在自动锁定或手动锁定时，内存中的密钥会被安全擦除
- 可选择在本地安全存储派生密钥的哈希，用于验证密码是否正确

## 安全注意事项

### 密码强度

加密的安全性很大程度上取决于密码的强度：

- 使用至少12个字符的密码
- 包含大小写字母、数字和特殊符号
- 避免使用个人信息或常见词汇
- 考虑使用密码管理器生成和存储强密码

### 潜在风险

使用加密时应注意以下风险：

- **密钥丢失**：如果丢失密钥，数据将无法恢复
- **浏览器漏洞**：浏览器安全漏洞可能导致密钥泄露
- **恶意扩展**：其他恶意扩展可能尝试访问内存中的密钥
- **物理访问**：如果攻击者能物理访问您的设备，可能会尝试获取密钥

### 安全建议

为了最大限度地保护您的加密数据：

1. 定期备份加密密钥（存储在安全的离线位置）
2. 启用自动锁定功能
3. 在共享设备上使用后手动锁定
4. 定期更新浏览器和扩展
5. 谨慎安装其他浏览器扩展
6. 考虑使用专用浏览器配置文件

## 故障排除

如果您在使用加密功能时遇到问题，请尝试以下解决方案：

### 无法解锁

如果无法解锁拾光忆栈：

1. 确保输入了正确的密码或密钥
2. 检查大小写和特殊字符
3. 尝试使用备份的密钥文件
4. 如果仍然无法访问，可能需要重置（这将丢失所有加密数据）

### 加密过程中断

如果加密过程中断：

1. 重新打开拾光忆栈
2. 系统会检测到未完成的加密过程
3. 按照提示继续加密或重新开始

### 同步问题

如果加密数据同步出现问题：

1. 确保所有设备使用相同的加密密钥
2. 检查云存储配置
3. 尝试手动触发同步

## 下一步

现在您已经了解了如何使用数据加密功能，接下来可以学习：

- [多设备同步](./multi-device-sync.md) - 如何在多个设备上同步加密数据
- [数据导入导出](./import-export.md) - 如何导入导出加密数据
- [云存储配置](./cloud-storage.md) - 如何配置安全的云存储
