# 云存储配置

拾光忆栈支持多种云存储提供商，让您可以选择最适合自己的存储方式。本指南将详细介绍如何配置和使用不同的云存储服务。

## 支持的云存储提供商

拾光忆栈目前支持以下云存储提供商：

- **华为云OBS** - 华为云对象存储服务
- **MinIO** - 开源对象存储服务（计划支持）
- **腾讯云COS** - 腾讯云对象存储服务（计划支持）
- **Amazon S3** - 亚马逊简单存储服务（计划支持）
- **Google Drive** - 谷歌云端硬盘（计划支持）

![支持的云存储](../images/supported-cloud-storage.png)

## 为什么使用云存储

使用云存储有以下优势：

1. **多设备同步** - 在不同设备间同步您的记忆
2. **数据备份** - 防止数据丢失
3. **扩展存储空间** - 突破本地存储限制
4. **隐私控制** - 使用私有云存储保护数据隐私

## 华为云OBS配置

华为云对象存储服务(OBS)是拾光忆栈当前主要支持的云存储服务。

### 创建华为云账号

如果您还没有华为云账号，请按照以下步骤创建：

1. 访问[华为云官网](https://www.huaweicloud.com/)
2. 点击右上角的"注册"按钮
3. 按照提示完成注册流程
4. 登录您的华为云账号

### 创建OBS存储桶

1. 登录华为云控制台
2. 在服务列表中找到"对象存储服务 OBS"
3. 点击"创建桶"按钮
4. 填写以下信息：
   - **区域**：选择离您较近的区域
   - **桶名称**：输入一个唯一的名称
   - **存储类别**：标准存储
   - **桶策略**：私有
   - **版本控制**：启用（推荐）
5. 点击"创建"按钮

![创建OBS存储桶](../images/create-obs-bucket.png)

### 创建访问密钥

1. 在华为云控制台右上角点击您的用户名
2. 选择"我的凭证"
3. 在左侧菜单中选择"访问密钥"
4. 点击"新增访问密钥"按钮
5. 按照提示完成验证
6. 下载密钥文件并安全保存
7. 从密钥文件中获取Access Key ID和Secret Access Key

> **重要**：请妥善保管您的访问密钥，不要泄露给他人。

### 在拾光忆栈中配置OBS

1. 打开拾光忆栈设置页面
2. 点击左侧菜单的"同步设置"
3. 在"云存储配置"部分，选择"华为云OBS"
4. 填写以下信息：
   - **Access Key ID**：您的访问密钥ID
   - **Secret Access Key**：您的访问密钥密码
   - **区域**：您创建存储桶的区域（如cn-north-4）
   - **Endpoint**：OBS服务端点（如obs.cn-north-4.myhuaweicloud.com）
   - **Bucket名称**：您创建的存储桶名称
5. 点击"测试连接"验证配置是否正确
6. 配置正确后，点击"保存"按钮

![配置OBS](../images/configure-obs.png)

## MinIO配置（计划支持）

MinIO是一个高性能的开源对象存储服务，适合自托管私有云存储。

### 部署MinIO服务器

1. 按照[MinIO官方文档](https://min.io/docs/minio/linux/index.html)部署MinIO服务器
2. 创建一个专用的存储桶
3. 创建访问密钥

### 在拾光忆栈中配置MinIO

1. 打开拾光忆栈设置页面
2. 点击左侧菜单的"同步设置"
3. 在"云存储配置"部分，选择"MinIO"
4. 填写以下信息：
   - **服务器URL**：您的MinIO服务器地址
   - **Access Key**：您的MinIO访问密钥
   - **Secret Key**：您的MinIO密钥密码
   - **Bucket名称**：您创建的存储桶名称
   - **使用SSL**：是否使用SSL连接
5. 点击"测试连接"验证配置
6. 配置正确后，点击"保存"按钮

## 腾讯云COS配置（计划支持）

腾讯云对象存储(COS)是腾讯云提供的数据存储服务。

### 创建腾讯云账号

1. 访问[腾讯云官网](https://cloud.tencent.com/)
2. 注册并登录腾讯云账号

### 创建COS存储桶

1. 登录腾讯云控制台
2. 在产品列表中找到"对象存储"
3. 创建一个新的存储桶
4. 设置适当的访问权限

### 创建访问密钥

1. 在腾讯云控制台访问"访问密钥"页面
2. 创建新的访问密钥
3. 保存SecretId和SecretKey

### 在拾光忆栈中配置COS

1. 打开拾光忆栈设置页面
2. 点击左侧菜单的"同步设置"
3. 在"云存储配置"部分，选择"腾讯云COS"
4. 填写相关配置信息
5. 测试连接并保存

## Amazon S3配置（计划支持）

Amazon S3是亚马逊云提供的对象存储服务。

### 创建AWS账号

1. 访问[AWS官网](https://aws.amazon.com/)
2. 注册并登录AWS账号

### 创建S3存储桶

1. 登录AWS管理控制台
2. 找到S3服务
3. 创建一个新的存储桶
4. 配置适当的权限

### 创建IAM用户和访问密钥

1. 在AWS控制台找到IAM服务
2. 创建一个新的IAM用户
3. 分配S3访问权限
4. 创建访问密钥
5. 保存Access Key ID和Secret Access Key

### 在拾光忆栈中配置S3

1. 打开拾光忆栈设置页面
2. 点击左侧菜单的"同步设置"
3. 在"云存储配置"部分，选择"Amazon S3"
4. 填写相关配置信息
5. 测试连接并保存

## Google Drive配置（计划支持）

Google Drive是谷歌提供的云存储服务。

### 创建Google账号

1. 如果您还没有Google账号，请在[Google官网](https://accounts.google.com/)注册

### 在拾光忆栈中配置Google Drive

1. 打开拾光忆栈设置页面
2. 点击左侧菜单的"同步设置"
3. 在"云存储配置"部分，选择"Google Drive"
4. 点击"授权"按钮
5. 在弹出的窗口中登录您的Google账号并授权拾光忆栈访问
6. 授权成功后，点击"保存"按钮

## 切换云存储提供商

如果您想从一个云存储提供商切换到另一个，请按照以下步骤操作：

1. 首先备份您的数据（参见[数据导入导出](./import-export.md)）
2. 打开同步设置页面
3. 在"云存储配置"部分，选择新的存储提供商
4. 配置新提供商的设置
5. 测试连接成功后，点击"迁移数据"按钮
6. 在弹出的对话框中确认迁移操作
7. 等待数据迁移完成

> **注意**：数据迁移可能需要一些时间，取决于您的数据量和网络速度。

## 存储空间管理

### 查看存储使用情况

1. 打开同步设置页面
2. 在"存储使用情况"部分查看：
   - 总存储空间
   - 已使用空间
   - 各类数据占用的空间（记忆、图片、视频等）

![存储使用情况](../images/storage-usage.png)

### 清理存储空间

如果您的存储空间不足，可以尝试以下方法清理：

1. 删除不需要的记忆
2. 压缩或删除大型媒体文件
3. 清理旧版本的备份
4. 使用"存储清理"工具自动清理

## 数据安全

### 加密存储

为了保护您的数据安全，建议启用加密存储：

1. 打开设置页面
2. 找到"加密设置"部分
3. 启用"加密存储"选项
4. 设置加密密钥
5. 保存设置

详细信息请参阅[数据加密](./encryption.md)章节。

### 访问控制

确保您的云存储配置了适当的访问控制：

1. 使用私有存储桶，不要公开访问
2. 为云存储账号启用多因素认证
3. 定期更换访问密钥
4. 使用最小权限原则配置IAM策略

## 故障排除

如果您在配置或使用云存储时遇到问题，请尝试以下解决方案：

### 连接测试失败

如果连接测试失败，请检查：

1. 访问密钥是否正确
2. 存储桶名称是否正确
3. 区域和端点是否匹配
4. 网络连接是否正常
5. 防火墙是否阻止了连接

### 同步失败

如果同步失败，请检查：

1. 存储空间是否充足
2. 权限是否正确
3. 网络连接是否稳定
4. 查看错误日志了解具体原因

### 数据不一致

如果云端和本地数据不一致，请尝试：

1. 手动触发完全同步
2. 检查同步日志
3. 如有必要，使用"修复同步"功能

## 最佳实践

以下是使用云存储的一些最佳实践：

1. **选择合适的区域**：选择离您较近的存储区域，提高访问速度
2. **定期备份**：除了云存储同步，定期导出备份文件
3. **监控存储使用**：定期检查存储使用情况，避免超出限制
4. **安全第一**：使用强密码和多因素认证保护您的云存储账号
5. **测试恢复**：定期测试从云存储恢复数据的流程

## 下一步

现在您已经了解了如何配置和使用云存储，接下来可以学习：

- [多设备同步](./multi-device-sync.md) - 如何在多个设备上同步记忆
- [数据加密](./encryption.md) - 如何加密您的记忆数据
- [数据导入导出](./import-export.md) - 如何导入导出记忆数据
