# 快捷键

拾光忆栈提供丰富的键盘快捷键，帮助您提高工作效率。本指南将详细介绍所有可用的快捷键以及如何自定义它们。

## 全局快捷键

全局快捷键在任何网页中都可以使用，即使拾光忆栈的界面没有打开：

| 快捷键 | 功能 | 描述 |
|--------|------|------|
| `Ctrl+Shift+M` | 快速保存 | 保存选中的文本到拾光忆栈 |
| `Ctrl+Shift+E` | 打开拾光忆栈 | 打开拾光忆栈的主界面 |
| `Ctrl+Shift+F` | 搜索记忆 | 打开拾光忆栈的搜索界面 |

> **注意**：在Mac上，使用 `Command` 键代替 `Ctrl` 键。

## 浏览记忆页面快捷键

在浏览记忆页面中可以使用以下快捷键：

| 快捷键 | 功能 | 描述 |
|--------|------|------|
| `↑/↓` | 导航记忆 | 在列表视图中上下导航 |
| `←/→` | 导航记忆 | 在卡片视图中左右导航 |
| `Enter` | 查看详情 | 查看选中记忆的详情 |
| `Ctrl+E` | 编辑记忆 | 编辑选中的记忆 |
| `Ctrl+D` | 删除记忆 | 删除选中的记忆 |
| `Ctrl+S` | 收藏记忆 | 将选中记忆添加到收藏夹 |
| `Ctrl+F` | 搜索 | 聚焦到搜索框 |
| `Esc` | 取消选择 | 清除当前选择或关闭详情面板 |
| `Ctrl+1~9` | 分类快捷键 | 将记忆移动到对应编号的分类 |
| `Ctrl+T` | 添加标签 | 为选中记忆添加标签 |
| `Ctrl+C` | 复制内容 | 复制选中记忆的内容 |
| `Ctrl+Shift+C` | 复制链接 | 复制记忆的唯一链接 |
| `Ctrl+P` | 打印 | 打印选中的记忆 |
| `/` | 快速搜索 | 聚焦到搜索框 |
| `F5` | 刷新 | 刷新记忆列表 |

## 添加记忆页面快捷键

在添加或编辑记忆页面中可以使用以下快捷键：

| 快捷键 | 功能 | 描述 |
|--------|------|------|
| `Ctrl+S` | 保存 | 保存当前记忆 |
| `Ctrl+Enter` | 保存并关闭 | 保存记忆并关闭编辑器 |
| `Esc` | 取消 | 取消编辑并返回 |
| `Ctrl+B` | 粗体 | 将选中文本设为粗体 |
| `Ctrl+I` | 斜体 | 将选中文本设为斜体 |
| `Ctrl+U` | 下划线 | 为选中文本添加下划线 |
| `Ctrl+K` | 添加链接 | 为选中文本添加链接 |
| `Ctrl+Shift+I` | 插入图片 | 打开图片上传对话框 |
| `Ctrl+Shift+V` | 插入视频 | 打开视频上传对话框 |
| `Tab` | 自动完成 | 在标签输入时自动完成 |
| `Alt+1~9` | 选择分类 | 选择对应编号的分类 |

## 搜索页面快捷键

在搜索结果页面中可以使用以下快捷键：

| 快捷键 | 功能 | 描述 |
|--------|------|------|
| `↑/↓` | 导航结果 | 在搜索结果中上下导航 |
| `Enter` | 查看详情 | 查看选中结果的详情 |
| `Ctrl+Enter` | 高级搜索 | 打开高级搜索面板 |
| `Esc` | 清除搜索 | 清除当前搜索条件 |
| `Ctrl+S` | 保存搜索 | 保存当前搜索条件 |
| `Alt+1~9` | 过滤器快捷键 | 应用对应编号的过滤器 |

## 设置页面快捷键

在设置页面中可以使用以下快捷键：

| 快捷键 | 功能 | 描述 |
|--------|------|------|
| `Ctrl+S` | 保存设置 | 保存当前设置 |
| `Ctrl+Z` | 撤销更改 | 撤销最近的设置更改 |
| `Ctrl+R` | 重置设置 | 重置为默认设置 |
| `Ctrl+Tab` | 切换设置页 | 在设置页面间切换 |

## 自定义快捷键

拾光忆栈允许您自定义大部分快捷键：

### 通过Chrome扩展设置自定义全局快捷键

1. 打开Chrome扩展管理页面（chrome://extensions/）
2. 点击左侧的"键盘快捷键"
3. 找到拾光忆栈扩展
4. 为每个操作设置您喜欢的快捷键

![Chrome快捷键设置](../images/chrome-shortcuts.png)

### 通过拾光忆栈设置自定义内部快捷键

1. 打开拾光忆栈设置页面
2. 找到"快捷键设置"部分
3. 点击要修改的快捷键旁边的"编辑"按钮
4. 按下新的快捷键组合
5. 点击"保存"按钮

![自定义快捷键](../images/custom-shortcuts.png)

### 快捷键冲突

如果您设置的快捷键与其他扩展或浏览器快捷键冲突：

1. 系统会显示冲突警告
2. 您可以选择覆盖现有快捷键或选择其他组合
3. 某些浏览器保留的快捷键无法覆盖

## 快捷键提示

拾光忆栈提供快捷键提示功能，帮助您学习和记忆快捷键：

### 启用快捷键提示

1. 打开设置页面
2. 找到"快捷键设置"部分
3. 启用"显示快捷键提示"选项
4. 保存设置

启用后，当您将鼠标悬停在按钮或功能上时，会显示对应的快捷键。

### 快捷键列表

查看所有可用的快捷键：

1. 在拾光忆栈主界面中按下 `?` 键
2. 或者点击帮助菜单中的"快捷键列表"
3. 系统会显示一个包含所有快捷键的对话框

![快捷键列表](../images/shortcut-list.png)

## 触摸屏手势

对于触摸屏设备，拾光忆栈还提供了手势支持：

| 手势 | 功能 | 描述 |
|------|------|------|
| 左滑 | 下一页 | 在记忆列表中查看下一页 |
| 右滑 | 上一页 | 在记忆列表中查看上一页 |
| 双击 | 查看详情 | 查看选中记忆的详情 |
| 长按 | 上下文菜单 | 显示记忆的上下文菜单 |
| 两指缩放 | 调整大小 | 调整卡片或文本大小 |

## 命令面板

拾光忆栈提供命令面板功能，可以通过键盘快速访问各种功能：

1. 按下 `Ctrl+Shift+P` 打开命令面板
2. 开始输入命令名称
3. 使用 `↑/↓` 键选择命令
4. 按 `Enter` 执行选中的命令

![命令面板](../images/command-palette.png)

命令面板支持以下类型的命令：

- 导航命令（如"打开浏览记忆"、"打开设置"）
- 记忆操作（如"新建记忆"、"删除选中记忆"）
- 视图控制（如"切换视图模式"、"排序方式"）
- 工具命令（如"导出数据"、"同步记忆"）

## 快捷键备忘表

为方便参考，您可以下载或打印快捷键备忘表：

1. 在设置页面找到"快捷键设置"部分
2. 点击"下载快捷键备忘表"按钮
3. 选择下载格式（PDF或图片）

您也可以[点击这里](../downloads/shortcuts-cheatsheet.pdf)下载最新的快捷键备忘表。

## 辅助功能

拾光忆栈的快捷键设计考虑了辅助功能：

- 所有功能都可以通过键盘访问
- 支持屏幕阅读器
- 可以自定义快捷键以适应不同需求
- 提供高对比度模式

如果您有特殊的辅助功能需求，请联系我们的支持团队。

## 下一步

现在您已经了解了如何使用和自定义快捷键，接下来可以学习：

- [多设备同步](./multi-device-sync.md) - 如何在多个设备上同步记忆
- [数据导入导出](./import-export.md) - 如何导入导出记忆数据
- [云存储配置](./cloud-storage.md) - 如何配置云存储
