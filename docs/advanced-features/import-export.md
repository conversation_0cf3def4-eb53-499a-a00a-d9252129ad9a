# 数据导入导出

拾光忆栈提供灵活的数据导入导出功能，方便您备份、迁移和共享记忆数据。本指南将详细介绍如何使用这些功能。

## 数据导出

### 导出全部数据

导出所有记忆和设置：

1. 打开设置页面
2. 找到"数据管理"部分
3. 点击"导出全部数据"按钮
4. 选择导出格式：
   - **JSON**：完整数据，适合备份和迁移
   - **CSV**：表格格式，适合数据分析
   - **HTML**：网页格式，适合查看和分享
   - **Markdown**：文本格式，适合编辑和发布
5. 选择是否包含媒体文件（图片和视频）
6. 点击"导出"按钮
7. 选择保存位置

![导出全部数据](../images/export-all-data.png)

### 导出选定记忆

导出特定的记忆：

1. 在浏览记忆页面选择要导出的记忆
2. 点击"批量操作"按钮
3. 选择"导出选中记忆"
4. 选择导出格式和选项
5. 点击"导出"按钮

### 导出特定分类或标签

导出特定分类或标签下的记忆：

1. 在浏览记忆页面使用分类或标签过滤记忆
2. 点击工具栏中的"导出"按钮
3. 选择导出格式和选项
4. 点击"导出"按钮

### 导出格式详解

#### JSON格式

JSON格式包含完整的记忆数据和元数据，适合备份和迁移：

```json
{
  "version": "1.0",
  "export_date": "2023-06-01T12:00:00Z",
  "user_id": "user_123456789",
  "memories": [
    {
      "id": "mem_123456789",
      "title": "示例记忆",
      "content": "这是一条示例记忆的内容...",
      "created_at": "2023-05-15T10:30:00Z",
      "category": "2",
      "tags": ["示例", "测试"],
      "url": "https://example.com/page",
      "source": "Example Website",
      "images": [
        {
          "id": "img_123456789",
          "path": "/assets/images/mem_123456789/img_123456789.jpg",
          "thumbnail_path": "/assets/images/mem_123456789/thumbnails/img_123456789_thumb.jpg"
        }
      ],
      "videos": []
    }
  ],
  "categories": [
    {"id": "1", "name": "工作", "color": "#1890ff", "icon": "BulbOutlined"},
    {"id": "2", "name": "生活", "color": "#52c41a", "icon": "HeartOutlined"}
  ],
  "settings": {
    "defaultCategory": "2",
    "theme": "light",
    "fontSize": 14
  }
}
```

#### CSV格式

CSV格式将记忆数据组织为表格形式，适合导入电子表格软件进行分析：

```csv
id,title,content,created_at,category,tags,url,source
mem_123456789,示例记忆,这是一条示例记忆的内容...,2023-05-15T10:30:00Z,生活,"示例,测试",https://example.com/page,Example Website
```

#### HTML格式

HTML格式生成一个可在浏览器中查看的网页，保留格式和媒体内容：

```html
<!DOCTYPE html>
<html>
<head>
  <title>拾光忆栈导出 - 2023-06-01</title>
  <style>/* CSS样式 */</style>
</head>
<body>
  <h1>拾光忆栈记忆导出</h1>
  <div class="memory">
    <h2>示例记忆</h2>
    <div class="metadata">
      <span class="date">2023-05-15</span>
      <span class="category">生活</span>
      <span class="tags">示例, 测试</span>
    </div>
    <div class="content">
      这是一条示例记忆的内容...
    </div>
    <div class="images">
      <img src="data:image/jpeg;base64,..." alt="图片" />
    </div>
    <div class="source">
      来源: <a href="https://example.com/page">Example Website</a>
    </div>
  </div>
</body>
</html>
```

#### Markdown格式

Markdown格式生成纯文本文件，适合在各种编辑器中查看和编辑：

```markdown
# 拾光忆栈记忆导出

## 示例记忆

*2023-05-15 | 生活 | 标签: 示例, 测试*

这是一条示例记忆的内容...

![图片](data:image/jpeg;base64,...)

来源: [Example Website](https://example.com/page)

---
```

### 导出媒体文件

导出包含媒体文件的记忆：

1. 在导出对话框中启用"包含媒体文件"选项
2. 选择媒体文件处理方式：
   - **内联**：将媒体文件嵌入导出文件（适用于HTML和Markdown）
   - **单独文件**：将媒体文件保存为单独的文件
   - **压缩包**：将所有文件打包为ZIP压缩包

## 数据导入

### 导入拾光忆栈数据

导入之前导出的拾光忆栈数据：

1. 打开设置页面
2. 找到"数据管理"部分
3. 点击"导入数据"按钮
4. 选择导入文件（JSON格式）
5. 选择导入选项：
   - **合并**：保留现有数据，添加新数据
   - **替换**：删除现有数据，使用导入的数据
6. 点击"导入"按钮
7. 等待导入完成

![导入数据](../images/import-data.png)

### 导入其他格式

拾光忆栈支持导入多种格式的数据：

#### 导入CSV

1. 在导入对话框中选择"CSV"选项
2. 上传CSV文件
3. 映射CSV列到拾光忆栈字段
4. 点击"导入"按钮

#### 导入Markdown

1. 在导入对话框中选择"Markdown"选项
2. 上传Markdown文件或文件夹
3. 配置导入选项
4. 点击"导入"按钮

#### 导入HTML

1. 在导入对话框中选择"HTML"选项
2. 上传HTML文件
3. 配置导入选项
4. 点击"导入"按钮

### 批量导入

导入多个文件或文件夹：

1. 在导入对话框中选择"批量导入"选项
2. 选择要导入的文件或文件夹
3. 配置导入规则：
   - 文件命名规则
   - 文件夹结构处理
   - 默认分类和标签
4. 点击"开始导入"按钮

## 从其他服务导入

拾光忆栈支持从其他服务导入数据：

### 从Evernote导入

1. 在导入对话框中选择"从其他服务导入"
2. 选择"Evernote"
3. 上传Evernote导出的ENEX文件
4. 配置导入选项
5. 点击"导入"按钮

### 从OneNote导入

1. 在导入对话框中选择"从其他服务导入"
2. 选择"OneNote"
3. 上传OneNote导出的文件
4. 配置导入选项
5. 点击"导入"按钮

### 从印象笔记导入

1. 在导入对话框中选择"从其他服务导入"
2. 选择"印象笔记"
3. 上传印象笔记导出的文件
4. 配置导入选项
5. 点击"导入"按钮

## 自动备份

拾光忆栈可以自动备份您的数据：

### 配置自动备份

1. 打开设置页面
2. 找到"备份设置"部分
3. 启用"自动备份"选项
4. 配置备份频率：
   - 每天
   - 每周
   - 每月
5. 选择备份位置：
   - 云存储
   - 本地存储
6. 设置保留的备份数量
7. 保存设置

![自动备份设置](../images/auto-backup-settings.png)

### 管理备份

查看和管理现有备份：

1. 打开设置页面
2. 找到"备份设置"部分
3. 点击"管理备份"按钮
4. 在备份列表中，您可以：
   - 查看备份详情
   - 下载备份
   - 恢复备份
   - 删除备份

### 恢复备份

从备份恢复数据：

1. 在备份管理页面找到要恢复的备份
2. 点击"恢复"按钮
3. 选择恢复选项：
   - **完全恢复**：替换所有当前数据
   - **选择性恢复**：只恢复选定的数据
4. 确认恢复操作
5. 等待恢复完成

## 数据迁移

### 设备间迁移

将数据从一个设备迁移到另一个设备：

1. 在源设备上导出全部数据（JSON格式，包含媒体文件）
2. 在目标设备上安装拾光忆栈
3. 导入之前导出的数据
4. 配置相同的云存储设置以保持同步

### 账号迁移

将数据从一个用户账号迁移到另一个：

1. 在源账号中导出全部数据
2. 登出当前账号
3. 设置新账号
4. 导入之前导出的数据
5. 配置云存储设置

## 数据共享

### 导出共享版本

创建适合共享的导出版本：

1. 选择要共享的记忆
2. 点击"导出共享版本"按钮
3. 选择共享格式（HTML或Markdown推荐）
4. 配置共享选项：
   - 是否包含元数据
   - 是否包含来源信息
   - 是否包含媒体文件
5. 点击"导出"按钮

### 创建公开链接

创建可访问的公开链接（需要高级功能）：

1. 选择要共享的记忆
2. 点击"创建公开链接"按钮
3. 配置访问选项：
   - 访问密码
   - 过期时间
   - 访问权限
4. 点击"创建"按钮
5. 复制生成的链接进行共享

## 高级导入导出

### 命令行导入导出

对于高级用户，拾光忆栈提供命令行工具进行批量导入导出：

1. 安装拾光忆栈CLI工具
2. 使用命令行参数配置导入导出操作
3. 执行自动化脚本进行批处理

### API集成

拾光忆栈提供API用于与其他系统集成：

1. 获取API密钥
2. 使用API端点进行数据交换
3. 实现自定义集成和工作流

## 故障排除

如果您在导入导出过程中遇到问题，请尝试以下解决方案：

### 导出失败

如果导出失败，请检查：

1. 存储空间是否充足
2. 是否有特殊字符或格式问题
3. 文件大小是否超过限制
4. 尝试分批导出或不包含媒体文件

### 导入失败

如果导入失败，请检查：

1. 文件格式是否正确
2. 文件是否损坏
3. 数据结构是否兼容
4. 尝试使用"验证"功能检查文件有效性

### 媒体文件问题

如果媒体文件导入导出有问题：

1. 检查文件格式是否支持
2. 检查文件大小是否超过限制
3. 尝试单独处理媒体文件

## 最佳实践

以下是数据导入导出的一些最佳实践：

1. **定期备份**：设置自动备份，防止数据丢失
2. **使用JSON格式**：完整备份使用JSON格式，包含所有元数据
3. **分类备份**：按分类或时间段分批备份大量数据
4. **测试恢复**：定期测试从备份恢复的流程
5. **安全存储**：将备份文件存储在安全的位置，考虑加密敏感数据

## 下一步

现在您已经了解了如何导入导出数据，接下来可以学习：

- [多设备同步](./multi-device-sync.md) - 如何在多个设备上同步记忆
- [数据加密](./encryption.md) - 如何加密您的记忆数据
- [云存储配置](./cloud-storage.md) - 如何配置云存储
