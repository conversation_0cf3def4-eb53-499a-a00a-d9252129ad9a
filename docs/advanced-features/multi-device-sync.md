# 多设备同步

拾光忆栈支持在多个设备上同步您的记忆，确保您随时随地都能访问重要信息。本指南将详细介绍如何设置和使用多设备同步功能。

## 同步原理

拾光忆栈使用云存储作为中间媒介实现多设备同步：

1. 每个设备将记忆数据上传到云存储
2. 其他设备从云存储下载最新数据
3. 系统自动处理数据合并和冲突解决
4. 所有设备最终保持数据一致

![同步原理](../images/sync-principle.png)

## 前提条件

使用多设备同步功能需要满足以下条件：

1. 在所有设备上安装拾光忆栈扩展
2. 配置相同的云存储账户
3. 使用相同的用户ID（确保在所有设备上使用相同的用户信息）

## 设置同步

### 配置云存储

首先，您需要配置云存储：

1. 按照[云存储配置](./cloud-storage.md)指南设置您选择的云存储提供商
2. 确保测试连接成功

### 启用自动同步

配置自动同步设置：

1. 打开设置页面
2. 找到"同步设置"部分或点击左侧菜单的"同步设置"
3. 启用"自动同步"选项
4. 配置同步频率：
   - **实时同步**：数据变更时立即同步
   - **定时同步**：按设定的时间间隔同步
   - **手动同步**：仅在手动触发时同步
5. 配置同步范围：
   - **全部数据**：同步所有记忆和设置
   - **仅记忆**：只同步记忆数据，不同步设置
   - **自定义**：选择要同步的特定数据类型
6. 保存设置

![同步设置](../images/sync-settings.png)

### 首次同步

设置完成后，执行首次同步：

1. 在同步设置页面点击"立即同步"按钮
2. 如果是首次使用，系统会询问您是否要：
   - **上传本地数据**：将当前设备的数据上传到云端
   - **下载云端数据**：从云端下载数据到当前设备
   - **合并数据**：合并本地和云端数据
3. 选择适合您情况的选项
4. 等待同步完成

> **提示**：首次同步时，如果您已经在其他设备上使用拾光忆栈，建议选择"下载云端数据"或"合并数据"。

## 使用同步功能

### 手动同步

您可以随时手动触发同步：

1. 点击Chrome工具栏中的拾光忆栈图标
2. 在弹出窗口中点击"同步"按钮

或者：

1. 打开拾光忆栈主界面
2. 点击顶部工具栏中的"同步"按钮

### 查看同步状态

您可以查看当前的同步状态：

1. 打开同步设置页面
2. 查看"同步状态"部分：
   - 上次同步时间
   - 同步状态（成功/失败）
   - 同步统计（上传/下载的数据量）

![同步状态](../images/sync-status.png)

### 同步历史

查看同步历史记录：

1. 在同步设置页面点击"查看同步历史"
2. 系统会显示最近的同步操作列表
3. 每条记录包含：
   - 同步时间
   - 同步类型（自动/手动）
   - 同步结果
   - 同步的数据量
   - 可能的错误信息

![同步历史](../images/sync-history.png)

## 冲突解决

当多个设备同时修改相同的记忆时，可能会发生冲突。拾光忆栈提供了自动和手动的冲突解决机制：

### 自动冲突解决

默认情况下，系统会使用以下规则自动解决冲突：

- **最新优先**：保留最后修改的版本
- **非空优先**：如果一个版本为空，保留非空版本
- **合并不冲突的字段**：对于不冲突的字段（如标签），尝试合并

### 手动冲突解决

对于无法自动解决的冲突，系统会提示您手动解决：

1. 同步过程中检测到冲突时，会显示冲突解决对话框
2. 对话框显示冲突的记忆和不同版本的差异
3. 您可以选择：
   - **保留本地版本**：使用当前设备的版本
   - **使用云端版本**：使用云存储中的版本
   - **手动合并**：自定义合并两个版本
4. 如果选择手动合并，系统会打开合并编辑器
5. 在合并编辑器中，您可以逐字段选择要保留的内容

![冲突解决](../images/conflict-resolution.png)

### 冲突解决策略

您可以在设置中配置默认的冲突解决策略：

1. 打开同步设置页面
2. 找到"冲突解决"部分
3. 选择默认策略：
   - **最新优先**：总是使用最后修改的版本
   - **本地优先**：优先使用本地版本
   - **云端优先**：优先使用云端版本
   - **总是询问**：每次冲突都手动解决
4. 保存设置

## 设备管理

拾光忆栈允许您管理已同步的设备：

### 查看已同步设备

1. 打开同步设置页面
2. 找到"设备管理"部分
3. 查看所有已同步的设备列表
4. 每个设备显示以下信息：
   - 设备名称
   - 设备类型（桌面/移动）
   - 最后活跃时间
   - 操作系统和浏览器信息

![设备管理](../images/device-management.png)

### 重命名设备

为了更容易识别不同设备，您可以为它们指定友好名称：

1. 在设备列表中找到要重命名的设备
2. 点击设备名称旁边的"编辑"图标
3. 输入新名称
4. 点击"保存"

### 移除设备

如果您不再使用某个设备，可以将其从同步列表中移除：

1. 在设备列表中找到要移除的设备
2. 点击"移除"按钮
3. 确认移除操作

> **注意**：移除设备不会删除该设备上的数据，只会将其从同步列表中移除。如果该设备再次连接，它将被视为新设备。

## 高级同步功能

### 选择性同步

您可以选择只同步特定类型的记忆：

1. 打开同步设置页面
2. 找到"选择性同步"部分
3. 配置要同步的内容：
   - 按分类选择
   - 按时间范围选择
   - 按标签选择
   - 是否包含媒体文件

![选择性同步](../images/selective-sync.png)

### 增量同步

为了提高同步效率，拾光忆栈使用增量同步技术：

- 只传输变更的数据，而不是整个数据库
- 使用分块存储架构，每次只同步变更的块
- 支持断点续传，中断后可以从断点继续

### 同步带宽限制

如果您担心同步占用过多网络带宽，可以设置限制：

1. 打开同步设置页面
2. 找到"带宽设置"部分
3. 配置上传和下载速度限制
4. 设置是否在使用移动网络时限制同步

## 故障排除

如果您在使用多设备同步时遇到问题，请尝试以下解决方案：

### 同步失败

如果同步失败，请检查：

1. 网络连接是否正常
2. 云存储配置是否正确
3. 存储空间是否充足
4. 查看错误日志了解具体原因

尝试的解决方法：

1. 重新测试云存储连接
2. 重启浏览器
3. 尝试手动同步
4. 检查云存储服务状态

### 数据不一致

如果不同设备上的数据不一致：

1. 在所有设备上手动触发同步
2. 检查是否有未解决的冲突
3. 确认所有设备使用的是相同的用户ID
4. 查看同步历史了解可能的问题

### 同步速度慢

如果同步速度很慢：

1. 检查网络连接质量
2. 减少同步的数据量（使用选择性同步）
3. 尝试在网络良好时进行同步
4. 考虑更换地理位置更近的云存储区域

## 最佳实践

以下是使用多设备同步的一些最佳实践：

1. **定期同步**：设置适当的自动同步频率，确保数据及时更新
2. **备份重要数据**：在进行大规模同步操作前备份数据
3. **合理命名设备**：为每个设备设置有意义的名称，方便识别
4. **避免同时编辑**：尽量避免在多个设备上同时编辑相同的记忆
5. **检查同步状态**：定期查看同步状态，确保同步正常

## 下一步

现在您已经了解了如何使用多设备同步功能，接下来可以学习：

- [云存储配置](./cloud-storage.md) - 如何配置不同的云存储提供商
- [数据加密](./encryption.md) - 如何加密您的记忆数据
- [数据导入导出](./import-export.md) - 如何导入导出记忆数据
