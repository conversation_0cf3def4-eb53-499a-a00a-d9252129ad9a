# Chrome插件权限申请说明

## 权限申请列表
拾光忆栈插件申请以下权限：
- `storage` - 本地数据存储
- `tabs` - 标签页信息获取  
- `contextMenus` - 右键菜单功能

---

## 权限使用说明

### 1. "storage" 权限
**用途**: 在用户设备上存储插件配置和记忆数据

**具体使用场景**:
- 保存用户设置（云存储配置、分类设置、外观偏好等）
- 缓存记忆数据以支持离线访问和快速加载
- 存储设备标识用于多设备同步
- 保存用户创建的分类和标签
- 自动备份功能的数据存储

**代码位置**: `src/services/StorageService.js`, `src/content/index.js`
**API调用**: `chrome.storage.local.get()`, `chrome.storage.local.set()`

### 2. "tabs" 权限  
**用途**: 获取当前网页基本信息和管理插件页面

**具体使用场景**:
- 用户保存记忆时自动获取当前网页URL作为来源信息
- 打开插件的各个功能页面（浏览记忆、设置等）
- 向当前标签页发送消息以支持右键菜单功能

**代码位置**: `src/background/index.js`, `src/popup/Popup.js`, `src/content/index.js`
**API调用**: `chrome.tabs.create()`, `chrome.tabs.sendMessage()`

**重要说明**: 仅读取页面URL和标题等基本信息，不访问页面内容，不收集浏览历史

### 3. "contextMenus" 权限
**用途**: 在网页右键菜单中添加快速保存选项

**具体使用场景**:
- 在用户选中文本时显示"保存到拾光忆栈"菜单项
- 处理右键菜单点击事件，触发快速保存功能
- 提供最便捷的内容保存方式

**代码位置**: `src/background/index.js`
**API调用**: `chrome.contextMenus.create()`, `chrome.contextMenus.onClicked`

---

## 隐私保护承诺

### 数据处理原则
- **本地优先**: 所有数据主要存储在用户本地设备
- **用户控制**: 用户完全控制自己的数据，可随时导出或删除
- **最小收集**: 仅处理用户主动保存的内容，不收集其他信息
- **透明开源**: 代码完全开源，可在GitHub查看实现细节

### 具体保护措施
- 支持私有云存储，数据不经过第三方服务器
- 敏感配置信息支持加密存储
- 不收集用户浏览历史或个人信息
- 提供完整的数据导出和清理功能

---

## 权限必要性说明

每个权限都是插件核心功能所必需的：

1. **storage权限**: 没有此权限，插件无法保存用户设置和记忆数据，核心功能完全无法使用
2. **tabs权限**: 没有此权限，无法自动获取网页来源信息，无法打开插件页面，用户体验严重受损
3. **contextMenus权限**: 没有此权限，用户无法通过右键菜单快速保存内容，这是插件最重要的交互方式

移除任何一个权限都会导致插件功能严重缺失或完全无法使用。

---

## 开源验证

- **GitHub仓库**: https://github.com/AIPlayZone/MemoryKeeper
- **代码审查**: 所有权限使用都有对应的代码实现，可公开审查
- **社区监督**: 接受开源社区的监督和反馈

我们承诺严格按照声明的用途使用这些权限，绝不进行任何损害用户隐私或安全的操作。
